# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
---
default_stages: [pre-commit, pre-push]
default_language_version:
  python: python3
  node: 22.16.0
  golang: 1.24.0
minimum_pre_commit_version: '3.2.0'
exclude: ^.*/.*_vendor/
repos:
  - repo: meta
    hooks:
      - id: identity
        name: Print checked files
        description: Print input to the static check hooks for troubleshooting
      - id: check-hooks-apply
        name: Check if all hooks apply to the repository
  - repo: https://github.com/thlorenz/doctoc.git
    rev: v2.2.0
    hooks:
      - id: doctoc
        name: Add TOC for Markdown and RST files
        files:
          (?x)
          ^README\.md$|
          ^UPDATING.*\.md$|
          ^chart/UPDATING.*\.md$|
          ^dev/.*\.md$|
          ^dev/.*\.rst$|
          ^docs/README\.md$|
          ^\.github/.*\.md$|
          ^airflow-core/tests/system/README\.md$
        args:
          - "--maxlevel"
          - "2"
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.5
    hooks:
      - id: insert-license
        name: Add license for all SQL files
        files: \.sql$
        exclude: |
          (?x)
          ^\.github/
        args:
          - --comment-style
          - "/*||*/"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all RST files
        exclude: ^\.github/.*$|newsfragments/.*\.rst$
        args:
          - --comment-style
          - "||"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.rst
          - --fuzzy-match-generates-todo
        files: \.rst$
      - id: insert-license
        name: Add license for CSS/JS/JSX/PUML/TS/TSX
        files: \.(css|jsx?|puml|tsx?)$
        exclude: ^\.github/.*$|ui/openapi-gen/|.*/dist/.*
        args:
          - --comment-style
          - "/*!| *| */"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all Shell files
        exclude: ^\.github/.*$|^dev/breeze/autocomplete/.*$
        files: \.bash$|\.sh$
        args:
          - --comment-style
          - "|#|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all toml files
        exclude: ^\.github/.*$|^dev/breeze/autocomplete/.*$
        files: \.toml$
        args:
          - --comment-style
          - "|#|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all Python files
        exclude: ^\.github/.*$|^.*/_vendor/.*$|^airflow-ctl/.*/.*generated\.py$
        files: \.py$|\.pyi$
        args:
          - --comment-style
          - "|#|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all XML files
        exclude: ^\.github/.*$
        files: \.xml$
        args:
          - --comment-style
          - "<!--||-->"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all Helm template files
        files: ^chart/templates/.*
        args:
          - --comment-style
          - "{{/*||*/}}"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all YAML files except Helm templates
        exclude: >
          (?x)
          ^\.github/.*$|^chart/templates/.*|
          .*reproducible_build\.yaml$|
          ^.*/v2.*\.yaml$|
          ^.*/openapi/_private_ui.*\.yaml$|
          ^.*/pnpm-lock\.yaml$|
          .*-generated\.yaml$
        types: [yaml]
        files: \.ya?ml$
        args:
          - --comment-style
          - "|#|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all Markdown files
        files: \.md$
        exclude: PROVIDER_CHANGES.*\.md$
        args:
          - --comment-style
          - "<!--|| -->"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
      - id: insert-license
        name: Add license for all other files
        exclude: ^\.github/.*$
        args:
          - --comment-style
          - "|#|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --fuzzy-match-generates-todo
        files: >
          \.cfg$|\.conf$|\.ini$|\.ldif$|\.properties$|\.service$|\.tf$|Dockerfile.*$
  # https://github.com/Lucas-C/pre-commit-hooks/pull/103
  - repo: https://github.com/ashb/pre-commit-hooks
    rev: 36ddb15cd7a607003b06bf11c28045c91ea97dc0
    hooks:
      - id: insert-license
        name: Add license for all Go files
        types: [go]
        args:
          - --comment-style
          - "|//|"
          - --license-filepath
          - scripts/ci/license-templates/LICENSE.txt
          - --insert-license-after-regex
          # We need this 'generated by' line at the top for `golines` to not format it
          - '// Code generated by .*'
  - repo: local
    hooks:
      - id: check-min-python-version
        name: Check minimum Python version
        entry: ./scripts/ci/pre_commit/check_min_python_version.py
        language: python
        additional_dependencies: ['rich>=12.4.4']
        require_serial: true
      - id: check-imports-in-providers
        name: Check imports in providers
        entry: ./scripts/ci/pre_commit/check_imports_in_providers.py
        language: python
        additional_dependencies: ['rich>=12.4.4', 'ruff==0.11.2']
        files: ^providers/.*/src/airflow/providers/.*\.py$
        require_serial: true
      - id: update-black-version
        name: Update black versions everywhere (manual)
        entry: ./scripts/ci/pre_commit/update_black_version.py
        stages: ['manual']
        language: python
        files: ^\.pre-commit-config\.yaml$
        additional_dependencies: ['pyyaml>=6.0.2']
        pass_filenames: false
        require_serial: true
      - id: update-installers-and-pre-commit
        name: Update installers and pre-commit to latest (manual)
        entry: ./scripts/ci/pre_commit/update_installers_and_pre_commit.py
        stages: ['manual']
        language: python
        files: ^\.pre-commit-config\.yaml$|^scripts/ci/pre_commit/update_installers_and_pre_commit\.py$
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['pyyaml>=6.0.2', 'rich>=12.4.4', 'requests>=2.31.0']
      - id: update-chart-dependencies
        name: Update chart dependencies to latest (manual)
        entry: ./scripts/ci/pre_commit/update_chart_dependencies.py
        stages: ['manual']
        language: python
        additional_dependencies: ['pyyaml>=6.0.2', 'rich>=12.4.4', 'requests>=2.31.0']
        files: ^\.pre-commit-config\.yaml$|^scripts/ci/pre_commit/update_build_dependencies\.py$
        pass_filenames: false
        require_serial: true
      - id: check-taskinstance-tis-attrs
        name: Check that TI and TIS have the same attributes
        entry: ./scripts/ci/pre_commit/check_ti_vs_tis_attributes.py
        language: python
        additional_dependencies: ['rich>=12.4.4']
        files: ^airflow-core/src/airflow/models/taskinstance\.py$|^airflow-core/src/airflow/models/taskinstancehistory\.py$
        pass_filenames: false
        require_serial: true
      - id: check-deferrable-default
        name: Check and fix default value of default_deferrable
        language: python
        entry: ./scripts/ci/pre_commit/check_deferrable_default.py
        pass_filenames: false
        additional_dependencies: ['libcst>=1.1.0']
        files: ^(providers/.*/)?airflow/.*/(sensors|operators)/.*\.py$
  - repo: https://github.com/asottile/blacken-docs
    rev: 1.19.1
    hooks:
      - id: blacken-docs
        name: Run black on docs
        args:
          - --line-length=110
          - --target-version=py39
          - --target-version=py310
          - --target-version=py311
          - --target-version=py312
        alias: blacken-docs
        additional_dependencies: ['black==24.10.0']
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-merge-conflict
        name: Check that merge conflicts are not being committed
      - id: debug-statements
        name: Detect accidentally committed debug statements
      - id: check-builtin-literals
        name: Require literal syntax when initializing builtins
      - id: detect-private-key
        name: Detect if private key is added to the repository
        exclude: ^providers/ssh/docs/connections/ssh\.rst$
      - id: end-of-file-fixer
        name: Make sure that there is an empty line at the end
        exclude: ^airflow-core/docs/img/.*\.dot|^airflow-core/docs/img/.*\.sha256|.*/dist/.*|LICENSES-ui\.txt$
      - id: mixed-line-ending
        name: Detect if mixed line ending is used (\r vs. \r\n)
      - id: check-executables-have-shebangs
        name: Check that executables have shebang
      - id: check-xml
        name: Check XML files with xmllint
        exclude: >
          (?x)
          ^scripts/ci/docker-compose/gremlin/.
      - id: trailing-whitespace
        name: Remove trailing whitespace at end of line
        exclude: ^airflow-core/docs/img/.*\.dot$|^dev/breeze/doc/images/output.*$
      - id: fix-encoding-pragma
        name: Remove encoding header from Python files
        args:
          - --remove
      - id: pretty-format-json
        name: Format JSON files
        args:
          - --autofix
          - --no-sort-keys
          - --indent
          - "4"
        files: ^chart/values\.schema\.json$|^chart/values_schema\.schema\.json$
        pass_filenames: true
  - repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.10.0
    hooks:
      - id: rst-backticks
        name: Check if RST files use double backticks for code
      - id: python-no-log-warn
        name: Check if there are no deprecate log warn
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.37.1
    hooks:
      - id: yamllint
        name: Check YAML files with yamllint
        entry: yamllint -c yamllint-config.yml --strict
        types: [yaml]
        exclude: >
          (?x)
          ^.*airflow\.template\.yaml$|
          ^.*init_git_sync\.template\.yaml$|
          ^chart/(?:templates|files)/.*\.yaml$|
          ^helm-tests/tests/chart_utils/keda.sh_scaledobjects\.yaml$|
          .*/v1.*\.yaml$|
          ^.*openapi.*\.yaml$|
          ^\.pre-commit-config\.yaml$|
          ^.*reproducible_build\.yaml$|
          ^.*pnpm-lock\.yaml$
  - repo: https://github.com/ikamensh/flynt
    rev: '1.0.1'
    hooks:
      - id: flynt
        name: Run flynt string format converter for Python
        args:
         # If flynt detects too long text it ignores it. So we set a very large limit to make it easy
         # to split the text by hand. Too long lines are detected by flake8 (below),
         # so the user is informed to take action.
         - --line-length
         - '99999'
  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
      - id: codespell
        name: Run codespell
        description: Run codespell to check for common misspellings in files
        entry: bash -c 'echo "If you think that this failure is an error, consider adding the word(s)
          to the codespell dictionary at docs/spelling_wordlist.txt.
          The word(s) should be in lowercase." && exec codespell "$@"' --
        language: python
        types: [text]
        exclude: >
          (?x)
          material-icons\.css$|
          ^images/.*$|
          ^RELEASE_NOTES\.txt$|
          ^.*package-lock\.json$|
          ^.*/kinglear\.txt$|
          ^.*pnpm-lock\.yaml$|
          .*/dist/.*|
          ^airflow-core/src/airflow/ui/src/i18n/locales/de/
        args:
          - --ignore-words=docs/spelling_wordlist.txt
          - --skip=providers/.*/src/airflow/providers/*/*.rst,providers/*/docs/changelog.rst,docs/*/commits.rst,providers/*/docs/commits.rst,providers/*/*/docs/commits.rst,docs/apache-airflow/tutorial/pipeline_example.csv,*.min.js,*.lock,INTHEWILD.md,*.svg
          - --exclude-file=.codespellignorelines
  - repo: https://github.com/woodruffw/zizmor-pre-commit
    rev: v1.7.0
    hooks:
      - id: zizmor
        name: Run zizmor to check for github workflow syntax errors
        types: [yaml]
        files: ^\.github/workflows/.*$|^\.github/actions/.*$
        require_serial: true
        entry: zizmor
  - repo: local
    # Note that this is the 2nd "local" repo group in the .pre-commit-config.yaml file. This is because
    # we try to minimise the number of passes that must happen in order to apply some of the changes
    # done by pre-commits. Some of the pre-commits not only check for errors but also fix them. This means
    # that output from an earlier pre-commit becomes input to another pre-commit. Splitting the local
    # scripts of our and adding some other non-local pre-commit in-between allows us to handle such
    # changes quickly - especially when we want the early modifications from the first local group
    # to be applied before the non-local pre-commits are run
    hooks:
      - id: update-providers-dependencies
        name: Update dependencies for providers
        entry: ./scripts/ci/pre_commit/update_providers_dependencies.py
        language: python
        always_run: true
        pass_filenames: false
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2', 'tomli>=2.0.1']
      - id: validate-operators-init
        name: No templated field logic checks in operator __init__
        description: Prevent templated field logic checks in operators' __init__
        language: python
        entry: ./scripts/ci/pre_commit/validate_operators_init.py
        pass_filenames: true
        files: ^providers/.*/src/airflow/providers/.*/(operators|transfers|sensors)/.*\.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: update-providers-build-files
        name: Update providers build files
        entry: ./scripts/ci/pre_commit/update_providers_build_files.py
        language: python
        pass_filenames: true
        files: |
          (?x)
          ^providers/[^\/]*/src/airflow/providers/[^\/]*/__init__\.py$|
          ^providers/[^\/]*/[^\/]*/src/airflow/providers/[^\/]*/[^\/]*/__init__\.py$|
          ^providers/.*/pyproject\.toml$|
          ^providers/.*/provider\.yaml$|
          ^airflow_breeze/templates/PROVIDER__INIT__PY_TEMPLATE\.py\.jinja2$|
          ^airflow_breeze/templates/get_provider_info_TEMPLATE\.py\.jinja2$|
          ^airflow_breeze/templates/PROVIDER_README_TEMPLATE\.rst\.jinja2$
        additional_dependencies: ['rich>=12.4.4','requests>=2.31.0']
        require_serial: true
      - id: ruff
        name: Run 'ruff' for extremely fast Python linting
        description: "Run 'ruff' for extremely fast Python linting"
        entry: ruff check --force-exclude
        language: python
        types_or: [python, pyi]
        args: [--fix]
        require_serial: true
        additional_dependencies: ['ruff==0.11.2']
        exclude: ^airflow-core/tests/unit/dags/test_imports\.py$|^performance/tests/test_.*\.py$
      - id: ruff-format
        name: Run 'ruff format'
        description: "Run 'ruff format' for extremely fast Python formatting"
        entry: ./scripts/ci/pre_commit/ruff_format.py
        language: python
        types_or: [python, pyi]
        args: []
        require_serial: true
        additional_dependencies: ['ruff==0.11.2']
        exclude: ^airflow-core/tests/unit/dags/test_imports\.py$
      - id: replace-bad-characters
        name: Replace bad characters
        entry: ./scripts/ci/pre_commit/replace_bad_characters.py
        language: python
        types: [file, text]
        exclude: ^clients/gen/go\.sh$|^\.gitmodules$|.*/dist/.*|\.go$|/go\.(mod|sum)$
        additional_dependencies: ['rich>=12.4.4']
      - id: lint-dockerfile
        name: Lint Dockerfile
        language: python
        entry: ./scripts/ci/pre_commit/lint_dockerfile.py
        files: Dockerfile.*$
        pass_filenames: true
        require_serial: true
      - id: check-airflow-k8s-not-used
        name: Check airflow.kubernetes imports are not used
        language: python
        files: ^airflow-core/src/airflow/.*\.py$
        require_serial: true
        exclude: ^airflow-core/src/airflow/kubernetes/
        entry: ./scripts/ci/pre_commit/check_airflow_imports.py
          --pattern '^airflow\.kubernetes'
          --message "You should only import kubernetes code from `airflow.providers.cncf.kubernetes`."
        additional_dependencies: ['rich>=12.4.4']
      - id: check-common-compat-used-for-openlineage
        name: Check common.compat is used for OL deprecated classes
        language: python
        files: ^airflow-core/src/airflow/.*\.py$
        require_serial: true
        exclude: >
          (?x)
          ^airflow-core/src/airflow/openlineage/|
          ^airflow/providers/common/compat/openlineage/facet.py$
        entry: ./scripts/ci/pre_commit/check_airflow_imports.py
          --pattern '^openlineage\.client\.(facet|run)'
          --message "You should import from `airflow.providers.common.compat.openlineage.facet` instead."
        additional_dependencies: ['rich>=12.4.4']
      - id: check-airflow-providers-bug-report-template
        name: Sort airflow-bug-report provider list
        language: python
        files: ^\.github/ISSUE_TEMPLATE/3-airflow_providers_bug_report\.yml$
        require_serial: true
        entry: ./scripts/ci/pre_commit/check_airflow_bug_report_template.py
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2']
      - id: check-cncf-k8s-only-for-executors
        name: Check cncf.kubernetes imports used for executors only
        language: python
        files: ^airflow-core/src/airflow/.*\.py$
        require_serial: true
        exclude: >
          (?x)
          ^providers/.*/src/airflow/providers/|
          ^airflow-core/src/airflow/exceptions\.py$|
          ^airflow-core/src/airflow/models/renderedtifields\.py$|
          ^airflow-core/src/airflow/serialization/serialized_objects\.py$|
          ^airflow-core/src/airflow/serialization/serializers/kubernetes\.py$|
          ^airflow-core/src/airflow/utils/sqlalchemy\.py$
        entry: ./scripts/ci/pre_commit/check_airflow_imports.py
            --pattern '^airflow\.providers\.cncf\.kubernetes'
            --message "Only few k8s executors exceptions are allowed to use `airflow.providers.cncf.kubernetes`."
        additional_dependencies: ['rich>=12.4.4']
      - id: update-local-yml-file
        name: Update mounts in the local yml file
        entry: ./scripts/ci/pre_commit/local_yml_mounts.py
        language: python
        files: ^dev/breeze/src/airflow_breeze/utils/docker_command_utils\.py$|^scripts/ci/docker_compose/local\.yml$
        pass_filenames: false
        additional_dependencies: ['rich>=12.4.4']
      - id: check-sql-dependency-common-data-structure
        name: Check dependency of SQL providers
        description: Check dependency of SQL Providers with common data structure
        entry: ./scripts/ci/pre_commit/check_common_sql_dependency.py
        language: python
        files: ^providers/.*/src/airflow/providers/.*/hooks/.*\.py$
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2', 'packaging>=23.2']
      - id: check-extra-packages-references
        name: Checks setup extra packages
        description: Checks if all the extras defined in hatch_build.py are listed in extra-packages-ref.rst file
        language: python
        files: ^airflow-core/docs/extra-packages-ref\.rst$|^hatch_build\.py$
        pass_filenames: false
        entry: ./scripts/ci/pre_commit/check_extra_packages_ref.py
        additional_dependencies: ['rich>=12.4.4', 'hatchling==1.27.0', 'tabulate>=0.9.0']
      - id: check-extras-order
        name: Check order of extras in Dockerfile
        entry: ./scripts/ci/pre_commit/check_order_dockerfile_extras.py
        language: python
        files: ^Dockerfile$
        pass_filenames: false
        additional_dependencies: ['rich>=12.4.4']
      - id: generate-airflow-diagrams
        name: Generate airflow diagrams
        entry: ./scripts/ci/pre_commit/generate_airflow_diagrams.py
        language: python
        files: >
          (?x)
          ^airflow-core/docs/.*/diagram_[^/]*\.py$|
          ^docs/images/.*\.py$
        pass_filenames: true
        additional_dependencies: ['rich>=12.4.4', 'diagrams>=0.23.4']
      - id: generate-volumes-for-sources
        name: Generate volumes for docker compose
        entry: ./scripts/ci/pre_commit/generate_volumes_for_sources.py
        language: python
        files: ^providers/.*/provider\.yaml$
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: update-supported-versions
        name: Updates supported versions in documentation
        entry: ./scripts/ci/pre_commit/supported_versions.py
        language: python
        files: ^airflow-core/docs/installation/supported-versions\.rst$|^scripts/ci/pre_commit/supported_versions\.py$|^README\.md$
        pass_filenames: false
        additional_dependencies: ['tabulate>=0.9.0']
      - id: check-revision-heads-map
        name: Check that the REVISION_HEADS_MAP is up-to-date
        language: python
        entry: ./scripts/ci/pre_commit/check_revision_heads_map.py
        pass_filenames: false
        files: >
          (?x)
          ^scripts/ci/pre_commit/version_heads_map\.py$|
          ^airflow-core/src/airflow/migrations/versions/.*$|^airflow-core/src/airflow/migrations/versions|
          ^providers/fab/src/airflow/providers/fab/migrations/versions/.*$|^providers/fab/src/airflow/providers/fab/migrations/versions|
          ^airflow-core/src/airflow/utils/db\.py$|
          ^providers/fab/src/airflow/providers/fab/auth_manager/models/db\.py$
        additional_dependencies: ['packaging>=23.2', 'rich>=12.4.4']
      - id: update-version
        name: Update versions in docs
        entry: ./scripts/ci/pre_commit/update_versions.py
        language: python
        files: ^docs|^airflow-core/src/airflow/__init__\.py$|.*/pyproject\.toml$
        pass_filenames: false
        additional_dependencies: ['rich>=12.4.4']
      - id: check-pydevd-left-in-code
        language: pygrep
        name: Check for pydevd debug statements accidentally left
        entry: "pydevd.*settrace\\("
        pass_filenames: true
        files: \.py$
      - id: check-links-to-example-dags-do-not-use-hardcoded-versions
        name: Verify no hard-coded version in example dags
        description: The links to example dags should use |version| as version specification
        language: pygrep
        entry: >
          (?i)
          .*https://github.*/main/providers/.*/src/airflow/providers/.*/example_dags/|
          .*https://github.*/master/providers/.*/src/airflow/providers/.*/example_dags/
        pass_filenames: true
        files: ^providers/.*/docs/.*\.rst
      - id: check-safe-filter-usage-in-html
        language: pygrep
        name: Don't use safe in templates
        description: the Safe filter is error-prone, use Markup() in code instead
        entry: "\\|\\s*safe"
        files: \.html$
        pass_filenames: true
      - id: check-no-providers-in-core-examples
        language: pygrep
        name: No providers imports in core example DAGs
        description: The core example DAGs have no dependencies other than standard provider or core Airflow
        entry: "^\\s*from airflow\\.providers.(?!standard.)"
        pass_filenames: true
        files: ^airflow-core/src/airflow/example_dags/.*\.py$
      - id: check-no-airflow-deprecation-in-providers
        language: pygrep
        name: Do not use DeprecationWarning in providers
        description: Use AirflowProviderDeprecationWarning in providers
        entry: "^\\s*DeprecationWarning*"
        pass_filenames: true
        files: ^providers/.*/src/airflow/providers/.*\.py$
      - id: check-urlparse-usage-in-code
        language: pygrep
        name: Don't use urlparse in code
        description: urlparse is not recommended, use urlsplit() in code instead
        entry: "^\\s*from urllib\\.parse import ((\\|, )(urlparse\\|urlunparse))+$"
        pass_filenames: true
        files: \.py$
      - id: check-only-new-session-with-provide-session
        name: Check NEW_SESSION is only used with @provide_session
        language: python
        entry: ./scripts/ci/pre_commit/new_session_in_provide_session.py
        pass_filenames: true
        files: ^airflow-core/src/airflow/.+\.py$
        exclude: ^airflow-core/src/airflow/serialization/pydantic/.*
      - id: check-for-inclusive-language
        language: pygrep
        name: Check for language that we do not accept as community
        description: Please use more appropriate words for community documentation.
        entry: >
          (?ix)
          (black|white)[_-]?list|
          \bshe\b|
          \bhe\b|
          \bher\b|
          \bhis\b|
          \bmaster\b|
          \bslave\b|
          \bsanity\b|
          \bdummy\b
        pass_filenames: true
        exclude: >
          (?x)
          ^airflow-core/src/airflow/ui/openapi-gen/|
          ^airflow-core/src/airflow/cli/commands/local_commands/fastapi_api_command\.py$|
          ^airflow-core/src/airflow/config_templates/|
          ^airflow-core/src/airflow/models/baseoperator\.py$|
          ^airflow-core/src/airflow/operators/__init__\.py$|
          ^providers/common/sql/tests/provider_tests/common/sql/operators/test_sql_execute\.py$|
          ^providers/amazon/src/airflow/providers/amazon/aws/hooks/emr\.py$|
          ^providers/amazon/src/airflow/providers/amazon/aws/operators/emr\.py$|
          ^providers/apache/cassandra/src/airflow/providers/apache/cassandra/hooks/cassandra\.py$|
          ^providers/apache/hive/src/airflow/providers/apache/hive/operators/hive_stats\.py$|
          ^providers/apache/hive/src/airflow/providers/apache/hive/transfers/vertica_to_hive\.py$|
          ^providers/apache/spark/src/airflow/providers/apache/spark/decorators/|
          ^providers/apache/spark/src/airflow/providers/apache/spark/hooks/|
          ^providers/apache/spark/src/airflow/providers/apache/spark/operators/|
          ^providers/exasol/src/airflow/providers/exasol/hooks/exasol\.py$|
          ^providers/fab/src/airflow/providers/fab/auth_manager/security_manager/|
          ^providers/fab/src/airflow/providers/fab/www/static/|
          ^providers/fab/src/airflow/providers/fab/www/templates/|
          ^providers/google/src/airflow/providers/google/cloud/hooks/bigquery\.py$|
          ^providers/google/src/airflow/providers/google/cloud/operators/cloud_build\.py$|
          ^providers/google/src/airflow/providers/google/cloud/operators/dataproc\.py$|
          ^providers/google/src/airflow/providers/google/cloud/operators/mlengine\.py$|
          ^providers/microsoft/azure/src/airflow/providers/microsoft/azure/hooks/cosmos\.py$|
          ^providers/microsoft/winrm/src/airflow/providers/microsoft/winrm/hooks/winrm\.py$|
          ^airflow-core/docs/.*commits\.rst$|
          ^providers/microsoft/winrm/src/airflow/providers/microsoft/winrm/operators/winrm\.py$|
          ^providers/opsgenie/src/airflow/providers/opsgenie/hooks/opsgenie\.py$|
          ^providers/redis/src/airflow/providers/redis/provider\.yaml$|
          ^airflow-core/src/airflow/serialization/serialized_objects\.py$|
          ^airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui/pnpm-lock\.yaml$|
          ^airflow-core/src/airflow/ui/pnpm-lock\.yaml$|
          ^airflow-core/src/airflow/utils/db\.py$|
          ^airflow-core/src/airflow/utils/trigger_rule\.py$|
          ^chart/values.schema\.json$|
          ^helm-tests/tests/chart_utils/helm_template_generator\.py$|
          ^helm-tests/tests/chart_utils/ingress-networking-v1beta1\.json$|
          ^dev/|
          ^devel-common/src/docs/README\.rst$|
          ^docs/apache-airflow-providers-amazon/secrets-backends/aws-ssm-parameter-store\.rst$|
          ^providers/apache/kafka/docs/connections/kafka\.rst$|
          ^providers/apache/hdfs/docs/connections\.rst$|
          ^providers/apache/spark/docs/decorators/pyspark\.rst$|
          ^providers/microsoft/azure/docs/connections/azure_cosmos\.rst$|
          ^providers/fab/docs/auth-manager/webserver-authentication\.rst$|
          ^providers/google/docs/operators/cloud/kubernetes_engine\.rst$|
          ^providers/cncf/kubernetes/docs/operators\.rst$|
          ^.*/conf\.py$|
          ^.*/conf_constants\.py$|
          ^.*/provider_conf\.py$|
          ^devel-common/src/sphinx_exts/removemarktransform\.py|
          ^airflow-core/newsfragments/41761.significant\.rst$|
          ^scripts/ci/pre_commit/vendor_k8s_json_schema\.py$|
          ^scripts/ci/docker-compose/integration-keycloak\.yml$|
          ^scripts/ci/docker-compose/keycloak/keycloak-entrypoint\.sh$|
          ^airflow-core/tests/|
          ^providers/.*/tests/|
          ^\.pre-commit-config\.yaml$|
          ^.*CHANGELOG\.(rst|txt)$|
          ^.*changelog\.(rst|txt)$|
          ^.*commits\.(rst|txt)$|
          ^.*RELEASE_NOTES\.rst$|
          ^contributing-docs/03_contributors_quick_start\.rst$|
          ^.*\.(png|gif|jp[e]?g|svg|tgz|lock)$|
          git|
          ^airflow-core/newsfragments/43349\.significant\.rst$|
          ^airflow-core/newsfragments/41368\.significant\.rst$|
          .*/dist/.*|
          package-lock\.json$
      - id: check-base-operator-partial-arguments
        name: Check BaseOperator and partial() arguments
        language: python
        entry: ./scripts/ci/pre_commit/check_base_operator_partial_arguments.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/models/(?:base|mapped)operator\.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-init-decorator-arguments
        name: Sync model __init__ and decorator arguments
        language: python
        entry: ./scripts/ci/pre_commit/check_init_decorator_arguments.py
        pass_filenames: false
        files: ^task-sdk/src/airflow/sdk/definitions/dag\.py$|^task-sdk/src/airflow/sdk/definitions/decorators/task_group\.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-template-context-variable-in-sync
        name: Sync template context variable refs
        language: python
        entry: ./scripts/ci/pre_commit/check_template_context_variable_in_sync.py
        files: ^airflow-core/src/airflow/models/taskinstance\.py$|^task-sdk/src/airflow/sdk/definitions/context\.py$|^airflow-core/docs/templates-ref\.rst$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-base-operator-usage
        language: pygrep
        name: Check BaseOperator core imports
        description: Make sure BaseOperator is imported from airflow.models.baseoperator in core
        entry: "from airflow\\.models import.* BaseOperator\\b"
        files: \.py$
        pass_filenames: true
        exclude: >
          (?x)
          ^airflow-core/src/airflow/decorators/.*$|
          ^airflow-core/src/airflow/hooks/.*$|
          ^airflow-core/src/airflow/operators/.*$|
          ^providers/.*$
      - id: check-base-operator-usage
        language: pygrep
        name: Check BaseOperatorLink core imports
        description: Make sure BaseOperatorLink is not imported from airflow.models in core
        entry: "^\\s*from airflow\\.models\\.baseoperatorlink import BaseOperatorLink\\b"
        files: \.py$
        pass_filenames: true
        exclude: >
          (?x)
          ^airflow-core/src/airflow/decorators/.*$|
          ^airflow-core/src/airflow/hooks/.*$|
          ^airflow-core/src/airflow/operators/.*$|
          ^providers/.*/src/airflow/providers/.*$|
          ^providers/.*/src/airflow/providers/standard/sensors/.*$
      - id: check-base-operator-usage
        language: pygrep
        name: Check BaseOperator other imports
        description: Make sure BaseOperator is imported from airflow.models outside of core
        entry: "from airflow\\.models\\.baseoperator import.* BaseOperator"
        pass_filenames: true
        files: >
          (?x)
          ^providers/.*/src/airflow/providers/.*\.py$
        exclude: providers/standard/.*/.*\.py$
      - id: check-get-lineage-collector-providers
        language: python
        name: Check providers import hook lineage code from compat
        description: Make sure you import from airflow.provider.common.compat.lineage.hook instead of
          airflow.lineage.hook.
        entry: ./scripts/ci/pre_commit/check_airflow_imports.py
          --pattern '^airflow\.lineage\.hook'
          --message "Only TYPE_CHECKING imports from `airflow.lineage.hook` are allowed in providers."
          --only_top_level
        files: ^providers/.*/src/airflow/providers/.*\.py$
        exclude: ^providers/common/compat/src/airflow/providers/common/compat/.*\.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-decorated-operator-implements-custom-name
        name: Check @task decorator implements custom_operator_name
        language: python
        entry: ./scripts/ci/pre_commit/decorator_operator_implements_custom_name.py
        pass_filenames: true
        files: ^airflow-core/src/airflow/.*\.py$
      - id: check-core-deprecation-classes
        language: pygrep
        name: Verify usage of Airflow deprecation classes in core
        entry: category=DeprecationWarning|category=PendingDeprecationWarning
        files: \.py$
        exclude: ^airflow-core/src/airflow/configuration\.py$|airflow-core/tests/.*$|^providers/.*/src/airflow/providers/|^scripts/in_container/verify_providers\.py$|providers/.*/tests/.*$|^devel-common/
        pass_filenames: true
      - id: check-provide-create-sessions-imports
        language: pygrep
        name: Check session util imports
        description: NEW_SESSION, provide_session, and create_session should be imported from airflow.utils.session to avoid import cycles.
        entry: "from airflow\\.utils\\.db import.* (NEW_SESSION|provide_session|create_session)"
        files: \.py$
        pass_filenames: true
      - id: check-incorrect-use-of-LoggingMixin
        language: pygrep
        name: Make sure LoggingMixin is not used alone
        entry: "LoggingMixin\\(\\)"
        files: \.py$
        pass_filenames: true
      - id: check-daysago-import-from-utils
        language: pygrep
        name: days_ago imported from airflow.utils.dates
        entry: "(airflow\\.){0,1}utils\\.dates\\.days_ago"
        files: \.py$
        pass_filenames: true
      - id: check-start-date-not-used-in-defaults
        language: pygrep
        name: start_date not in default_args
        entry: "default_args\\s*=\\s*{\\s*(\"|')start_date(\"|')|(\"|')start_date(\"|'):"
        files: \.*example_dags.*\.py$
        pass_filenames: true
      - id: check-apache-license-rat
        name: Check if licenses are OK for Apache
        entry: ./scripts/ci/pre_commit/check_license.py
        language: python
        files: ^LICENSE$
        pass_filenames: false
      - id: check-aiobotocore-optional
        name: Check if aiobotocore is an optional dependency only
        entry: ./scripts/ci/pre_commit/check_aiobotocore_optional.py
        language: python
        files: ^providers/.*/provider\.yaml$
        pass_filenames: true
        additional_dependencies: ['click>=8.1.8', 'rich>=12.4.4', 'pyyaml>=6.0.2']
        require_serial: true
      - id: check-boring-cyborg-configuration
        name: Checks for Boring Cyborg configuration consistency
        language: python
        entry: ./scripts/ci/pre_commit/boring_cyborg.py
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['pyyaml>=6.0.2', 'termcolor==2.5.0', 'wcmatch==8.2']
      - id: update-in-the-wild-to-be-sorted
        name: Sort INTHEWILD.md alphabetically
        entry: ./scripts/ci/pre_commit/sort_in_the_wild.py
        language: python
        files: ^\.pre-commit-config\.yaml$|^INTHEWILD\.md$
        pass_filenames: false
        require_serial: true
      - id: update-installed-providers-to-be-sorted
        name: Sort and uniquify installed_providers.txt
        entry: ./scripts/ci/pre_commit/sort_installed_providers.py
        language: python
        files: ^\.pre-commit-config\.yaml$|^.*_installed_providers\.txt$
        pass_filenames: false
        require_serial: true
      - id: update-spelling-wordlist-to-be-sorted
        name: Sort spelling_wordlist.txt
        entry: ./scripts/ci/pre_commit/sort_spelling_wordlist.py
        language: python
        files: ^\.pre-commit-config\.yaml$|^docs/spelling_wordlist\.txt$
        require_serial: true
        pass_filenames: false
      - id: lint-helm-chart
        name: Lint Helm Chart
        entry: ./scripts/ci/pre_commit/lint_helm.py
        language: python
        pass_filenames: false
        files: ^chart
        require_serial: true
        additional_dependencies: ['rich>=12.4.4','requests>=2.31.0']
      - id: kubeconform
        name: Kubeconform check on our helm chart
        entry: ./scripts/ci/pre_commit/check_kubeconform.py
        language: python
        pass_filenames: false
        files: ^chart
        require_serial: true
        additional_dependencies: ['rich>=12.4.4','requests>=2.31.0']
      - id: shellcheck
        name: Check Shell scripts syntax correctness
        language: docker_image
        entry: koalaman/shellcheck:v0.8.0 -x -a
        files: \.(bash|sh)$|^hooks/build$|^hooks/push$
        exclude: ^dev/breeze/autocomplete/.*$
      - id: compile-ui-assets
        name: Compile ui assets (manual)
        language: node
        stages: ['manual']
        types_or: [javascript, ts, tsx]
        files: ^airflow-core/src/airflow/ui/|^airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui/
        entry: ./scripts/ci/pre_commit/compile_ui_assets.py
        pass_filenames: false
        additional_dependencies: ['pnpm@9.7.1']
      - id: compile-fab-assets
        name: Compile FAB provider assets
        language: node
        'types_or': [javascript, ts, tsx]
        files: ^providers/fab/.*/www/
        entry: ./scripts/ci/pre_commit/compile_fab_assets.py
        pass_filenames: false
        additional_dependencies: ['yarn@1.22.21']
      - id: compile-ui-assets-dev
        name: Compile ui assets in dev mode (manual)
        language: node
        stages: ['manual']
        types_or: [javascript, ts, tsx]
        files: ^airflow-core/src/airflow/ui/|^airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui/
        entry: ./scripts/ci/pre_commit/compile_ui_assets_dev.py
        pass_filenames: false
        additional_dependencies: ['pnpm@9.7.1']
      - id: check-providers-subpackages-init-file-exist
        name: Provider subpackage init files are there
        pass_filenames: false
        always_run: true
        entry: ./scripts/ci/pre_commit/check_providers_subpackages_all_have_init.py
        language: python
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: check-pre-commit-information-consistent
        name: Validate hook IDs & names and sync with docs
        entry: ./scripts/ci/pre_commit/check_pre_commit_hooks.py
        args:
          - --max-length=53
        language: python
        files: ^\.pre-commit-config\.yaml$|^scripts/ci/pre_commit/check_pre_commit_hooks\.py$
        additional_dependencies: ['pyyaml>=6.0.2', 'jinja2', 'black==24.10.0', 'tabulate>=0.9.0', 'rich>=12.4.4']
        require_serial: true
        pass_filenames: false
      - id: check-integrations-list-consistent
        name: Sync integrations list with docs
        entry: ./scripts/ci/pre_commit/check_integrations_list.py
        language: python
        files: ^scripts/ci/docker-compose/integration-.*\.yml$|^contributing-docs/testing/integration_tests\.rst$
        additional_dependencies: ['black==24.10.0', 'tabulate>=0.9.0', 'rich>=12.4.4', 'pyyaml>=6.0.2']
        require_serial: true
        pass_filenames: false
      - id: update-breeze-readme-config-hash
        name: Update Breeze README.md with config files hash
        language: python
        entry: ./scripts/ci/pre_commit/update_breeze_config_hash.py
        files: ^dev/breeze/pyproject\.toml$|^dev/breeze/README\.md$
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: update-pyproject-toml
        name: Update Airflow's meta-package pyproject.toml
        language: python
        entry: ./scripts/ci/pre_commit/update_airflow_pyproject_toml.py
        files: ^.*/pyproject\.toml$|^scripts/ci/pre_commit/update_airflow_pyproject_toml\.py$
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['rich>=12.4.4', 'tomli>=2.0.1', 'packaging>=23.2' ]
      - id: update-reproducible-source-date-epoch
        name: Update Source Date Epoch for reproducible builds
        language: python
        entry: ./scripts/ci/pre_commit/update_source_date_epoch.py
        files: ^RELEASE_NOTES\.rst$|^chart/RELEASE_NOTES\.rst$
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2']
        pass_filenames: false
        require_serial: true
      - id: check-breeze-top-dependencies-limited
        name: Check top-level breeze deps
        description: Breeze should have small number of top-level dependencies
        language: python
        entry: ./scripts/tools/check_if_limited_dependencies.py
        files: ^dev/breeze/.*$
        pass_filenames: false
        require_serial: true
        additional_dependencies: ['click>=8.1.8', 'rich>=12.4.4', 'pyyaml>=6.0.2']
      - id: check-tests-in-the-right-folders
        name: Check if tests are in the right folders
        entry: ./scripts/ci/pre_commit/check_tests_in_right_folders.py
        language: python
        files: ^airflow-core/tests/.*\.py$
        pass_filenames: true
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: check-system-tests-present
        name: Check if system tests have required segments of code
        entry: ./scripts/ci/pre_commit/check_system_tests.py
        language: python
        files: ^.*/tests/system/.*/example_[^/]*\.py$
        pass_filenames: true
        additional_dependencies: ['rich>=12.4.4']
      - id: generate-pypi-readme
        name: Generate PyPI README
        entry: ./scripts/ci/pre_commit/generate_pypi_readme.py
        language: python
        files: ^README\.md$
        pass_filenames: false
      - id: lint-markdown
        name: Run markdownlint
        description: Checks the style of Markdown files.
        entry: markdownlint
        language: node
        types: [markdown]
        files: \.(md|mdown|markdown)$
        additional_dependencies: ['markdownlint-cli@0.38.0']
      - id: lint-json-schema
        name: Lint JSON Schema files
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --spec-file
          - scripts/ci/pre_commit/draft7_schema.json
        language: python
        pass_filenames: true
        files: .*\.schema\.json$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: lint-json-schema
        name: Lint NodePort Service
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --spec-url
          - https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/v1.20.2-standalone/service-v1.json
        language: python
        pass_filenames: true
        files: ^scripts/ci/kubernetes/nodeport\.yaml$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: lint-json-schema
        name: Lint Docker compose files
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --spec-url
          - https://raw.githubusercontent.com/compose-spec/compose-spec/master/schema/compose-spec.json
        language: python
        pass_filenames: true
        files: ^scripts/ci/docker-compose/.+\.ya?ml$|docker-compose\.ya?ml$
        exclude: >
          (?x)
          ^scripts/ci/docker-compose/grafana/.|
          ^scripts/ci/docker-compose/gremlin/.|
          ^scripts/ci/docker-compose/.+-config\.ya?ml$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: lint-json-schema
        name: Lint chart/values.schema.json
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --spec-file
          - chart/values_schema.schema.json
          - chart/values.schema.json
        language: python
        pass_filenames: false
        files: ^chart/values\.schema\.json$|^chart/values_schema\.schema\.json$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: update-vendored-in-k8s-json-schema
        name: Vendor k8s definitions into values.schema.json
        entry: ./scripts/ci/pre_commit/vendor_k8s_json_schema.py
        language: python
        files: ^chart/values\.schema\.json$
        additional_dependencies: ['requests==2.32.3']
      - id: lint-json-schema
        name: Lint chart/values.yaml
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --enforce-defaults
          - --spec-file
          - chart/values.schema.json
          - chart/values.yaml
        language: python
        pass_filenames: false
        files: ^chart/values\.yaml$|^chart/values\.schema\.json$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: lint-json-schema
        name: Lint config_templates/config.yml
        entry: ./scripts/ci/pre_commit/lint_json_schema.py
        args:
          - --spec-file
          - airflow-core/src/airflow/config_templates/config.yml.schema.json
        language: python
        pass_filenames: true
        files: ^airflow-core/src/airflow/config_templates/config\.yml$
        require_serial: true
        additional_dependencies: ['jsonschema>=3.2.0,<5.0', 'pyyaml>=6.0.2', 'requests==2.32.3', 'rich>=12.4.4']
      - id: check-persist-credentials-disabled-in-github-workflows
        name: Check persistent creds in workflow files
        description: Check that workflow files have persist-credentials disabled
        entry: ./scripts/ci/pre_commit/checkout_no_credentials.py
        language: python
        pass_filenames: true
        files: ^\.github/workflows/.*\.yml$
        additional_dependencies: ['pyyaml>=6.0.1', 'rich>=12.4.4']
      - id: check-docstring-param-types
        name: Check that docstrings do not specify param types
        entry: ./scripts/ci/pre_commit/docstring_param_type.py
        language: python
        pass_filenames: true
        files: \.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-zip-file-is-not-committed
        name: Check no zip files are committed
        description: Zip files are not allowed in the repository
        language: fail
        entry: |
          Zip files are not allowed in the repository as they are hard to
          track and have security implications. Please remove the zip file from the repository.
        files: \.zip$
      - id: check-code-deprecations
        name: Check deprecations categories in decorators
        entry: ./scripts/ci/pre_commit/check_deprecations.py
        language: python
        pass_filenames: true
        files: ^airflow-core/src/airflow/.*\.py$
        additional_dependencies: ['rich>=12.4.4', 'python-dateutil>=2.9.0']
      - id: lint-chart-schema
        name: Lint chart/values.schema.json file
        entry: ./scripts/ci/pre_commit/chart_schema.py
        language: python
        pass_filenames: false
        files: ^chart/values\.schema\.json$
        require_serial: true
      - id: update-inlined-dockerfile-scripts
        name: Inline Dockerfile and Dockerfile.ci scripts
        entry: ./scripts/ci/pre_commit/inline_scripts_in_docker.py
        language: python
        pass_filenames: false
        files: ^Dockerfile$|^Dockerfile\.ci$|^scripts/docker/.*$
        require_serial: true
      - id: check-changelog-has-no-duplicates
        name: Check changelogs for duplicate entries
        language: python
        files: changelog\.(rst|txt)$
        entry: ./scripts/ci/pre_commit/changelog_duplicates.py
        pass_filenames: true
      - id: check-changelog-format
        name: Check changelog format
        language: python
        files: changelog\.(rst|txt)$
        entry: ./scripts/ci/pre_commit/check_changelog_format.py
        pass_filenames: true
      - id: check-newsfragments-are-valid
        name: Check newsfragments are valid
        language: python
        files: newsfragments/.*\.rst$
        entry: ./scripts/ci/pre_commit/newsfragments.py
        pass_filenames: true
        # We sometimes won't have newsfragments in the repo, so always run it so `check-hooks-apply` passes
        # This is fast, so not too much downside
        always_run: true
      - id: check-significant-newsfragments-are-valid
        name: Check significant newsfragments are valid
        # Significant newsfragments follows a special format so that we can group information easily.
        language: python
        files: airflow-core/newsfragments/.*\.rst$
        entry: ./scripts/ci/pre_commit/significant_newsfragments_checker.py
        pass_filenames: false
        additional_dependencies: ['docutils>=0.21.2', 'pygments>=2.19.1', 'jinja2>=3.1.5']
        # We sometimes won't have newsfragments in the repo, so always run it so `check-hooks-apply` passes
        # This is fast, so not too much downside
        always_run: true
      - id: update-breeze-cmd-output
        name: Update breeze docs
        description: Update output of breeze commands in Breeze documentation
        entry: ./scripts/ci/pre_commit/breeze_cmd_line.py
        language: python
        files: >
          (?x)
          ^dev/breeze/.*$|
          ^\.pre-commit-config\.yaml$|
          ^scripts/ci/pre_commit/breeze_cmd_line\.py$|
          ^generated/provider_dependencies\.json$
        require_serial: true
        pass_filenames: false
        additional_dependencies: ['rich>=12.4.4']
      - id: check-example-dags-urls
        name: Check that example dags url include provider versions
        entry: ./scripts/ci/pre_commit/update_example_dags_paths.py
        language: python
        pass_filenames: true
        files: ^airflow-core/docs/.*example-dags\.rst$|^docs/.*index\.rst$^airflow-core/docs/.*index\.rst$
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2']
        always_run: true
      - id: check-system-tests-tocs
        name: Check that system tests is properly added
        entry: ./scripts/ci/pre_commit/check_system_tests_hidden_in_index.py
        language: python
        pass_filenames: true
        files: ^providers/.*/docs/index\.rst$
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2']
      - id: check-lazy-logging
        name: Check that all logging methods are lazy
        entry: ./scripts/ci/pre_commit/check_lazy_logging.py
        language: python
        pass_filenames: true
        files: \.py$
        additional_dependencies: ['rich>=12.4.4', 'astor>=0.8.1']
      - id: create-missing-init-py-files-tests
        name: Create missing init.py files in tests
        entry: ./scripts/ci/pre_commit/check_init_in_tests.py
        language: python
        additional_dependencies: ['rich>=12.4.4']
        pass_filenames: false
        files: ^airflow-core/tests/.*\.py$
      - id: check-tests-unittest-testcase
        name: Unit tests do not inherit from unittest.TestCase
        description: Check that unit tests do not inherit from unittest.TestCase
        entry: ./scripts/ci/pre_commit/unittest_testcase.py
        language: python
        pass_filenames: true
        files: ^airflow-core/tests/.*\.py$
      - id: check-provider-docs-valid
        name: Validate provider doc files
        entry: ./scripts/ci/pre_commit/check_provider_docs.py
        language: python
        files: ^providers/.*/provider\.yaml$|^.*/docs/.*
        additional_dependencies: ['rich>=12.4.4', 'pyyaml>=6.0.2', 'jinja2']
        require_serial: true
        pass_filenames: false
      - id: bandit
        name: bandit
        description: "Bandit is a tool for finding common security issues in Python code"
        entry: bandit
        language: python
        language_version: python3
        types: [python]
        additional_dependencies: ['bandit==1.7.6']
        require_serial: true
        files: ^airflow-core/src/airflow/.*
        exclude:
          airflow/example_dags/.*
        args:
          - "--skip"
          - "B101,B301,B324,B403,B404,B603"
          - "--severity-level"
          - "high"  # TODO: remove this line when we fix all the issues
      - id: pylint
        name: pylint
        description: "Pylint is a static code analyser for Python 2 or 3."
        entry: pylint
        language: python
        language_version: python3
        types: [python]
        additional_dependencies: ['pylint==3.1.0']
        require_serial: true
        files: ^airflow-core/src/airflow/.*
        exclude:
          airflow/example_dags/.*
        args:
          # Use pylint only for the specific check, which are not available into the ruff
          - "--disable=all"
          # W0133: "Exception statement has no effect"
          # see: https://github.com/astral-sh/ruff/issues/10145
          - "--enable=W0133"
      - id: check-fab-migrations
        language: pygrep
        name: Check no migration is done on FAB related table
        description: >
          FAB tables are no longer used in core Airflow but in FAB provider.
          As such, it is forbidden to create migrations related to FAB tables in core Airflow.
          Such migrations should be in FAB provider. To achieve this, a new capability must be implemented:
          support migrations for providers. In other words, providers need to be able to specify migrations
          so that, any FAB related migration (besides the legacy ones) is defined in FAB provider.
          See https://github.com/apache/airflow/issues/32210
        entry: >
          (?ix)
          \bab_permission\b|
          \bab_view_menu\b|
          \bab_role\b|
          \bab_permission_view\b|
          \bab_permission_view_role\b|
          \bab_user\b|
          \bab_user_role\b|
          \bab_register_user\b
        pass_filenames: true
        files: ^airflow-core/src/airflow/migrations/versions/.*\.py$
        exclude:
          airflow-core/src/airflow/migrations/versions/0028_3_0_0_drop_ab_user_id_foreign_key.py
      - id: generate-airflowctl-help-images
        name: Generate SVG from Airflow CTL Commands
        entry: ./scripts/ci/pre_commit/capture_airflowctl_help.py
        language: python
        pass_filenames: false
        files:
          ^airflow-ctl/src/airflowctl/api/operations.py|airflow-ctl/src/airflowctl/cli/.*\.py$
        additional_dependencies: ['rich>=12.4.4']
      - id: gofmt
        name: Format go code
        entry: golines --base-formatter=gofumpt --write-output --max-len=100 --chain-split-dots
        additional_dependencies: [github.com/segmentio/golines@latest, mvdan.cc/gofumpt@v0.8.0]
        files: ^go-sdk/
        types: [go]
        language: golang
      - id: gci
        name: Consistent import ordering for Go files
        # Since this is invoked from the root folder, not go-sdk/, gci can't auto-detect the prefix
        entry: gci write -s standard -s default -s "prefix(github.com/apache/airflow)"
        additional_dependencies: [github.com/daixiang0/gci@v0.13.6]
        files: ^go-sdk/
        types: [go]
        language: golang
        ## ADD MOST PRE-COMMITS ABOVE THAT LINE
        # The below pre-commits are those requiring CI image to be built
      - id: mypy-dev
        name: Run mypy for dev
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: ^dev/.*\.py$|^scripts/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-dev
        stages: ['manual']
        name: Run mypy for dev (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py dev
        pass_filenames: false
        files: ^.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-airflow-core
        name: Run mypy for airflow-core
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: airflow-core/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-airflow-core
        stages: ['manual']
        name: Run mypy for airflow-core (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py airflow-core
        pass_filenames: false
        files: airflow-core/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-providers
        name: Run mypy for providers
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: ^providers/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-providers
        stages: ['manual']
        name: Run mypy for providers (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py providers
        pass_filenames: false
        files: ^.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-task-sdk
        name: Run mypy for task-sdk
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: ^task-sdk/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-task-sdk
        stages: ['manual']
        name: Run mypy for task-sdk (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py task-sdk
        pass_filenames: false
        files: ^.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-devel-common
        name: Run mypy for devel-common
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: ^devel-common/.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-devel-common
        stages: ['manual']
        name: Run mypy for devel-common (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py devel-common
        pass_filenames: false
        files: ^.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-airflow-ctl
        name: Run mypy for airflow-ctl
        language: python
        entry: ./scripts/ci/pre_commit/mypy.py
        files: ^airflow-ctl/src/airflowctl/.*\.py$|^airflow-ctl/tests/.*\.py$
        exclude: .*generated.py
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: mypy-airflow-ctl
        stages: ['manual']
        name: Run mypy for airflow-ctl (manual)
        language: python
        entry: ./scripts/ci/pre_commit/mypy_folder.py airflow-ctl
        pass_filenames: false
        files: ^.*\.py$
        require_serial: true
        additional_dependencies: ['rich>=12.4.4']
      - id: generate-openapi-spec
        name: Generate the FastAPI API spec
        language: python
        entry: ./scripts/ci/pre_commit/generate_openapi_spec.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/api_fastapi/.*\.py$|^airflow-core/src/airflow/api_fastapi/auth/managers/simple/.*\.py$|^providers/fab/src/airflow/providers/fab/auth_manager/api_fastapi/.*\.py$
        exclude: ^airflow-core/src/airflow/api_fastapi/execution_api/.*
        additional_dependencies: ['rich>=12.4.4', 'openapi-spec-validator>=0.7.1']
      - id: generate-openapi-spec-fab
        name: Generate the FastAPI API spec for FAB
        language: python
        entry: ./scripts/ci/pre_commit/generate_openapi_spec_fab.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/api_fastapi/.*\.py$|^airflow-core/src/airflow/api_fastapi/auth/managers/simple/.*\.py$|^providers/fab/src/airflow/providers/fab/auth_manager/api_fastapi/.*\.py$
        exclude: ^airflow-core/src/airflow/api_fastapi/execution_api/.*
        additional_dependencies: ['rich>=12.4.4', 'openapi-spec-validator>=0.7.1']
      - id: ts-compile-format-lint-ui
        name: Compile / format / lint UI
        description: TS types generation / ESLint / Prettier new UI files
        language: node
        types_or: [javascript, ts, tsx, yaml, css, json]
        files: |
          (?x)
          ^airflow-core/src/airflow/ui/|
          ^airflow-core/src/airflow/api_fastapi/core_api/openapi/.*\.yaml$|
          ^airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui/|
          ^airflow-core/src/airflow/api_fastapi/auth/managers/simple/openapi/v1.*\.yaml$
        entry: ./scripts/ci/pre_commit/compile_lint_ui.py
        additional_dependencies: ['pnpm@9.7.1']
        pass_filenames: false
      - id: check-provider-yaml-valid
        name: Validate provider.yaml files
        entry: ./scripts/ci/pre_commit/check_provider_yaml_files.py
        language: python
        files: ^providers/.*/provider\.yaml$
        exclude: ^providers/.*/.venv/.*$
        additional_dependencies: ['rich>=12.4.4']
        require_serial: true
      - id: check-template-fields-valid
        name: Check templated fields mapped in operators/sensors
        language: python
        entry: ./scripts/ci/pre_commit/check_template_fields.py
        files: ^(providers/.*/)?airflow-core/.*/(sensors|operators)/.*\.py$
        additional_dependencies: ['rich>=12.4.4']
        require_serial: true
      - id: update-migration-references
        name: Update migration ref doc
        language: python
        entry: ./scripts/ci/pre_commit/migration_reference.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/migrations/versions/.*\.py$|^airflow-core/docs/migrations-ref\.rst$
        additional_dependencies: ['rich>=12.4.4']
      - id: generate-tasksdk-datamodels
        name: Generate Datamodels for TaskSDK client
        language: python
        entry: uv run -p 3.12 --no-dev --no-progress --active --group codegen --project apache-airflow-task-sdk --directory task-sdk -s dev/generate_task_sdk_models.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/api_fastapi/execution_api/.*\.py$
        require_serial: true
      - id: generate-airflowctl-datamodels
        name: Generate Datamodels for AirflowCTL
        language: python
        entry: >
          bash -c '
          uv run -p 3.12 --no-dev --no-progress --active --group codegen --project apache-airflow-ctl --directory airflow-ctl/ datamodel-codegen &&
           uv run -p 3.12 --no-dev --no-progress --active --group codegen --project apache-airflow-ctl --directory airflow-ctl/ datamodel-codegen --input="../airflow-core/src/airflow/api_fastapi/auth/managers/simple/openapi/v2-simple-auth-manager-generated.yaml" --output="src/airflowctl/api/datamodels/auth_generated.py"'
        pass_filenames: false
        files: ^airflow-core/src/airflow/api_fastapi/core_api/datamodels/.*\.py$|^airflow-core/src/airflow/api_fastapi/auth/managers/simple/datamodels/.*\.py$
        require_serial: true
      - id: update-er-diagram
        name: Update ER diagram
        language: python
        entry: ./scripts/ci/pre_commit/update_er_diagram.py
        pass_filenames: false
        files: ^airflow-core/src/airflow/migrations/versions/.*\.py$|^airflow-core/docs/migrations-ref\.rst$
        additional_dependencies: ['rich>=12.4.4']
      - id: check-default-configuration
        name: Check the default configuration
        entry: ./scripts/ci/pre_commit/check_default_configuration.py
        language: python
        additional_dependencies: ['rich>=12.4.4']
        require_serial: true
        pass_filenames: false
        files: ^airflow-core/src/airflow/config_templates/config\.yml$
      - id: generate-airflowctl-help-images
        name: Generate SVG from Airflow CTL Commands
        entry: ./scripts/ci/pre_commit/capture_airflowctl_help.py
        language: python
        pass_filenames: false
        files:
          ^airflow-ctl/src/airflowctl/api/operations.py|airflow-ctl/src/airflowctl/ctl/commands/.*\.py$
        additional_dependencies: ['rich>=12.4.4', 'argcomplete>=1.10']
        ## ONLY ADD PRE-COMMITS HERE THAT REQUIRE CI IMAGE
