# Airflow configuration
airflow.cfg
airflow_login.py
dbinit.py
initdb.py
secrets.py

# Airflow sqlite databases
airflow.db

# Airflow temporary artifacts
airflow-core/src/airflow/git_version
airflow-core/src/airflow/ui/coverage/
# and legacy ones
airflow/git_version
airflow/ui/coverage/
logs/
airflow-webserver.pid
airflow-api-server.pid
standalone_admin_password.txt
warnings.txt
warn-summary-*.txt

# Ruff cache
.ruff-cache

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
!providers/fab/**/www/static/dist
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.coverage
.coverage.*
.cache
nosetests.xml
coverage*.xml
*,cover
.hypothesis/
.pytest_cache

# Translations
*.mo
*.pot

# Django stuff:
# *.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache
/webserver_config.py

# Scrapy stuff:
.scrapy

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# nvm (Node Version Manager)
.nvmrc

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# dotenv
.env
.env.local
.autoenv*.zsh

# virtualenv
.venv*
venv*
ENV/

# Spyder project settings
.spyderproject

# Rope project settings
.ropeproject

# PyCharm
.idea/
*.iml

# vim
*.swp

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# OSX
.DS_Store

# SQL Server backups
*.bkp

# Spark
rat-results.txt

# Git stuff
# Kubernetes generated templated files
*.generated
*.tar.gz
scripts/ci/kubernetes/kube/.generated/airflow.yaml
scripts/ci/kubernetes/docker/requirements.txt

# Node & Webpack Stuff
*.entry.js
node_modules
npm-debug.log*
derby.log
metastore_db
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.vscode/*
!.vscode/extensions.json
/.vite/
# Exclude the ui .vite dir
airflow-core/src/airflow/ui/.vite/
# And the legacy old files just in case
airflow/ui/.vite/
# Add the auth manager vite folder just in case
airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui/.vite/
.pnpm-store
*.tsbuildinfo

# Airflow log files when airflow is run locally
airflow-*.err
airflow-*.out
airflow-*.log
airflow-*.pid
.airflow_db_initialised

# Generated doc files
_api
_build
_doctree
_inventory_cache

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Needed for CI Dockerfile.ci build system
.build
/tmp
/files

/hive_scratch_dir/
/.bash_aliases
/.bash_history
/.kube
/.inputrc
log.txt*

# Legacy Provider-related ignores
/airflow/providers/__init__.py

# Docker context files
/docker-context-files/*
!/docker-context-files/.README.md
# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Terraform variables
*.tfvars

# Might be generated when you build wheels
pip-wheel-metadata

.pypirc

# Dev files
/dev/packages.txt
/dev/Dockerfile.pmc

# Generated UI licenses
3rd-party-licenses/LICENSES-ui.txt
licenses/LICENSES-ui.txt

# Packaged breeze on Windows
/breeze.exe

# Generated out dir

/out

# files generated by memray
*.py.*.html
*.py.*.bin

# used to checkout target-branch in CI
/target-airflow

# This directory used for store autogenerated images
/images

# Dask Executor tests generate this directory
/tests/executors/dask-worker-space/

# airflow-build-dockerfile and correconding ignore file
airflow-build-dockerfile*

# Temporary ignore uv.lock until we integrate it fully in our constraint preparation mechanism
/uv.lock

# Ignore zip files https://github.com/apache/airflow/issues/46449
*.zip

_api/

AVI_AIRFLOW_HOME
