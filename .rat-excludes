# Note: these patterns are applied to single files or directories, not full paths
# coverage/* will ignore any coverage dir, but airflow/www/static/coverage/* will match nothing

.git-blame-ignore-revs
.github/*
.gitignore
.gitattributes
.gitrepo
.airflow_db_initialised
.airflowignore
.babelrc
.coverage
.codecov.yml
.codespellignorelines
.eslintignore
.eslintrc
.prettierignore
.prettierrc
.rat-excludes
.stylelintignore
.stylelintrc
.venv
requirements
requirements.txt
.*log
.travis.yml
.*pyc
.*lock
dist
build
airflow.egg-info
apache_airflow.egg-info
.idea
metastore_db
.*sql
.*svg
.*csv
.*md5
.*zip
.*lock
logs
.bash_aliases
venv
clients/*
files/*
dags/*
generated/*
.gitmodules
prod_image_installed_providers.txt
airflow_pre_installed_providers.txt

# Generated doc files
.*html
_build/*
_static/*
_images/*
.buildinfo
searchindex.js
_api/*

# the word list for checking spellings
spelling_wordlist.txt

# Apache Rat does not detect BSD-2 clause properly
# it is compatible according to http://www.apache.org/legal/resolved.html#category-a
kerberos_auth.py
airflow_api_auth_backend_kerberos_auth_py.html
3rd-party-licenses/*
parallel.js
underscore.js
jquery.dataTables.min.js
jqClock.min.js
dagre-d3.min.js
d3.v3.min.js
ace.js
node_modules/*
.*json
coverage/*
git_version
flake8_diff.sh
coverage*.xml
_sources/*

robots.txt
rat-results.txt
apache-airflow-.*\+source.tar.gz.*
apache-airflow-.*\+bin.tar.gz.*
PULL_REQUEST_TEMPLATE.md
PROVIDER_CHANGES*.md
manifests/*
redirects.txt
reproducible_build.yaml

# Locally mounted files
.*egg-info/*
.bash_history
.bash_aliases
.inputrc

# the example notebook is ASF 2 licensed but RAT cannot read this
input_notebook.ipynb

# .git might be a file in case of worktree
.git
tmp

# Vendored-in code
.*_vendor

# generated doc-only-changes.txt
.latest-doc-only-change.txt

# Chart ignored files
chart/.gitignore
chart/values.schema.json
chart/Chart.lock
chart/values_schema.schema.json
chart/Chart.yaml

# Generated autocomplete files
./dev/breeze/autocomplete/*

# Generated devel_deps files
devel_deps.txt

# Newsfragments are snippets that will be, eventually, consumed into RELEASE_NOTES
newsfragments/*

# Warning file generated
warnings.txt
warn-summary-*.txt

# Dev stuff
tests/*
scripts/*
images/*
dev/*
.*\.iml
out/*
airflow-build-dockerfile*

# Sha files
.*sha256

# DOAP file
doap_airflow.rdf

# pyenv
.python-version

# nvm (Node Version Manager)
.nvmrc

# PKG-INFO file
PKG-INFO

# checksum files
.*\.md5sum

# Openapi files
.openapi-generator-ignore
version.txt
v2*.yaml
_private_ui*.yaml

# Front end generated files
api-generated.ts
openapi-gen
pnpm-lock.yaml

# python generated file
generated.py
auth_generated.py

# hash files
www-hash.txt

# go setup files
go.mod
go.sum
