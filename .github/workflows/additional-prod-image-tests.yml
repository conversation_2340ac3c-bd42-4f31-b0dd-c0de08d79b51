# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Additional PROD image tests
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      default-branch:
        description: "The default branch for the repository"
        required: true
        type: string
      constraints-branch:
        description: "Branch used to construct constraints URL from."
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to upgrade to newer dependencies (true/false)"
        required: true
        type: string
      docker-cache:
        description: "Docker cache specification to build the image (registry, local, disabled)."
        required: true
        type: string
      disable-airflow-repo-cache:
        description: "Disable airflow repo cache read from main."
        required: true
        type: string
      canary-run:
        description: "Whether to run the canary run (true/false)"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      use-uv:
        description: "Whether to use uv"
        required: true
        type: string
permissions:
  contents: read
jobs:
  prod-image-extra-checks-main:
    name: PROD image extra checks (main)
    uses: ./.github/workflows/prod-image-extra-checks.yml
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      python-versions: "[ '${{ inputs.default-python-version }}' ]"
      default-python-version: ${{ inputs.default-python-version }}
      branch: ${{ inputs.default-branch }}
      use-uv: "false"
      upgrade-to-newer-dependencies: ${{ inputs.upgrade-to-newer-dependencies }}
      constraints-branch: ${{ inputs.constraints-branch }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
    if: inputs.default-branch == 'main' && inputs.canary-run == 'true'

  prod-image-extra-checks-release-branch:
    name: PROD image extra checks (release)
    uses: ./.github/workflows/prod-image-extra-checks.yml
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      python-versions: "[ '${{ inputs.default-python-version }}' ]"
      default-python-version: ${{ inputs.default-python-version }}
      branch: ${{ inputs.default-branch }}
      use-uv: "false"
      upgrade-to-newer-dependencies: ${{ inputs.upgrade-to-newer-dependencies }}
      constraints-branch: ${{ inputs.constraints-branch }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
    if: inputs.default-branch != 'main' && inputs.canary-run == 'true'

  test-examples-of-prod-image-building:
    timeout-minutes: 60
    name: "Test examples of PROD image building"
    runs-on: ${{ fromJSON(inputs.runners) }}
    env:
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          persist-credentials: false
      - name: "Prepare breeze & PROD image: ${{ inputs.default-python-version }}"
        uses: ./.github/actions/prepare_breeze_and_image
        with:
          platform: ${{ inputs.platform }}
          image-type: "prod"
          python: ${{ inputs.default-python-version }}
          use-uv: ${{ inputs.use-uv }}
      - name: "Test examples of PROD image building"
        env:
          GITHUB_REPOSITORY: ${{ github.repository }}
          DEFAULT_BRANCH: ${{ inputs.default-branch }}
          DEFAULT_PYTHON_VERSION: ${{ inputs.default-python-version }}
        run: "
          cd ./docker-tests && \
          TEST_IMAGE=\"ghcr.io/$GITHUB_REPOSITORY/$DEFAULT_BRANCH\
          /prod/python$DEFAULT_PYTHON_VERSION\" \
          uv run pytest tests/docker_tests/test_examples_of_prod_image_building.py -n auto --color=yes"

  test-docker-compose-quick-start:
    timeout-minutes: 60
    name: "Docker Compose quick start with PROD image verifying"
    runs-on: ${{ fromJSON(inputs.runners) }}
    env:
      PYTHON_MAJOR_MINOR_VERSION: "${{ inputs.default-python-version }}"
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          persist-credentials: false
      - name: "Prepare breeze & PROD image: ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        uses: ./.github/actions/prepare_breeze_and_image
        with:
          platform: ${{ inputs.platform }}
          image-type: "prod"
          python: ${{ env.PYTHON_MAJOR_MINOR_VERSION }}
          use-uv: ${{ inputs.use-uv }}
        id: breeze
      - name: "Test docker-compose quick start"
        run: breeze testing docker-compose-tests
