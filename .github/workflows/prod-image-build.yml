# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Build PROD images
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      build-type:
        description: >
          Name of the 'type' of the build - usually 'Regular' but other types are used to test image
          variations.
        required: true
        type: string
      upload-package-artifact:
        description: >
          Whether to upload package artifacts (true/false). If false, the job will rely on artifacts prepared
          by the main prod-image build job.
        required: true
        type: string
      target-commit-sha:
        description: "The commit SHA to checkout for the build"
        required: false
        default: ""
        type: string
      pull-request-target:
        description: "Whether we are running this from pull-request-target workflow (true/false)"
        required: false
        default: "false"
        type: string
      is-committer-build:
        description: "Whether the build is executed by committer (true/false)"
        required: false
        default: "false"
        type: string
      push-image:
        description: "Whether to push image to the registry (true/false)"
        required: true
        type: string
      upload-image-artifact:
        description: "Whether to upload docker image artifact"
        required: false
        default: "false"
        type: string
      debian-version:
        description: "Base Debian distribution to use for the build (bookworm)"
        type: string
        default: "bookworm"
      install-mysql-client-type:
        description: "MySQL client type to use during build (mariadb/mysql)"
        type: string
        default: "mariadb"
      use-uv:
        description: "Whether to use uv to build the image (true/false)"
        required: true
        type: string
      python-versions:
        description: "JSON-formatted array of Python versions to build images from"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      branch:
        description: "Branch used to run the CI jobs in (main/v*_*_test)."
        required: true
        type: string
      constraints-branch:
        description: "Branch used to construct constraints URL from."
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to attempt to upgrade image to newer dependencies (true/false)"
        required: true
        type: string
      docker-cache:
        description: "Docker cache specification to build the image (registry, local, disabled)."
        required: true
        type: string
      disable-airflow-repo-cache:
        description: "Disable airflow repo cache read from main."
        required: true
        type: string
      prod-image-build:
        description: "Whether this is a prod-image build (true/false)"
        required: true
        type: string
permissions:
  contents: read
jobs:
  build-prod-packages:
    name: "Build Airflow and provider distributions"
    timeout-minutes: 10
    runs-on: ${{ fromJSON(inputs.runners) }}
    if: inputs.prod-image-build == 'true'
    env:
      PYTHON_MAJOR_MINOR_VERSION: "${{ inputs.default-python-version }}"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
        if: inputs.upload-package-artifact == 'true'
      - name: "Checkout target branch"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
        if: inputs.upload-package-artifact == 'true'
      - name: "Cleanup dist and context file"
        shell: bash
        run: rm -fv ./dist/* ./docker-context-files/*
        if: inputs.upload-package-artifact == 'true'
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
        if: inputs.upload-package-artifact == 'true'
      - name: "Prepare providers packages - all providers built from sources"
        shell: bash
        run: >
          breeze release-management prepare-provider-distributions
          --distributions-list-file ./prod_image_installed_providers.txt
          --distribution-format wheel --include-not-ready-providers --skip-tag-check
        if: >
          inputs.upload-package-artifact == 'true' && inputs.branch == 'main'
      - name: "Prepare providers packages with only new versions of providers"
        shell: bash
        run: >
          breeze release-management prepare-provider-distributions
          --distributions-list-file ./prod_image_installed_providers.txt
          --distribution-format wheel --include-not-ready-providers
        if: >
          inputs.upload-package-artifact == 'true' && inputs.branch != 'main'
      - name: "Prepare airflow package"
        shell: bash
        run: >
          breeze release-management prepare-airflow-distributions --distribution-format wheel
        if: inputs.upload-package-artifact == 'true'
      - name: "Prepare task-sdk package"
        shell: bash
        run: >
          breeze release-management prepare-task-sdk-distributions --distribution-format wheel
        if: inputs.upload-package-artifact == 'true'
      - name: "Prepare airflow-ctl package"
        shell: bash
        run: >
          breeze release-management prepare-airflow-ctl-distributions --distribution-format wheel
        if: inputs.upload-package-artifact == 'true'
      - name: "Upload prepared packages as artifacts"
        uses: actions/upload-artifact@v4
        with:
          name: prod-packages
          path: ./dist
          retention-days: 7
          if-no-files-found: error
        if: inputs.upload-package-artifact == 'true'

  build-prod-images:
    strategy:
      fail-fast: false
      matrix:
        python-version: ${{ fromJSON(inputs.python-versions) || fromJSON('[""]') }}
    timeout-minutes: 80
    name: "Build PROD ${{ inputs.build-type }} image ${{ matrix.python-version }}"
    runs-on: ${{ fromJSON(inputs.runners) }}
    needs:
      - build-prod-packages
    env:
      BACKEND: sqlite
      PYTHON_MAJOR_MINOR_VERSION: "${{ matrix.python-version }}"
      DEFAULT_BRANCH: ${{ inputs.branch }}
      DEFAULT_CONSTRAINTS_BRANCH: ${{ inputs.constraints-branch }}
      INCLUDE_NOT_READY_PROVIDERS: "true"
      # You can override CONSTRAINTS_GITHUB_REPOSITORY by setting secret in your repo but by default the
      # Airflow one is going to be used
      CONSTRAINTS_GITHUB_REPOSITORY: >-
        ${{ secrets.CONSTRAINTS_GITHUB_REPOSITORY != '' &&
        secrets.CONSTRAINTS_GITHUB_REPOSITORY || 'apache/airflow' }}
      # In builds from forks, this token is read-only. For scheduled/direct push it is WRITE one
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      PLATFORM: ${{ inputs.platform }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout target branch"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
      - name: "Cleanup dist and context file"
        shell: bash
        run: rm -fv ./dist/* ./docker-context-files/*
      - name: "Download packages prepared as artifacts"
        uses: actions/download-artifact@v4
        with:
          name: prod-packages
          path: ./docker-context-files
      - name: "Show downloaded packages"
        run: ls -la ./docker-context-files
      - name: "Download constraints"
        uses: actions/download-artifact@v4
        with:
          name: constraints
          path: ./docker-context-files
      - name: "Show constraints"
        run: |
          for file in ./docker-context-files/constraints*/constraints*.txt
          do
            echo "=== ${file} ==="
            echo
            cat ${file}
            echo
            echo "=== END ${file} ==="
          done
      - name: "Login to ghcr.io"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ACTOR: ${{ github.actor }}
        run: echo "${GITHUB_TOKEN}" | docker login ghcr.io -u ${ACTOR} --password-stdin
      - name: "Build PROD images w/ source providers ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        shell: bash
        run: >
          breeze prod-image build
          --builder airflow_cache
          --commit-sha "${{ github.sha }}"
          --install-distributions-from-context
          --airflow-constraints-mode constraints-source-providers
          --use-constraints-for-context-distributions
        env:
          PUSH: ${{ inputs.push-image }}
          DOCKER_CACHE: ${{ inputs.docker-cache }}
          DISABLE_AIRFLOW_REPO_CACHE: ${{ inputs.disable-airflow-repo-cache }}
          DEBIAN_VERSION: ${{ inputs.debian-version }}
          INSTALL_MYSQL_CLIENT_TYPE: ${{ inputs.install-mysql-client-type }}
          UPGRADE_TO_NEWER_DEPENDENCIES: ${{ inputs.upgrade-to-newer-dependencies }}
          INCLUDE_NOT_READY_PROVIDERS: "true"
      - name: "Verify PROD image ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        run: breeze prod-image verify
      - name: "Export PROD docker image ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        env:
          PLATFORM: ${{ inputs.platform }}
        run: >
          breeze prod-image save --platform "${PLATFORM}" --image-file-dir "/mnt"
        if: inputs.upload-image-artifact == 'true'
      - name: "Stash PROD docker image ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        uses: apache/infrastructure-actions/stash/save@1c35b5ccf8fba5d4c3fdf25a045ca91aa0cbc468
        with:
          key: prod-image-save-v3-${{ inputs.platform }}-${{ env.PYTHON_MAJOR_MINOR_VERSION }}
          path: "/mnt/prod-image-save-*-${{ env.PYTHON_MAJOR_MINOR_VERSION }}.tar"
          if-no-files-found: 'error'
          retention-days: '2'
        if: inputs.upload-image-artifact == 'true'
