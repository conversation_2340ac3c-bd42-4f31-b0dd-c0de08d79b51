# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: PROD images extra checks
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      python-versions:
        description: "JSON-formatted array of Python versions to build images from"
        required: true
        type: string
      default-python-version:
        description: "Default Python version to use for the image"
        required: true
        type: string
      branch:
        description: "Branch used to run the CI jobs in (main/v*_*_test)."
        required: true
        type: string
      use-uv:
        description: "Whether to use uv to build the image (true/false)"
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to attempt to upgrade image to newer dependencies (false/RANDOM_VALUE)"
        required: true
        type: string
      constraints-branch:
        description: "Branch used to construct constraints URL from."
        required: true
        type: string
      docker-cache:
        description: "Docker cache specification to build the image (registry, local, disabled)."
        required: true
        type: string
      disable-airflow-repo-cache:
        description: "Disable airflow repo cache read from main."
        required: true
        type: string
permissions:
  contents: read
jobs:
  myssql-client-image:
    uses: ./.github/workflows/prod-image-build.yml
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      build-type: "MySQL Client"
      upload-image-artifact: "false"
      upload-package-artifact: "false"
      install-mysql-client-type: "mysql"
      python-versions: ${{ inputs.python-versions }}
      default-python-version: ${{ inputs.default-python-version }}
      branch: ${{ inputs.branch }}
      # Always build images during the extra checks and never push them
      push-image: "false"
      use-uv: ${{ inputs.use-uv }}
      upgrade-to-newer-dependencies: ${{ inputs.upgrade-to-newer-dependencies }}
      constraints-branch: ${{ inputs.constraints-branch }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
      prod-image-build: "true"

  pip-image:
    uses: ./.github/workflows/prod-image-build.yml
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      build-type: "pip"
      upload-image-artifact: "false"
      upload-package-artifact: "false"
      install-mysql-client-type: "mysql"
      python-versions: ${{ inputs.python-versions }}
      default-python-version: ${{ inputs.default-python-version }}
      branch: ${{ inputs.branch }}
      # Always build images during the extra checks and never push them
      push-image: "false"
      use-uv: "false"
      upgrade-to-newer-dependencies: ${{ inputs.upgrade-to-newer-dependencies }}
      constraints-branch: ${{ inputs.constraints-branch }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
      prod-image-build: "true"
