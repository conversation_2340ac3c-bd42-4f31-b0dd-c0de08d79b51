# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Special tests
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      default-branch:
        description: "The default branch for the repository"
        required: true
        type: string
      core-test-types-list-as-strings-in-json:
        description: "The list of core test types to run separated by spaces"
        required: true
        type: string
      providers-test-types-list-as-strings-in-json:
        description: "The list of providers test types to run separated by spaces"
        required: true
        type: string
      run-coverage:
        description: "Whether to run coverage or not (true/false)"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      excluded-providers-as-string:
        description: "Excluded providers (per Python version) as json string"
        required: true
        type: string
      python-versions:
        description: "The list of python versions (stringified JSON array) to run the tests on."
        required: true
        type: string
      default-postgres-version:
        description: "The default version of the postgres to use"
        required: true
        type: string
      canary-run:
        description: "Whether to run canary tests or not (true/false)"
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to upgrade to newer dependencies or not (true/false)"
        required: true
        type: string
      include-success-outputs:
        description: "Whether to include success outputs or not (true/false)"
        required: true
        type: string
      debug-resources:
        description: "Whether to debug resources or not (true/false)"
        required: true
        type: string
      skip-providers-tests:
        description: "Whether to skip providers tests or not (true/false)"
        required: true
        type: string
      use-uv:
        description: "Whether to use uv or not (true/false)"
        required: true
        type: string
permissions:
  contents: read
jobs:
  tests-min-sqlalchemy:
    name: "Min SQLAlchemy test: core"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      downgrade-sqlalchemy: "true"
      test-name: "MinSQLAlchemy-Postgres"
      test-scope: "DB"
      test-group: "core"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.core-test-types-list-as-strings-in-json }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-min-sqlalchemy-providers:
    name: "Min SQLAlchemy test: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      downgrade-sqlalchemy: "true"
      test-name: "MinSQLAlchemy-Postgres"
      test-scope: "DB"
      test-group: "providers"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.providers-test-types-list-as-strings-in-json }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-boto-core:
    name: "Latest Boto test: core"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      upgrade-boto: "true"
      test-name: "LatestBoto-Postgres"
      test-scope: "All"
      test-group: "core"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-boto-providers:
    name: "Latest Boto test: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      upgrade-boto: "true"
      test-name: "LatestBoto-Postgres"
      test-scope: "All"
      test-group: "providers"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}


  tests-pendulum-2-core:
    name: "Pendulum2 test: core"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      downgrade-pendulum: "true"
      test-name: "Pendulum2-Postgres"
      test-scope: "All"
      test-group: "core"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-pendulum-2-providers:
    name: "Pendulum2 test: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      downgrade-pendulum: "true"
      test-name: "Pendulum2-Postgres"
      test-scope: "All"
      test-group: "providers"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-quarantined-core:
    name: "Quarantined test: core"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      test-name: "Postgres"
      test-scope: "Quarantined"
      test-group: "core"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-quarantined-providers:
    name: "Quarantined test: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      test-name: "Postgres"
      test-scope: "Quarantined"
      test-group: "providers"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}

  tests-system-core:
    name: "System test: ${{ matrix.test-group }}"
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      test-name: "SystemTest"
      test-scope: "System"
      test-group: "core"
      backend: "postgres"
      python-versions: "['${{ inputs.default-python-version }}']"
      backend-versions: "['${{ inputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ inputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: ${{ inputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      run-coverage: ${{ inputs.run-coverage }}
      debug-resources: ${{ inputs.debug-resources }}
      skip-providers-tests: ${{ inputs.skip-providers-tests }}
      use-uv: ${{ inputs.use-uv }}
