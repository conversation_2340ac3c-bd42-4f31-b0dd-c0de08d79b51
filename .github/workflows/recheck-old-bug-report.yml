# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
# https://github.com/actions/stale
name: 'Recheck old bug reports'
on:  # yamllint disable-line rule:truthy
  schedule:
    - cron: '0 7 * * *'
permissions:
  # All other permissions are set to none
  issues: write
jobs:
  recheck-old-bug-report:
    runs-on: ["ubuntu-22.04"]
    steps:
      - uses: actions/stale@v9
        with:
          only-issue-labels: 'kind:bug'
          stale-issue-label: 'Stale Bug Report'
          days-before-issue-stale: 365
          days-before-issue-close: 30
          # Stale bot does not support defining to
          # scan only issues: https://github.com/actions/stale/issues/837
          # To avoid this job scanning also PRs and setting the defaults of the bot to them,
          # we set high number of days thus effectively this job will not do any changes on PRs
          # Possible workaround with -1 as mentioned in
          # https://github.com/actions/stale/issues/1112#issuecomment-**********
          days-before-pr-stale: -1
          days-before-pr-close: -1
          remove-stale-when-updated: false
          remove-issue-stale-when-updated: true
          labels-to-add-when-unstale: 'needs-triage'
          labels-to-remove-when-unstale: 'Stale Bug Report'
          stale-issue-message: >
            This issue has been automatically marked as stale because it has been open for 365 days
            without any activity. There has been several Airflow releases since last activity on this issue.
            Kindly asking to recheck the report against latest Airflow version and let us know
            if the issue is reproducible. The issue will be closed in next 30 days if no further activity
            occurs from the issue author.
          close-issue-message: >
            This issue has been closed because it has not received response from the issue author.
