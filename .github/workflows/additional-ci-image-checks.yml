# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Additional CI image checks
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      python-versions:
        description: "The list of python versions (stringified JSON array) to run the tests on."
        required: true
        type: string
      branch:
        description: "Branch used to run the CI jobs in (main/v*_*_test)."
        required: true
        type: string
      constraints-branch:
        description: "Branch used to get constraints from"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to upgrade to newer dependencies (true/false)"
        required: true
        type: string
      skip-pre-commits:
        description: "Whether to skip pre-commits (true/false)"
        required: true
        type: string
      docker-cache:
        description: "Docker cache specification to build the image (registry, local, disabled)."
        required: true
        type: string
      disable-airflow-repo-cache:
        description: "Disable airflow repo cache read from main."
        required: true
        type: string
      canary-run:
        description: "Whether this is a canary run (true/false)"
        required: true
        type: string
      latest-versions-only:
        description: "Whether to run only latest versions (true/false)"
        required: true
        type: string
      include-success-outputs:
        description: "Whether to include success outputs (true/false)"
        required: true
        type: string
      debug-resources:
        description: "Whether to debug resources (true/false)"
        required: true
        type: string
      use-uv:
        description: "Whether to use uv to build the image (true/false)"
        required: true
        type: string
permissions:
  contents: read
jobs:
  # Push early BuildX cache to GitHub Registry in Apache repository, This cache does not wait for all the
  # tests to complete - it is run very early in the build process for "main" merges in order to refresh
  # cache using the current constraints. This will speed up cache refresh in cases when pyproject.toml
  # changes or in case of Dockerfile changes. Failure in this step is not a problem (at most it will
  # delay cache refresh. It does not attempt to upgrade to newer dependencies.
  # We only push CI cache as PROD cache usually does not gain as much from fresh cache because
  # it uses prepared airflow and provider distributions that invalidate the cache anyway most of the time
  push-early-buildx-cache-to-github-registry:
    name: Push Early Image Cache
    uses: ./.github/workflows/push-image-cache.yml
    permissions:
      contents: read
      # This write is only given here for `push` events from "apache/airflow" repo. It is not given for PRs
      # from forks. This is to prevent malicious PRs from creating images in the "apache/airflow" repo.
      packages: write
    with:
      runners: ${{ inputs.runners }}
      cache-type: "Early"
      include-prod-images: "false"
      push-latest-images: "false"
      platform: ${{ inputs.platform }}
      python-versions: ${{ inputs.python-versions }}
      branch: ${{ inputs.branch }}
      constraints-branch: ${{ inputs.constraints-branch }}
      use-uv: ${{ inputs.use-uv }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
    if: >
      inputs.canary-run == 'true' &&
      (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch')

  # Check that after earlier cache push, breeze command will build quickly
  check-that-image-builds-quickly:
    timeout-minutes: 11
    name: Check that image builds quickly
    runs-on: ${{ fromJSON(inputs.runners) }}
    env:
      UPGRADE_TO_NEWER_DEPENDENCIES: false
      PYTHON_MAJOR_MINOR_VERSION: ${{ inputs.default-python-version }}
      PYTHON_VERSION: ${{ inputs.default-python-version }}
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
      PLATFORM: ${{ inputs.platform }}
    if: inputs.branch == 'main'
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
      - name: "Login to ghcr.io"
        env:
          actor: ${{ github.actor }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: echo "$GITHUB_TOKEN" | docker login ghcr.io -u "$actor" --password-stdin
      - name: "Check that image builds quickly"
        run: breeze shell --max-time 600 --platform "${PLATFORM}"
