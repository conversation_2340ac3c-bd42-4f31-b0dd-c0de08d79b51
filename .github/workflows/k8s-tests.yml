# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: K8s tests
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      python-versions-list-as-string:
        description: "List of Python versions to test: space separated string"
        required: true
        type: string
      kubernetes-combos:
        description: "Array of combinations of Kubernetes and Python versions to test"
        required: true
        type: string
      include-success-outputs:
        description: "Whether to include success outputs"
        required: true
        type: string
      use-uv:
        description: "Whether to use uv"
        required: true
        type: string
      debug-resources:
        description: "Whether to debug resources"
        required: true
        type: string
permissions:
  contents: read
jobs:
  tests-kubernetes:
    timeout-minutes: 60
    name: "K8S System:${{ matrix.executor }}-${{ matrix.kubernetes-combo }}-${{ matrix.use-standard-naming }}"
    runs-on: ${{ fromJSON(inputs.runners) }}
    strategy:
      matrix:
        executor: [KubernetesExecutor, CeleryExecutor, LocalExecutor]
        use-standard-naming: [true, false]
        kubernetes-combo: ${{ fromJSON(inputs.kubernetes-combos) }}
      fail-fast: false
    env:
      DEBUG_RESOURCES: ${{ inputs.debug-resources }}
      INCLUDE_SUCCESS_OUTPUTS: ${{ inputs.include-success-outputs }}
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Prepare PYTHON_MAJOR_MINOR_VERSION and KUBERNETES_VERSION"
        id: prepare-versions
        env:
          KUBERNETES_COMBO: ${{ matrix.kubernetes-combo }}
        run: |
          echo "PYTHON_MAJOR_MINOR_VERSION=${KUBERNETES_COMBO}" | sed 's/-.*//' >> $GITHUB_ENV
          echo "KUBERNETES_VERSION=${KUBERNETES_COMBO}" | sed 's/=[^-]*-/=/'  >> $GITHUB_ENV
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      # env.PYTHON_MAJOR_MINOR_VERSION, env.KUBERNETES_VERSION are set in the previous
      # step id: prepare-versions
      - name: "Prepare breeze & PROD image: ${{ env.PYTHON_MAJOR_MINOR_VERSION }}"
        uses: ./.github/actions/prepare_breeze_and_image
        with:
          platform: ${{ inputs.platform }}
          image-type: "prod"
          python: ${{ env.PYTHON_MAJOR_MINOR_VERSION }}
          use-uv: ${{ inputs.use-uv }}
        id: breeze
      # preparing k8s environment with uv takes < 15 seconds with `uv` - there is no point in caching it.
      - name: "\
          Run complete K8S tests ${{ matrix.executor }}-${{ env.PYTHON_MAJOR_MINOR_VERSION }}-\
          ${{env.KUBERNETES_VERSION}}-${{ matrix.use-standard-naming }}"
        run: breeze k8s run-complete-tests --upgrade --no-copy-local-sources
        env:
          EXECUTOR: ${{ matrix.executor }}
          USE_STANDARD_NAMING: ${{ matrix.use-standard-naming }}
          VERBOSE: "false"
      - name: "\
          Print logs ${{ matrix.executor }}-${{ matrix.kubernetes-combo }}-\
          ${{ matrix.use-standard-naming }}"
        run: |
          for file in `find /tmp/kind_logs_*/ -type f` ; do
            echo "::group::${file}"
            cat $file
            echo "::endgroup::"
          done
        if: failure() || cancelled() || inputs.include-success-outputs == 'true'
      - name: "\
          Upload KinD logs ${{ matrix.executor }}-${{ matrix.kubernetes-combo }}-\
          ${{ matrix.use-standard-naming }}"
        uses: actions/upload-artifact@v4
        with:
          name: "\
            kind-logs-${{ matrix.kubernetes-combo }}-${{ matrix.executor }}-\
            ${{ matrix.use-standard-naming }}"
          path: /tmp/kind_logs_*
          retention-days: '7'
        if: failure() || cancelled() || inputs.include-success-outputs == 'true'
      - name: "Delete clusters just in case they are left"
        run: breeze k8s delete-cluster --all
        if: always()
