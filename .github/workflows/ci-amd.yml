# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Tests AMD
on:  # yamllint disable-line rule:truthy
  schedule:
    - cron: '28 1,7,13,19 * * *'
  push:
    branches:
      - v[0-9]+-[0-9]+-test
      - providers-[a-z]+-?[a-z]*/v[0-9]+-[0-9]+
  pull_request:
    branches:
      - main
      - v[0-9]+-[0-9]+-test
      - v[0-9]+-[0-9]+-stable
      - providers-[a-z]+-?[a-z]*/v[0-9]+-[0-9]+
    types: [opened, reopened, synchronize, ready_for_review]
  workflow_dispatch:
permissions:
  # All other permissions are set to none by default
  contents: read
env:
  GITHUB_REPOSITORY: ${{ github.repository }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  GITHUB_USERNAME: ${{ github.actor }}
  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
  VERBOSE: "true"

concurrency:
  group: ci-amd-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:

  build-info:
    name: "Build info"
    # At build-info stage we do not yet have outputs so we need to hard-code the runs-on to public runners
    runs-on: ["ubuntu-22.04"]
    env:
      GITHUB_CONTEXT: ${{ toJson(github) }}
    outputs:
      all-python-versions-list-as-string: >-
        ${{ steps.selective-checks.outputs.all-python-versions-list-as-string }}
      basic-checks-only: ${{ steps.selective-checks.outputs.basic-checks-only }}
      canary-run: ${{ steps.source-run-info.outputs.canary-run }}
      ci-image-build: ${{ steps.selective-checks.outputs.ci-image-build }}
      core-test-types-list-as-strings-in-json: >-
        ${{ steps.selective-checks.outputs.core-test-types-list-as-strings-in-json }}
      debug-resources: ${{ steps.selective-checks.outputs.debug-resources }}
      default-branch: ${{ steps.selective-checks.outputs.default-branch }}
      default-constraints-branch: ${{ steps.selective-checks.outputs.default-constraints-branch }}
      default-helm-version: ${{ steps.selective-checks.outputs.default-helm-version }}
      default-kind-version: ${{ steps.selective-checks.outputs.default-kind-version }}
      default-kubernetes-version: ${{ steps.selective-checks.outputs.default-kubernetes-version }}
      default-mysql-version: ${{ steps.selective-checks.outputs.default-mysql-version }}
      default-postgres-version: ${{ steps.selective-checks.outputs.default-postgres-version }}
      default-python-version: ${{ steps.selective-checks.outputs.default-python-version }}
      disable-airflow-repo-cache: ${{ steps.selective-checks.outputs.disable-airflow-repo-cache }}
      docker-cache: ${{ steps.selective-checks.outputs.docker-cache }}
      docs-build: ${{ steps.selective-checks.outputs.docs-build }}
      docs-list-as-string: ${{ steps.selective-checks.outputs.docs-list-as-string }}
      excluded-providers-as-string: ${{ steps.selective-checks.outputs.excluded-providers-as-string }}
      force-pip: ${{ steps.selective-checks.outputs.force-pip }}
      full-tests-needed: ${{ steps.selective-checks.outputs.full-tests-needed }}
      has-migrations: ${{ steps.selective-checks.outputs.has-migrations }}
      helm-test-packages: ${{ steps.selective-checks.outputs.helm-test-packages }}
      include-success-outputs: ${{ steps.selective-checks.outputs.include-success-outputs }}
      individual-providers-test-types-list-as-strings-in-json: >-
        ${{ steps.selective-checks.outputs.individual-providers-test-types-list-as-strings-in-json }}
      kubernetes-combos: ${{ steps.selective-checks.outputs.kubernetes-combos }}
      kubernetes-combos-list-as-string: >-
        ${{ steps.selective-checks.outputs.kubernetes-combos-list-as-string }}
      kubernetes-versions-list-as-string: >-
        ${{ steps.selective-checks.outputs.kubernetes-versions-list-as-string }}
      latest-versions-only: ${{ steps.selective-checks.outputs.latest-versions-only }}
      mypy-checks: ${{ steps.selective-checks.outputs.mypy-checks }}
      mysql-exclude: ${{ steps.selective-checks.outputs.mysql-exclude }}
      mysql-versions: ${{ steps.selective-checks.outputs.mysql-versions }}
      needs-api-codegen: ${{ steps.selective-checks.outputs.needs-api-codegen }}
      needs-api-tests: ${{ steps.selective-checks.outputs.needs-api-tests }}
      needs-helm-tests: ${{ steps.selective-checks.outputs.needs-helm-tests }}
      needs-mypy: ${{ steps.selective-checks.outputs.needs-mypy }}
      only-new-ui-files: ${{ steps.selective-checks.outputs.only-new-ui-files }}
      postgres-exclude: ${{ steps.selective-checks.outputs.postgres-exclude }}
      postgres-versions: ${{ steps.selective-checks.outputs.postgres-versions }}
      prod-image-build: ${{ steps.selective-checks.outputs.prod-image-build }}
      # yamllint disable rule:line-length
      providers-compatibility-tests-matrix: >
        ${{ steps.selective-checks.outputs.providers-compatibility-tests-matrix }}
      providers-test-types-list-as-strings-in-json: >-
        ${{ steps.selective-checks.outputs.providers-test-types-list-as-strings-in-json }}
      pull-request-labels: ${{ steps.source-run-info.outputs.pr-labels }}
      python-versions-list-as-string: ${{ steps.selective-checks.outputs.python-versions-list-as-string }}
      python-versions: ${{ steps.selective-checks.outputs.python-versions }}
      run-amazon-tests: ${{ steps.selective-checks.outputs.run-amazon-tests }}
      run-airflow-ctl-tests: ${{ steps.selective-checks.outputs.run-airflow-ctl-tests }}
      run-coverage: ${{ steps.source-run-info.outputs.run-coverage }}
      run-kubernetes-tests: ${{ steps.selective-checks.outputs.run-kubernetes-tests }}
      run-task-sdk-tests: ${{ steps.selective-checks.outputs.run-task-sdk-tests }}
      run-system-tests: ${{ steps.selective-checks.outputs.run-system-tests }}
      run-tests: ${{ steps.selective-checks.outputs.run-tests }}
      run-ui-tests: ${{ steps.selective-checks.outputs.run-ui-tests }}
      run-www-tests: ${{ steps.selective-checks.outputs.run-www-tests }}
      amd-runners: ${{ steps.selective-checks.outputs.amd-runners }}
      arm-runners: ${{ steps.selective-checks.outputs.arm-runners }}
      selected-providers-list-as-string: >-
        ${{ steps.selective-checks.outputs.selected-providers-list-as-string }}
      skip-pre-commits: ${{ steps.selective-checks.outputs.skip-pre-commits }}
      skip-providers-tests: ${{ steps.selective-checks.outputs.skip-providers-tests }}
      source-head-repo: ${{ steps.source-run-info.outputs.source-head-repo }}
      sqlite-exclude: ${{ steps.selective-checks.outputs.sqlite-exclude }}
      testable-core-integrations: ${{ steps.selective-checks.outputs.testable-core-integrations }}
      testable-providers-integrations: ${{ steps.selective-checks.outputs.testable-providers-integrations }}
      use-uv: ${{ steps.selective-checks.outputs.force-pip == 'true' && 'false' || 'true' }}
      upgrade-to-newer-dependencies: ${{ steps.selective-checks.outputs.upgrade-to-newer-dependencies }}
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: Fetch incoming commit ${{ github.sha }} with its parent
        uses: actions/checkout@v4
        with:
          ref: ${{ github.sha }}
          fetch-depth: 2
          persist-credentials: false
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
        id: breeze
      - name: "Get information about the Workflow"
        id: source-run-info
        run: breeze ci get-workflow-info 2>> ${GITHUB_OUTPUT}
        env:
          SKIP_BREEZE_SELF_UPGRADE_CHECK: "true"
      - name: Selective checks
        id: selective-checks
        env:
          PR_LABELS: "${{ steps.source-run-info.outputs.pr-labels }}"
          COMMIT_REF: "${{ github.sha }}"
          VERBOSE: "false"
        run: breeze ci selective-check 2>> ${GITHUB_OUTPUT}
      - name: env
        run: printenv
        env:
          PR_LABELS: ${{ steps.source-run-info.outputs.pr-labels }}
          GITHUB_CONTEXT: ${{ toJson(github) }}

  basic-tests:
    name: "Basic tests"
    needs: [build-info]
    uses: ./.github/workflows/basic-tests.yml
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      run-ui-tests: ${{needs.build-info.outputs.run-ui-tests}}
      run-www-tests: ${{needs.build-info.outputs.run-www-tests}}
      needs-api-codegen: ${{needs.build-info.outputs.needs-api-codegen}}
      default-python-version: ${{needs.build-info.outputs.default-python-version}}
      basic-checks-only: ${{needs.build-info.outputs.basic-checks-only}}
      skip-pre-commits: ${{needs.build-info.outputs.skip-pre-commits}}
      canary-run: ${{needs.build-info.outputs.canary-run}}
      latest-versions-only: ${{needs.build-info.outputs.latest-versions-only}}
      use-uv: ${{needs.build-info.outputs.use-uv}}

  build-ci-images:
    name: Build CI images
    needs: [build-info]
    uses: ./.github/workflows/ci-image-build.yml
    permissions:
      contents: read
      # This write is only given here for `push` events from "apache/airflow" repo. It is not given for PRs
      # from forks. This is to prevent malicious PRs from creating images in the "apache/airflow" repo.
      packages: write
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      push-image: "false"
      upload-image-artifact: "true"
      upload-mount-cache-artifact: ${{ needs.build-info.outputs.canary-run }}
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      branch: ${{ needs.build-info.outputs.default-branch }}
      constraints-branch: ${{ needs.build-info.outputs.default-constraints-branch }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      docker-cache: ${{ needs.build-info.outputs.docker-cache }}
      disable-airflow-repo-cache: ${{ needs.build-info.outputs.disable-airflow-repo-cache }}
    if: needs.build-info.outputs.ci-image-build == 'true'

  additional-ci-image-checks:
    name: "Additional CI image checks"
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/additional-ci-image-checks.yml
    permissions:
      contents: read
      packages: write
      id-token: write
    if: needs.build-info.outputs.canary-run == 'true'
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      branch: ${{ needs.build-info.outputs.default-branch }}
      constraints-branch: ${{ needs.build-info.outputs.default-constraints-branch }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      skip-pre-commits: ${{ needs.build-info.outputs.skip-pre-commits }}
      docker-cache: ${{ needs.build-info.outputs.docker-cache }}
      disable-airflow-repo-cache: ${{ needs.build-info.outputs.disable-airflow-repo-cache }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      latest-versions-only: ${{ needs.build-info.outputs.latest-versions-only }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  generate-constraints:
    name: "Generate constraints"
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/generate-constraints.yml
    if: needs.build-info.outputs.ci-image-build == 'true'
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      python-versions-list-as-string: ${{ needs.build-info.outputs.python-versions-list-as-string }}
      # generate no providers constraints only in canary builds - they take quite some time to generate
      # they are not needed for regular builds, they are only needed to update constraints in canaries
      generate-no-providers-constraints: ${{ needs.build-info.outputs.canary-run }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  ci-image-checks:
    name: "CI image checks"
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/ci-image-checks.yml
    permissions:
      id-token: write
      contents: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      needs-mypy: ${{ needs.build-info.outputs.needs-mypy }}
      mypy-checks: ${{ needs.build-info.outputs.mypy-checks }}
      python-versions-list-as-string: ${{ needs.build-info.outputs.python-versions-list-as-string }}
      branch: ${{ needs.build-info.outputs.default-branch }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      docs-list-as-string: ${{ needs.build-info.outputs.docs-list-as-string }}
      latest-versions-only: ${{ needs.build-info.outputs.latest-versions-only }}
      basic-checks-only: ${{ needs.build-info.outputs.basic-checks-only }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      skip-pre-commits: ${{ needs.build-info.outputs.skip-pre-commits }}
      ci-image-build: ${{ needs.build-info.outputs.ci-image-build }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      docs-build: ${{ needs.build-info.outputs.docs-build }}
      needs-api-codegen: ${{ needs.build-info.outputs.needs-api-codegen }}
      default-postgres-version: ${{ needs.build-info.outputs.default-postgres-version }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    secrets:
      DOCS_AWS_ACCESS_KEY_ID: ${{ secrets.DOCS_AWS_ACCESS_KEY_ID }}
      DOCS_AWS_SECRET_ACCESS_KEY: ${{ secrets.DOCS_AWS_SECRET_ACCESS_KEY }}

  providers:
    name: "provider distributions tests"
    uses: ./.github/workflows/test-providers.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    if: >
      needs.build-info.outputs.skip-providers-tests != 'true' &&
      needs.build-info.outputs.latest-versions-only != 'true' &&
      needs.build-info.outputs.run-tests == 'true'
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      selected-providers-list-as-string: ${{ needs.build-info.outputs.selected-providers-list-as-string }}
      # yamllint disable rule:line-length
      providers-compatibility-tests-matrix: >
        ${{ needs.build-info.outputs.providers-compatibility-tests-matrix }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      providers-test-types-list-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  tests-helm:
    name: "Helm tests"
    uses: ./.github/workflows/helm-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      helm-test-packages: ${{ needs.build-info.outputs.helm-test-packages }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: >
      needs.build-info.outputs.needs-helm-tests == 'true' &&
      needs.build-info.outputs.default-branch == 'main' &&
      needs.build-info.outputs.latest-versions-only != 'true'

  tests-postgres-core:
    name: "Postgres tests: core"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "postgres"
      test-name: "Postgres"
      test-scope: "DB"
      test-group: "core"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: ${{ needs.build-info.outputs.postgres-versions }}
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.postgres-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-migration-tests: "true"
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-postgres-providers:
    name: "Postgres tests: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "postgres"
      test-name: "Postgres"
      test-scope: "DB"
      test-group: "providers"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: ${{ needs.build-info.outputs.postgres-versions }}
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.postgres-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-migration-tests: "true"
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-mysql-core:
    name: "MySQL tests: core"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "mysql"
      test-name: "MySQL"
      test-scope: "DB"
      test-group: "core"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: ${{ needs.build-info.outputs.mysql-versions }}
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.mysql-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      run-migration-tests: "true"
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-mysql-providers:
    name: "MySQL tests: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "mysql"
      test-name: "MySQL"
      test-scope: "DB"
      test-group: "providers"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: ${{ needs.build-info.outputs.mysql-versions }}
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.mysql-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      run-migration-tests: "true"
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'


  tests-sqlite-core:
    name: "Sqlite tests: core"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "sqlite"
      test-name: "Sqlite"
      test-name-separator: ""
      test-scope: "DB"
      test-group: "core"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      # No versions for sqlite
      backend-versions: "['']"
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.sqlite-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      run-migration-tests: "true"
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-sqlite-providers:
    name: "Sqlite tests: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "sqlite"
      test-name: "Sqlite"
      test-name-separator: ""
      test-scope: "DB"
      test-group: "providers"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      # No versions for sqlite
      backend-versions: "['']"
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.sqlite-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      run-migration-tests: "true"
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'


  tests-non-db-core:
    name: "Non-DB tests: core"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "sqlite"
      test-name: ""
      test-name-separator: ""
      test-scope: "Non-DB"
      test-group: "core"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      # No versions for non-db
      backend-versions: "['']"
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.sqlite-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-non-db-providers:
    name: "Non-DB tests: providers"
    uses: ./.github/workflows/run-unit-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      backend: "sqlite"
      test-name: ""
      test-name-separator: ""
      test-scope: "Non-DB"
      test-group: "providers"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      # No versions for non-db
      backend-versions: "['']"
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: ${{ needs.build-info.outputs.sqlite-exclude }}
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-special:
    name: "Special tests"
    uses: ./.github/workflows/special-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    if: >
      needs.build-info.outputs.run-tests == 'true' &&
      (needs.build-info.outputs.canary-run == 'true' ||
       needs.build-info.outputs.upgrade-to-newer-dependencies != 'false' ||
       needs.build-info.outputs.full-tests-needed == 'true')
    with:
      default-branch: ${{ needs.build-info.outputs.default-branch }}
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      core-test-types-list-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      providers-test-types-list-as-strings-in-json: >
        ${{ needs.build-info.outputs.providers-test-types-list-as-strings-in-json }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      default-postgres-version: ${{ needs.build-info.outputs.default-postgres-version }}
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  tests-integration-system:
    name: Integration and System Tests
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/integration-system-tests.yml
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      testable-core-integrations: ${{ needs.build-info.outputs.testable-core-integrations }}
      testable-providers-integrations: ${{ needs.build-info.outputs.testable-providers-integrations }}
      run-system-tests: ${{ needs.build-info.outputs.run-tests }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      default-postgres-version: ${{ needs.build-info.outputs.default-postgres-version }}
      default-mysql-version: ${{ needs.build-info.outputs.default-mysql-version }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.run-tests == 'true'

  tests-with-lowest-direct-resolution-core:
    name: "Low dep tests:core"
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    if: >
      needs.build-info.outputs.run-tests == 'true'
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      test-name: "LowestDeps"
      force-lowest-dependencies: "true"
      test-scope: "All"
      test-group: "core"
      backend: "sqlite"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: "['${{ needs.build-info.outputs.default-postgres-version }}']"
      excluded-providers-as-string: ""
      excludes: "[]"
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.core-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      monitor-delay-time-in-seconds: 120
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  tests-with-lowest-direct-resolution-providers:
    name: "Low dep tests: providers"
    needs: [build-info, build-ci-images]
    uses: ./.github/workflows/run-unit-tests.yml
    permissions:
      contents: read
      packages: read
    if: needs.build-info.outputs.run-tests == 'true'
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      test-name: "LowestDeps"
      force-lowest-dependencies: "true"
      test-scope: "All"
      test-group: "providers"
      backend: "sqlite"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      backend-versions: "['${{ needs.build-info.outputs.default-postgres-version }}']"
      excluded-providers-as-string: ${{ needs.build-info.outputs.excluded-providers-as-string }}
      excludes: "[]"
      test-types-as-strings-in-json: >
        ${{ needs.build-info.outputs.individual-providers-test-types-list-as-strings-in-json }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      run-coverage: ${{ needs.build-info.outputs.run-coverage }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      monitor-delay-time-in-seconds: 120
      skip-providers-tests: ${{ needs.build-info.outputs.skip-providers-tests }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}

  build-prod-images:
    name: Build PROD images
    needs: [build-info, build-ci-images, generate-constraints]
    uses: ./.github/workflows/prod-image-build.yml
    permissions:
      contents: read
      # This write is only given here for `push` events from "apache/airflow" repo. It is not given for PRs
      # from forks. This is to prevent malicious PRs from creating images in the "apache/airflow" repo.
      packages: write
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      build-type: "Regular"
      push-image: "false"
      upload-image-artifact: "true"
      upload-package-artifact: "true"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      branch: ${{ needs.build-info.outputs.default-branch }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      constraints-branch: ${{ needs.build-info.outputs.default-constraints-branch }}
      docker-cache: ${{ needs.build-info.outputs.docker-cache }}
      disable-airflow-repo-cache: ${{ needs.build-info.outputs.disable-airflow-repo-cache }}
      prod-image-build: ${{ needs.build-info.outputs.prod-image-build }}

  additional-prod-image-tests:
    name: "Additional PROD image tests"
    needs: [build-info, build-prod-images, generate-constraints]
    uses: ./.github/workflows/additional-prod-image-tests.yml
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      default-branch: ${{ needs.build-info.outputs.default-branch }}
      constraints-branch: ${{ needs.build-info.outputs.default-constraints-branch }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      docker-cache: ${{ needs.build-info.outputs.docker-cache }}
      disable-airflow-repo-cache: ${{ needs.build-info.outputs.disable-airflow-repo-cache }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
    if: needs.build-info.outputs.prod-image-build == 'true'

  tests-kubernetes:
    name: "Kubernetes tests"
    uses: ./.github/workflows/k8s-tests.yml
    needs: [build-info, build-prod-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      python-versions-list-as-string: ${{ needs.build-info.outputs.python-versions-list-as-string }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}
      kubernetes-combos: ${{ needs.build-info.outputs.kubernetes-combos }}
    if: >
      ( needs.build-info.outputs.run-kubernetes-tests == 'true' ||
      needs.build-info.outputs.needs-helm-tests == 'true')

  tests-task-sdk:
    name: "Task SDK tests"
    uses: ./.github/workflows/airflow-distributions-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      distribution-name: "task-sdk"
      distribution-cmd-format: "prepare-task-sdk-distributions"
      test-type: "task-sdk-tests"
    if: >
      ( needs.build-info.outputs.run-task-sdk-tests == 'true' ||
      needs.build-info.outputs.run-tests == 'true' &&
      needs.build-info.outputs.only-new-ui-files != 'true')

  tests-airflow-ctl:
    name: "Airflow CTL tests"
    uses: ./.github/workflows/airflow-distributions-tests.yml
    needs: [build-info, build-ci-images]
    permissions:
      contents: read
      packages: read
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      distribution-name: "airflow-ctl"
      distribution-cmd-format: "prepare-airflow-ctl-distributions"
      test-type: "airflow-ctl-tests"
    if: >
      ( needs.build-info.outputs.run-airflow-ctl-tests == 'true' ||
      needs.build-info.outputs.run-tests == 'true' &&
      needs.build-info.outputs.only-new-ui-files != 'true')

  finalize-tests:
    name: Finalize tests
    permissions:
      contents: write
      packages: write
    # This will fire when all the jobs from "needs" are either successful or skipped
    if: always() && !failure() && !cancelled()
    needs:
      - additional-ci-image-checks
      - additional-prod-image-tests
      - basic-tests
      - build-info
      - build-prod-images
      - ci-image-checks
      - generate-constraints
      - providers
      - tests-helm
      - tests-integration-system
      - tests-kubernetes
      - tests-mysql-core
      - tests-mysql-providers
      - tests-non-db-core
      - tests-non-db-providers
      - tests-postgres-core
      - tests-postgres-providers
      # - tests-special
      - tests-sqlite-core
      - tests-sqlite-providers
      - tests-task-sdk
      - tests-airflow-ctl
      - tests-with-lowest-direct-resolution-core
      - tests-with-lowest-direct-resolution-providers
    uses: ./.github/workflows/finalize-tests.yml
    with:
      runners: ${{ needs.build-info.outputs.amd-runners }}
      platform: "linux/amd64"
      python-versions: ${{ needs.build-info.outputs.python-versions }}
      python-versions-list-as-string: ${{ needs.build-info.outputs.python-versions-list-as-string }}
      branch: ${{ needs.build-info.outputs.default-branch }}
      constraints-branch: ${{ needs.build-info.outputs.default-constraints-branch }}
      default-python-version: ${{ needs.build-info.outputs.default-python-version }}
      upgrade-to-newer-dependencies: ${{ needs.build-info.outputs.upgrade-to-newer-dependencies }}
      include-success-outputs: ${{ needs.build-info.outputs.include-success-outputs }}
      docker-cache: ${{ needs.build-info.outputs.docker-cache }}
      disable-airflow-repo-cache: ${{ needs.build-info.outputs.disable-airflow-repo-cache }}
      canary-run: ${{ needs.build-info.outputs.canary-run }}
      use-uv: ${{ needs.build-info.outputs.use-uv }}
      debug-resources: ${{ needs.build-info.outputs.debug-resources }}

  notify-slack-failure:
    name: "Notify Slack on Failure"
    needs:
      - finalize-tests
    if: github.event_name == 'schedule' && failure() && github.run_attempt == 1
    runs-on: ["ubuntu-22.04"]
    steps:
      - name: Notify Slack
        id: slack
        uses: slackapi/slack-github-action@485a9d42d3a73031f12ec201c457e2162c45d02d  # v2.0.0
        with:
          method: chat.postMessage
          token: ${{ env.SLACK_BOT_TOKEN }}
          # yamllint disable rule:line-length
          payload: |
            channel: "internal-airflow-ci-cd"
            text: "🚨🕒 Scheduled CI Failure Alert (AMD) on branch *${{ github.ref_name }}* 🕒🚨\n\n*Details:* <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View the failure log>"
            blocks:
              - type: "section"
                text:
                  type: "mrkdwn"
                  text: "🚨🕒 Scheduled CI Failure Alert (AMD) 🕒🚨\n\n*Details:* <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View the failure log>"
          # yamllint enable rule:line-length

  summarize-warnings:
    timeout-minutes: 15
    name: "Summarize warnings"
    runs-on: ${{ fromJSON(needs.build-info.outputs.amd-runners) }}
    if: needs.build-info.outputs.run-tests == 'true'
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Free up disk space"
        shell: bash
        run: ./scripts/tools/free_up_disk_space.sh
      - name: "Download all test warning artifacts from the current build"
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts
          pattern: test-warnings-*
      - name: "Setup python"
        uses: actions/setup-python@v5
        with:
          python-version: ${{ inputs.default-python-version }}
      - name: "Summarize all warnings"
        run: |
          ./scripts/ci/testing/summarize_captured_warnings.py ./artifacts \
            --pattern "**/warnings-*.txt" \
            --output ./files
      - name: "Upload artifact for summarized warnings"
        uses: actions/upload-artifact@v4
        with:
          name: test-summarized-amd-runner-warnings
          path: ./files/warn-summary-*.txt
          retention-days: 7
          if-no-files-found: ignore
          overwrite: true
