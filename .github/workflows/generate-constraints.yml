# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Generate constraints
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      python-versions-list-as-string:
        description: "Stringified array of all Python versions to test - separated by spaces."
        required: true
        type: string
      generate-no-providers-constraints:
        description: "Whether to generate constraints without providers (true/false)"
        required: true
        type: string
      debug-resources:
        description: "Whether to run in debug mode (true/false)"
        required: true
        type: string
      use-uv:
        description: "Whether to use uvloop (true/false)"
        required: true
        type: string
jobs:
  generate-constraints:
    permissions:
      contents: read
    timeout-minutes: 70
    name: Generate constraints ${{ inputs.python-versions-list-as-string }}
    runs-on: ${{ fromJSON(inputs.runners) }}
    env:
      DEBUG_RESOURCES: ${{ inputs.debug-resources }}
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      INCLUDE_SUCCESS_OUTPUTS: "true"
      PYTHON_VERSIONS: ${{ inputs.python-versions-list-as-string }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
        shell: bash
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
        id: breeze
      - name: "Prepare all CI images: ${{ inputs.python-versions-list-as-string}}"
        uses: ./.github/actions/prepare_all_ci_images
        with:
          platform: ${{ inputs.platform }}
          python-versions-list-as-string: ${{ inputs.python-versions-list-as-string }}
          docker-volume-location: ""  # TODO(jscheffl): Understand why it fails here and fix it
      - name: "Verify all CI images ${{ inputs.python-versions-list-as-string }}"
        run: breeze ci-image verify --run-in-parallel
      - name: "Source constraints"
        shell: bash
        run: >
          breeze release-management generate-constraints --run-in-parallel
          --airflow-constraints-mode constraints-source-providers --answer yes
      - name: "No providers constraints"
        shell: bash
        timeout-minutes: 25
        run: >
          breeze release-management generate-constraints --run-in-parallel
          --airflow-constraints-mode constraints-no-providers --answer yes --parallelism 3
        # The no providers constraints are only needed when we want to update constraints (in canary builds)
        # They slow down the start of PROD image builds so we want to only run them when needed.
        if: inputs.generate-no-providers-constraints == 'true'
      - name: "Prepare updated provider distributions"
        # In case of provider distributions which are not yet released, we build them from sources
        shell: bash
        run: >
          breeze release-management prepare-provider-distributions --include-not-ready-providers
          --distribution-format wheel
      - name: "Prepare airflow distributions"
        shell: bash
        run: >
          breeze release-management prepare-airflow-distributions --distribution-format wheel
      - name: "Prepare task-sdk distribution"
        shell: bash
        run: >
          breeze release-management prepare-task-sdk-distributions --distribution-format wheel
      - name: "PyPI constraints"
        shell: bash
        timeout-minutes: 25
        run: |
          for PYTHON in $PYTHON_VERSIONS; do
            breeze release-management generate-constraints --airflow-constraints-mode constraints \
            --answer yes --python "${PYTHON}"
          done
      - name: "Dependency upgrade summary"
        shell: bash
        env:
          PYTHON_VERSIONS: ${{ env.PYTHON_VERSIONS }}
        run: |
          for PYTHON_VERSION in $PYTHON_VERSIONS; do
            echo "Summarizing Python $PYTHON_VERSION"
            cat "files/constraints-${PYTHON_VERSION}"/*.md >> $GITHUB_STEP_SUMMARY || true
            df -H
          done
      - name: "Upload constraint artifacts"
        uses: actions/upload-artifact@v4
        with:
          name: constraints
          path: ./files/constraints-*/constraints-*.txt
          retention-days: 7
          if-no-files-found: error
