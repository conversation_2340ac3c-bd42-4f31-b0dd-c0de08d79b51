# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: "Release PROD images"
on:  # yamllint disable-line rule:truthy
  workflow_dispatch:
    inputs:
      airflowVersion:
        description: 'Airflow version (e.g. 3.0.1, 3.0.1rc1, 3.0.1b1)'
        required: true
      amdOnly:
        type: boolean
        description: 'Limit to amd64 images'
        default: false
      limitPythonVersions:
        type: string
        description: 'Force python versions (e.g. "3.9 3.10")'
        default: ''
permissions:
  contents: read
  packages: read
concurrency:
  group: ${{ github.event.inputs.airflowVersion }}
  cancel-in-progress: true
env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  VERBOSE: true
jobs:
  build-info:
    timeout-minutes: 10
    name: "Build Info"
    runs-on: ["ubuntu-24.04"]
    outputs:
      pythonVersions: ${{ steps.determine-python-versions.outputs.python-versions }}
      platformMatrix: ${{ steps.determine-matrix.outputs.platformMatrix }}
      airflowVersion: ${{ steps.check-airflow-version.outputs.airflowVersion }}
      skipLatest: ${{ steps.check-airflow-version.outputs.skip-latest }}
      amd-runners: ${{ steps.selective-checks.outputs.amd-runners }}
      arm-runners: ${{ steps.selective-checks.outputs.arm-runners }}
    env:
      GITHUB_CONTEXT: ${{ toJson(github) }}
      VERBOSE: true
      AIRFLOW_VERSION: ${{ github.event.inputs.airflowVersion }}
      AMD_ONLY: ${{ github.event.inputs.amdOnly }}
      LIMIT_PYTHON_VERSIONS: ${{ github.event.inputs.limitPythonVersions }}
    if: contains(fromJSON('[
      "ashb",
      "eladkal",
      "ephraimbuddy",
      "jedcunningham",
      "kaxil",
      "pierrejeambrun",
      "potiuk",
      "utkarsharma2"
      ]'), github.event.sender.login)
    steps:
      - name: "Input parameters summary"
        shell: bash
        run: |
          echo "Input parameters summary"
          echo "========================="
          echo "Airflow version: '${AIRFLOW_VERSION}'"
          echo "AMD only: '${AMD_ONLY}'"
          echo "Limit python versions: '${LIMIT_PYTHON_VERSIONS}'"
      - name: "Cleanup repo"
        shell: bash
        run: >
          docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"

      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Install uv"
        run: curl -LsSf https://astral.sh/uv/install.sh | sh
      - name: "Check airflow version"
        id: check-airflow-version
        shell: bash
        run: uv run scripts/ci/airflow_version_check.py "${AIRFLOW_VERSION}" >> "${GITHUB_OUTPUT}"
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: "true"
      - name: Selective checks
        id: selective-checks
        env:
          VERBOSE: "false"
        run: breeze ci selective-check 2>> ${GITHUB_OUTPUT}
      - name: "Determine build matrix"
        shell: bash
        id: determine-matrix
        run: |
          if [[ "${AMD_ONLY}" = "true" ]]; then
             echo 'platformMatrix=["linux/amd64"]' >> "${GITHUB_OUTPUT}"
          else
             echo 'platformMatrix=["linux/amd64", "linux/arm64"]' >> "${GITHUB_OUTPUT}"
          fi
      - name: "Determine python versions"
        shell: bash
        id: determine-python-versions
        env:
          ALL_PYTHON_VERSIONS: ${{ steps.selective-checks.outputs.all-python-versions }}
        # yamllint disable rule:line-length
        run: |
          # override python versions if specified
          if [[ "${LIMIT_PYTHON_VERSIONS}" != "" ]]; then
            PYTHON_VERSIONS=$(python3 -c "import json; print(json.dumps('${LIMIT_PYTHON_VERSIONS}'.split(' ')))")
          else
            PYTHON_VERSIONS=${ALL_PYTHON_VERSIONS}
          fi
          echo "python-versions=${PYTHON_VERSIONS}" >> "${GITHUB_OUTPUT}"


  release-images:
    name: "Release images"
    needs: [build-info]
    strategy:
      fail-fast: false
      matrix:
        python: ${{ fromJSON(needs.build-info.outputs.pythonVersions) }}
    uses: ./.github/workflows/release_single_dockerhub_image.yml
    secrets:
      DOCKERHUB_USER: ${{ secrets.DOCKERHUB_USER }}
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
    permissions:
      contents: read
    with:
      pythonVersion: ${{ matrix.python }}
      airflowVersion: ${{ needs.build-info.outputs.airflowVersion }}
      platformMatrix: ${{ needs.build-info.outputs.platformMatrix }}
      skipLatest: ${{ needs.build-info.outputs.skipLatest }}
      armRunners: ${{ needs.build-info.outputs.arm-runners }}
      amdRunners: ${{ needs.build-info.outputs.amd-runners }}
