# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Finalize tests
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      python-versions:
        description: "JSON-formatted array of Python versions to test"
        required: true
        type: string
      python-versions-list-as-string:
        description: "Stringified array of all Python versions to test - separated by spaces."
        required: true
        type: string
      branch:
        description: "The default branch to use for the build"
        required: true
        type: string
      constraints-branch:
        description: "The branch to use for constraints"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      upgrade-to-newer-dependencies:
        description: "Whether to upgrade to newer dependencies (true/false)"
        required: true
        type: string
      docker-cache:
        description: "Docker cache specification to build the image (registry, local, disabled)."
        required: true
        type: string
      disable-airflow-repo-cache:
        description: "Disable airflow repo cache read from main."
        required: true
        type: string
      include-success-outputs:
        description: "Whether to include success outputs (true/false)"
        required: true
        type: string
      canary-run:
        description: "Whether this is a canary run (true/false)"
        required: true
        type: string
      use-uv:
        description: "Whether to use uv to build the image (true/false)"
        required: true
        type: string
      debug-resources:
        description: "Whether to debug resources or not (true/false)"
        required: true
        type: string
permissions:
  contents: read
jobs:
  update-constraints:
    runs-on: ${{ fromJSON(inputs.runners) }}
    timeout-minutes: 80
    name: "Update constraints"
    permissions:
      contents: write
      packages: read
    env:
      DEBUG_RESOURCES: ${{ inputs.debug-resources}}
      PYTHON_VERSIONS: ${{ inputs.python-versions-list-as-string }}
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
    if: inputs.upgrade-to-newer-dependencies != 'false' && inputs.platform == 'linux/amd64'
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          # Needed to perform push action
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Set constraints branch name"
        id: constraints-branch
        run: ./scripts/ci/constraints/ci_branch_constraints.sh >> ${GITHUB_OUTPUT}
      - name: Checkout ${{ steps.constraints-branch.outputs.branch }}
        uses: actions/checkout@v4
        with:
          path: "constraints"
          ref: ${{ steps.constraints-branch.outputs.branch }}
          persist-credentials: true
          fetch-depth: 0
      - name: "Download constraints from the constraints generated by build CI image"
        uses: actions/download-artifact@v4
        with:
          name: constraints
          path: ./files
      - name: "Diff in constraints for Python: ${{ inputs.python-versions-list-as-string }}"
        run: ./scripts/ci/constraints/ci_diff_constraints.sh
      # only commit and push constraints in canary runs (main)
      - name: "Commit changed constraint files for Python: ${{ inputs.python-versions-list-as-string }}"
        run: ./scripts/ci/constraints/ci_commit_constraints.sh
        if: inputs.canary-run == 'true'
      - name: "Push changes"
        if: inputs.canary-run == 'true' && github.event_name != 'pull_request'
        working-directory: "constraints"
        run:
          git push

  push-buildx-cache-to-github-registry:
    name: Push Regular Image Cache ${{ inputs.platform }}
    needs: [update-constraints]
    uses: ./.github/workflows/push-image-cache.yml
    permissions:
      contents: read
      # This write is only given here for `push` events from "apache/airflow" repo. It is not given for PRs
      # from forks. This is to prevent malicious PRs from creating images in the "apache/airflow" repo.
      packages: write
    with:
      runners: ${{ inputs.runners }}
      platform: ${{ inputs.platform }}
      cache-type: "Regular AMD"
      include-prod-images: "true"
      push-latest-images: "true"
      python-versions: ${{ inputs.python-versions }}
      branch: ${{ inputs.branch }}
      constraints-branch: ${{ inputs.constraints-branch }}
      use-uv: ${{ inputs.use-uv }}
      include-success-outputs: ${{ inputs.include-success-outputs }}
      docker-cache: ${{ inputs.docker-cache }}
      disable-airflow-repo-cache: ${{ inputs.disable-airflow-repo-cache }}
    if: >
      inputs.canary-run == 'true' &&
      (github.event_name == 'schedule' || github.event_name == 'workflow_dispatch')
