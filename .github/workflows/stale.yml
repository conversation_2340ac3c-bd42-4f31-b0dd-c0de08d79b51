# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
# https://github.com/actions/stale
name: 'Close stale PRs & Issues'
on:  # yamllint disable-line rule:truthy
  schedule:
    - cron: '0 0 * * *'
permissions:
  # All other permissions are set to none
  pull-requests: write
  issues: write
jobs:
  stale:
    runs-on: ["ubuntu-22.04"]
    steps:
      - uses: actions/stale@v9
        with:
          stale-pr-message: >
            This pull request has been automatically marked as stale because it has not had
            recent activity. It will be closed in 5 days if no further activity occurs. Thank you
            for your contributions.
          days-before-pr-stale: 45
          days-before-pr-close: 5
          exempt-pr-labels: 'pinned,security'
          only-issue-labels: 'pending-response'
          remove-stale-when-updated: true
          days-before-issue-stale: 14
          days-before-issue-close: 7
          stale-issue-message: >
            This issue has been automatically marked as stale because it has been open for 14 days
            with no response from the author. It will be closed in next 7 days if no further
            activity occurs from the issue author.
          close-issue-message: >
            This issue has been closed because it has not received response from the issue author.
