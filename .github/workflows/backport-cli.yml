# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: Backport Commit
on:  # yamllint disable-line rule:truthy
  workflow_dispatch:
    inputs:
      commit-sha:
        description: "Commit sha to backport."
        required: true
        type: string
      target-branch:
        description: "Target branch to backport."
        required: true
        type: string

  workflow_call:
    inputs:
      commit-sha:
        description: "Commit sha to backport."
        required: true
        type: string
      target-branch:
        description: "Target branch to backport."
        required: true
        type: string

permissions:
  # Those permissions are only active for workflow dispatch (only committers can trigger it) and workflow call
  # Which is triggered automatically by "automatic-backport" push workflow (only when merging by committer)
  # Branch protection  prevents from pushing to the "code" branches
  contents: write
  pull-requests: write
jobs:
  backport:
    runs-on: ubuntu-latest

    steps:
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        id: checkout-for-backport
        uses: actions/checkout@v4
        with:
          persist-credentials: true
          fetch-depth: 0

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install cherry-picker==2.5.0 requests==2.32.3

      - name: Run backport script
        id: execute-backport
        env:
          GH_AUTH: ${{ secrets.GITHUB_TOKEN }}
          TARGET_BRANCH: ${{ inputs.target-branch }}
          COMMIT_SHA: ${{ inputs.commit-sha }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Your Name"
          set +e
          {
          echo 'cherry_picker_output<<EOF'
          cherry_picker ${COMMIT_SHA} ${TARGET_BRANCH}
          echo EOF
          } >> "${GITHUB_OUTPUT}"
        continue-on-error: true

      - name: Parse backport output
        id: parse-backport-output
        env:
          CHERRY_PICKER_OUTPUT: ${{ steps.execute-backport.outputs.cherry_picker_output }}
        run: |
          set +e
          echo "${CHERRY_PICKER_OUTPUT}"

          url=$(echo "${CHERRY_PICKER_OUTPUT}" | \
              grep -o 'Backport PR created at https://[^ ]*' | \
              awk '{print $5}')

          url=${url:-"EMPTY"}
          if [ "$url" == "EMPTY" ]; then
            # If the backport failed, abort the workflow
            cherry_picker --abort
          fi
          echo "backport-url=$url" >> "${GITHUB_OUTPUT}"
        continue-on-error: true

      - name: Update Status
        id: backport-status
        env:
          GH_TOKEN: ${{ github.token }}
          REPOSITORY: ${{ github.repository }}
          RUN_ID: ${{ github.run_id }}
          COMMIT_SHA: ${{ inputs.commit-sha }}
          TARGET_BRANCH: ${{ inputs.target-branch }}
          BACKPORT_URL: ${{ steps.parse-backport-output.outputs.backport-url }}
        run: |
          COMMIT_INFO_URL="https://api.github.com/repos/$REPOSITORY/commits/"
          COMMIT_INFO_URL="${COMMIT_INFO_URL}$COMMIT_SHA/pulls"

          PR_NUMBER=$(gh api \
              -H "Accept: application/vnd.github+json" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              /repos/$REPOSITORY/commits/$COMMIT_SHA/pulls \
              --jq '.[0].number')

          python ./dev/backport/update_backport_status.py \
              $BACKPORT_URL \
              $COMMIT_SHA $TARGET_BRANCH \
              "$PR_NUMBER"
