# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: He<PERSON> tests
on:  # yamllint disable-line rule:truthy
  workflow_call:
    inputs:
      runners:
        description: "The array of labels (in json form) determining runners."
        required: true
        type: string
      platform:
        description: "Platform for the build - 'linux/amd64' or 'linux/arm64'"
        required: true
        type: string
      helm-test-packages:
        description: "Stringified JSON array of helm test packages to test"
        required: true
        type: string
      default-python-version:
        description: "Which version of python should be used by default"
        required: true
        type: string
      use-uv:
        description: "Whether to use uvloop (true/false)"
        required: true
        type: string
permissions:
  contents: read
jobs:
  tests-helm:
    timeout-minutes: 80
    name: "Unit tests Helm: ${{ matrix.helm-test-package }}"
    runs-on: ${{ fromJSON(inputs.runners) }}
    strategy:
      fail-fast: false
      matrix:
        helm-test-package: ${{ fromJSON(inputs.helm-test-packages) }}
    env:
      # Always use default Python version of CI image for preparing packages
      PYTHON_MAJOR_MINOR_VERSION: "${{ inputs.default-python-version }}"
      PARALLEL_TEST_TYPES: "Helm"
      BACKEND: "none"
      DB_RESET: "false"
      JOB_ID: "helm-tests"
      USE_XDIST: "true"
      GITHUB_REPOSITORY: ${{ github.repository }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_USERNAME: ${{ github.actor }}
      VERBOSE: "true"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare breeze & CI image: ${{ inputs.default-python-version }}"
        uses: ./.github/actions/prepare_breeze_and_image
        with:
          platform: ${{ inputs.platform }}
          python: ${{ inputs.default-python-version }}
          use-uv: ${{ inputs.use-uv }}
      - name: "Helm Unit Tests: ${{ matrix.helm-test-package }}"
        env:
          HELM_TEST_PACKAGE: "${{ matrix.helm-test-package }}"
        run: breeze testing helm-tests --test-type "${HELM_TEST_PACKAGE}"

  tests-helm-release:
    timeout-minutes: 80
    name: "Release Helm"
    runs-on: ${{ fromJSON(inputs.runners) }}
    env:
      PYTHON_MAJOR_MINOR_VERSION: "${{inputs.default-python-version}}"
    steps:
      - name: "Cleanup repo"
        shell: bash
        run: docker run -v "${GITHUB_WORKSPACE}:/workspace" -u 0:0 bash -c "rm -rf /workspace/*"
      - name: "Checkout ${{ github.ref }} ( ${{ github.sha }} )"
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: "Prepare and cleanup runner"
        run: ./scripts/ci/prepare_and_cleanup_runner.sh
      - name: "Install Breeze"
        uses: ./.github/actions/breeze
        with:
          use-uv: ${{ inputs.use-uv }}
      - name: Setup git for tagging
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Your Name"
      - name: "Remove old artifacts"
        run: rm -rf dist/*
      - name: "Setup k8s/helm environment"
        run: breeze k8s setup-env
      - name: "Install helm gpg plugin"
        run: >
          breeze k8s shell -c "helm plugin install https://github.com/technosophos/helm-gpg
          --version 6303407eb63deaeb1b2f24de611e3468a27ec05b" || true
      - name: "Helm release tarball"
        run: >
          breeze release-management prepare-helm-chart-tarball --ignore-version-check --override-tag
          --skip-tag-signing --version 0.0.0 --version-suffix dev0
      - name: Generate GPG key for signing
        # Sometimes the key will be already added to the keyring, so we ignore the error
        run: gpg --batch --passphrase '' --quick-gen-key <EMAIL> default default || true
      - name: "Helm release package"
        run: >
          breeze release-management prepare-helm-chart-package --sign-email <EMAIL>
      - name: "Sign artifacts for ASF distribution"
        run: ./dev/sign.sh dist/airflow-*.tgz dist/airflow-*-source.tar.gz
        env:
          SIGN_WITH: <EMAIL>
      - name: "Fetch Git Tags"
        run: git fetch --tags
      - name: "Test helm chart issue generation automatically"
        # Adding same tags for now, will address in a follow-up
        run: >
          breeze release-management generate-issue-content-helm-chart --limit-pr-count 10
          --latest --verbose
      - name: "Upload Helm artifacts"
        uses: actions/upload-artifact@v4
        with:
          name: Helm artifacts
          path: ./dist/airflow-*
          retention-days: 7
          if-no-files-found: error
