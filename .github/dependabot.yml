# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License
---
version: 2
updates:
  - package-ecosystem: pip
    directories:
      - /airflow-core
      - /airflow-ctl
      - /clients/python
      - /dev/breeze
      - /docker-tests
      - /kubernetes-tests
      - /helm-tests
      - /task-sdk
      - /
    schedule:
      interval: daily

  - package-ecosystem: npm
    directories:
      - /airflow-core/src/airflow/ui
    schedule:
      interval: daily
    groups:
      core-ui-package-updates:
        patterns:
          - "*"

  - package-ecosystem: npm
    directories:
      - /airflow-core/src/airflow/api_fastapi/auth/managers/simple/ui
    schedule:
      interval: daily
    groups:
      core-ui-package-updates:
        patterns:
          - "*"
  - package-ecosystem: npm
    directories:
      - /providers/fab/src/airflow/providers/fab/www
    schedule:
      interval: daily
    groups:
      fab-ui-package-updates:
        patterns:
          - "*"

  # Repeat dependency updates on 2.11 branch as well
  - package-ecosystem: pip
    directories:
      - /clients/python
      - /dev/breeze
      - /docker_tests
      - /
    schedule:
      interval: daily
    target-branch: v2-11-test

  - package-ecosystem: npm
    directories:
      - /airflow/www/
    schedule:
      interval: daily
    target-branch: v2-11-test
    groups:
      core-ui-package-updates:
        patterns:
          - "*"
