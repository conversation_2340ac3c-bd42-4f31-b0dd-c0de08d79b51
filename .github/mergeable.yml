# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
---
version: 2
mergeable:
  - when: pull_request.*, pull_request_review.*
    validate:
      - do: title
        # Do not merge when it is marked work in progress (WIP)
        or:
          - must_exclude:
              regex: \[WIP\]|\bWIP\b|🚧
              message: This is work in progress. Do not merge yet.
          - must_exclude:
              regex: ^\[DONT-MERGE\]
              message: Do not merge this PR yet.
      - do: label
        must_exclude:
          regex: 'wip'
      - do: label
        must_exclude:
          regex: 'dont-merge'
      # If package.json is updated, so should yarn.lock
      - do: dependent
        changed:
          file: 'package.json'
          files: ['yarn.lock']
