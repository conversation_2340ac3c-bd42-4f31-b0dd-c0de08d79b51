---
name: Airflow feature request
description: Suggest an idea for this project
labels: ["kind:feature", "needs-triage"]
body:
  - type: markdown
    attributes:
      # yamllint disable rule:line-length
      value: "
        <img src='https://raw.githubusercontent.com/apache/airflow/main/airflow-core/docs/img/logos/airflow_64x64_emoji_transparent.png' align='left' width='80' height='80'>
        Thank you for finding the time to propose new feature!

        We really appreciate the community efforts to improve Airflow.

        Features should be small improvements that do not dramatically change Airflow assumptions.
        Note, that in this case you do not even need to create an issue if you have a code change ready to submit!
        You can open [Pull Request](https://github.com/apache/airflow/pulls) immediately instead.

        For bigger features, those that are impacting Airflow's architecture, security assumptions,
        backwards compatibility etc. should be discussed in the [airflow devlist](https://lists.apache.org/list.html?<EMAIL>).
        Such features will need initial discussion - possibly in [discussion](https://github.com/apache/airflow/discussions), followed by
        [Airflow Improvement Proposal](https://cwiki.apache.org/confluence/display/AIRFLOW/Airflow+Improvement+Proposals) and formal voting.
        If you want to introduce such feature, you need to be prepared to lead a discussion, get consensus
        among the community and eventually conduct a successful
        [vote](https://www.apache.org/foundation/voting.html) in the community.

        If unsure - open a [discussion](https://github.com/apache/airflow/discussions) first to gather
        an initial feedback on your idea.

        <br clear='left'/>"

      # yamllint enable rule:line-length
  - type: textarea
    attributes:
      label: Description
      description: A short description of your feature
  - type: textarea
    attributes:
      label: Use case/motivation
      description: What would you like to happen?
      placeholder: >
          Rather than telling us how you might implement this feature, try to take a
          step back and describe what you are trying to achieve.
  - type: textarea
    attributes:
      label: Related issues
      description: Is there currently another issue associated with this?
  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        If want to submit a PR you do not need to open feature request, <b>just create the PR!</b>.
        Especially if you already have a good understanding of how to implement the feature.
        Airflow is a community-managed project and we love to bring new contributors in.
        Find us in #new-contributors on Slack!
        It's optional though - if you have good idea for small feature,
        others might implement it if they pick an interest in it, so feel free to leave that
        checkbox unchecked.
      options:
        - label: Yes I am willing to submit a PR!
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: The Code of Conduct helps create a safe space for everyone. We require
        that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://github.com/apache/airflow/blob/main/CODE_OF_CONDUCT.md)
          required: true
  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
