---
name: Airflow Doc issue report
description: Problems and issues with Apache Airflow documentation
labels: ["kind:bug", "kind:documentation", "needs-triage"]
body:
  - type: markdown
    attributes:
      # yamllint disable rule:line-length
      value: "
        <img src='https://raw.githubusercontent.com/apache/airflow/main/airflow-core/docs/img/logos/airflow_64x64_emoji_transparent.png' align='left' width='80' height='80'>
        Thank you for finding the time to report the problem!

        We really appreciate the community's efforts to improve Airflow.

        Note, you do not need to create an issue if you have a change ready to submit!

        You can open a [pull request](https://github.com/apache/airflow/pulls) immediately instead.
        <br clear='left'/>"
      # yamllint enable rule:line-length
  - type: textarea
    attributes:
      label: What do you see as an issue?
      description: Please describe the issue with documentation you have.
      placeholder: >
        Please include links to the documentation that has the problem and possibly screenshots showing
        the problem. Explain why do you think it is an issue. Make sure you include view of the target
        audience of the documentation. Please explain why you think the docs are wrong.
  - type: textarea
    attributes:
      label: Solving the problem
      description: How do you think the problem can be solved?
      placeholder: >
        Please explain how you think the documentation could be fixed. Ideally specify where a new or missing
        documentation should be added and what kind of information should be included. Sometimes people
        writing the documentation do not realise that some assumptions the have might not be in the heads
        of the reader, so try to explain exactly what you would like to see in the docs and why.
  - type: textarea
    attributes:
      label: Anything else
      description: Anything else we need to know?
      placeholder: >
        How often does this problem occur? (Once? Every time? Only when certain conditions are met?)
        Any relevant logs to include? Put them here inside fenced
        ``` ``` blocks or inside a foldable details tag if it's long:
        <details><summary>x.log</summary> lots of stuff </details>
  - type: checkboxes
    attributes:
      label: Are you willing to submit PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process
        especially if you already have a good understanding of how to implement the fix.
        Airflow is a community-managed project and we love to bring new contributors in.
        Find us in #new-contributors on Slack!
      options:
        - label: Yes I am willing to submit a PR!
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: >
        The Code of Conduct helps create a safe space for everyone. We require
        that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://github.com/apache/airflow/blob/main/CODE_OF_CONDUCT.md)
          required: true
  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
