---
name: Airflow Helm Chart Bug report
description: Problems and issues with the Apache Airflow Official Helm Chart
labels: ["kind:bug", "area:helm-chart", "needs-triage"]
body:
  - type: markdown
    attributes:
      # yamllint disable rule:line-length
      value: "
        <img src='https://raw.githubusercontent.com/apache/airflow/main/airflow-core/docs/img/logos/airflow_64x64_emoji_transparent.png' align='left' width='120' height='120'>
        Thank you for finding the time to report the problem!

        We really appreciate the community's efforts to improve Airflow.

        Note that this issue is only for the
        [Official Apache Airflow Helm Chart](https://airflow.apache.org/docs/helm-chart/stable/index.html).
        If you use another 3rd-party Chart, you should report your issue in the repo of that chart instead.

        Note, you do not need to create an issue if you have a change ready to submit!

        You can open a [pull request](https://github.com/apache/airflow/pulls) immediately instead.
        <br clear='left'/>"
      # yamllint enable rule:line-length
  - type: dropdown
    attributes:
      label: Official Helm Chart version
      description: >
        What Apache Airflow Helm Chart version are you using?
      multiple: false
      options:
        - "1.16.0 (latest released)"
        - "1.15.0"
        - "1.14.0"
        - "1.13.1"
        - "1.13.0"
        - "1.12.0"
        - "1.11.0"
        - "1.10.0"
        - "1.9.0"
        - "1.8.0"
        - "1.7.0"
        - "1.6.0"
        - "1.5.0"
        - "1.4.0"
        - "1.3.0"
        - "1.2.0"
        - "1.1.0"
        - "1.0.0"
        - "main (development)"
    validations:
      required: true
  - type: input
    attributes:
      label: Apache Airflow version
      description: >
        What Apache Airflow version are you using?
        [Only Airflow 2 is supported](https://github.com/apache/airflow#version-life-cycle) for bugs.
    validations:
      required: true
  - type: input
    attributes:
      label: Kubernetes Version
      description: Which Kubernetes Version do you use?
    validations:
      required: true
  - type: textarea
    attributes:
      label: Helm Chart configuration
      description: Additional description of your Helm Chart configuration.
      placeholder: >
        Enter any relevant details of your Helm Chart configuration. Maybe you can
        paste your `values.yaml` or important parts of it here? Make sure to surround the code
        you paste with ``` ```.
  - type: textarea
    attributes:
      label: Docker Image customizations
      description: What are the specific modification you've made in your image?
      placeholder: >
        Did you extend or customise the official Airflow image? Did you add any packages? Maybe
        you can share a link to your image, or copy the Dockerfile and `docker build` commands
        you used to build the image? Make sure to surround the code you paste with ``` ```.
  - type: textarea
    attributes:
      label: What happened
      description: Describe what happened.
      placeholder: >
        Please provide the context in which the problem occurred and explain what happened
  - type: textarea
    attributes:
      label: What you think should happen instead
      description: What do you think went wrong?
      placeholder: >
        Please explain why you think the behaviour is erroneous. It is extremely helpful if you copy&paste
        the fragment of logs showing the exact error messages or wrong behaviour and screenshots for
        UI problems or YouTube link to a video of you demonstrating the problem. You can include files by
        dragging and dropping them here.
  - type: textarea
    attributes:
      label: How to reproduce
      description: >
        What should we do to reproduce the problem? If you are not able to provide a reproducible case,
        please open a [Discussion](https://github.com/apache/airflow/discussions) instead.
      placeholder: >
        Please make sure you provide a reproducible step-by-step case of how to reproduce the problem
        as minimally and precisely as possible. Keep in mind we do not have access to your cluster or DAGs.
        Remember that non-reproducible issues will be closed! Opening a discussion is recommended as a
        first step.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Anything else
      description: Anything else we need to know?
      placeholder: >
        How often does this problem occur? (Once? Every time? Only when certain conditions are met?)
        Any relevant logs to include? Put them here inside fenced
        ``` ``` blocks or inside a foldable details tag if it's long:
        <details><summary>x.log</summary> lots of stuff </details>
  - type: checkboxes
    attributes:
      label: Are you willing to submit PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process
        especially if you already have a good understanding of how to implement the fix.
        Airflow is a community-managed project and we love to bring new contributors in.
        Find us in #new-contributors on Slack!
      options:
        - label: Yes I am willing to submit a PR!
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: >
        The Code of Conduct helps create a safe space for everyone. We require
        that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://github.com/apache/airflow/blob/main/CODE_OF_CONDUCT.md)
          required: true
  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
