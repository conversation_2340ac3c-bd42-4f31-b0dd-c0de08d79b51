---
name: Airflow Bug report
description: Problems and issues with code in Apache Airflow core
labels: ["kind:bug", "area:core", "needs-triage"]
body:
  - type: markdown
    attributes:
      # yamllint disable rule:line-length
      value: "
        <img src='https://raw.githubusercontent.com/apache/airflow/main/airflow-core/docs/img/logos/airflow_64x64_emoji_transparent.png' align='left' width='80' height='80'>
        Thank you for finding the time to report the problem!

        We really appreciate the community's efforts to improve Airflow.

        Note, you do not need to create an issue if you have a change ready to submit!

        You can open a [pull request](https://github.com/apache/airflow/pulls) immediately instead.
        <br clear='left'/>"
      # yamllint enable rule:line-length
  - type: dropdown
    attributes:
      label: Apache Airflow version
      description: >
        What Apache Airflow version are you using? If you do not see your version, please (ideally) test on
        the latest release or main to see if the issue is fixed before reporting it.
      multiple: false
      options:
        - "3.0.1"
        - "2.11.0"
        - "main (development)"
        - "Other Airflow 2 version (please specify below)"
    validations:
      required: true
  - type: input
    attributes:
      label: If "Other Airflow 2 version" selected, which one?
      # yamllint disable rule:line-length
      description: >
        On what 2.X version of Airflow are you currently experiencing the issue? Remember, you are encouraged to
        test with the latest release or on the main branch to verify your issue still exists, especially if
        your version is at least a minor version older than the [current stable release](https://airflow.apache.org/docs/apache-airflow/stable/installation/supported-versions.html#version-life-cycle).
      # yamllint enable rule:line-length
  - type: textarea
    attributes:
      label: What happened?
      description: Describe what happened.
      placeholder: >
        Please provide the context in which the problem occurred and explain what happened
    validations:
      required: true
  - type: textarea
    attributes:
      label: What you think should happen instead?
      description: What do you think went wrong?
      placeholder: >
        Please explain why you think the behaviour is erroneous. It is extremely helpful if you copy&paste
        the fragment of logs showing the exact error messages or wrong behaviour and screenshots for
        UI problems or YouTube link to a video of you demonstrating the problem. You can include files by
        dragging and dropping them here.
  - type: textarea
    attributes:
      label: How to reproduce
      description: >
        What should we do to reproduce the problem? If you are not able to provide a reproducible case,
        please open a [discussion](https://github.com/apache/airflow/discussions) instead.
      placeholder: >
        Please make sure you provide a reproducible step-by-step case of how to reproduce the problem
        as minimally and precisely as possible. Keep in mind we do not have access to your cluster or DAGs.
        Remember that non-reproducible issues will be closed! Opening a discussion is recommended as a
        first step.
    validations:
      required: true
  - type: input
    attributes:
      label: Operating System
      description: What Operating System are you using?
      placeholder: "You can get it via `cat /etc/os-release` for example"
    validations:
      required: true
  - type: textarea
    attributes:
      label: Versions of Apache Airflow Providers
      description: What Apache Airflow Providers versions are you using?
      placeholder: You can use `pip freeze | grep apache-airflow-providers` (you can leave only relevant ones)
  - type: dropdown
    attributes:
      label: Deployment
      description: >
        What kind of deployment do you have? If you use a Managed Service, consider first using regular
        channels of reporting issues for the service.
      multiple: false
      options:
        - "Official Apache Airflow Helm Chart"
        - "Other 3rd-party Helm chart"
        - "Docker-Compose"
        - "Other Docker-based deployment"
        - "Virtualenv installation"
        - "Astronomer"
        - "Google Cloud Composer"
        - "Amazon (AWS) MWAA"
        - "Microsoft ADF Managed Airflow"
        - "Other"
    validations:
      required: true
  - type: textarea
    attributes:
      label: Deployment details
      description: Additional description of your deployment.
      placeholder: >
        Enter any relevant details of your deployment. Especially version of your tools,
        software (docker-compose, helm, k8s, etc.), any customisation and configuration you added.
  - type: textarea
    attributes:
      label: Anything else?
      description: Anything else we need to know?
      placeholder: >
        How often does this problem occur? (Once? Every time? Only when certain conditions are met?)
        Any relevant logs to include? Put them here inside fenced
        ``` ``` blocks or inside a foldable details tag if it's long:
        <details><summary>x.log</summary> lots of stuff </details>
  - type: checkboxes
    attributes:
      label: Are you willing to submit PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process
        especially if you already have a good understanding of how to implement the fix.
        Airflow is a community-managed project and we love to bring new contributors in.
        Find us in #new-contributors on Slack!
      options:
        - label: Yes I am willing to submit a PR!
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: >
        The Code of Conduct helps create a safe space for everyone. We require
        that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://github.com/apache/airflow/blob/main/CODE_OF_CONDUCT.md)
          required: true
  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
