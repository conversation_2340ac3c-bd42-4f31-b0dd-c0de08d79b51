# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: 'Run migration tests'
description: 'Runs migration tests'
runs:
  using: "composite"
  steps:
    - name: "Test migration file 2 to 3 migration: ${{env.BACKEND}}"
      shell: bash
      run: |
        breeze shell "${{ env.AIRFLOW_2_CMD }}" --use-airflow-version 2.11.0 --answer y &&
        breeze shell "export AIRFLOW__DATABASE__EXTERNAL_DB_MANAGERS=${{env.DB_MANGERS}}
                    ${{ env.AIRFLOW_3_CMD }}" --no-db-cleanup
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
        DB_RESET: "false"
        DB_MANAGERS: "airflow.providers.fab.auth_manager.models.db.FABDBManager"
        AIRFLOW_2_CMD: >-
          airflow db reset --skip-init -y &&
          airflow db migrate --to-revision heads
        AIRFLOW_3_CMD: >-
          airflow db migrate --to-revision heads &&
          airflow db downgrade -n 2.7.0 -y &&
          airflow db migrate
      if: env.BACKEND != 'sqlite'
    - name: "Bring composer down"
      shell: bash
      run: breeze down
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
    - name: "Test ORM migration 2 to 3: ${{env.BACKEND}}"
      shell: bash
      run: >
        breeze shell "${{ env.AIRFLOW_2_CMD }}" --use-airflow-version 2.11.0 --answer y &&
        breeze shell "export AIRFLOW__DATABASE__EXTERNAL_DB_MANAGERS=${{env.DB_MANGERS}}
             ${{ env.AIRFLOW_3_CMD }}" --no-db-cleanup
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
        DB_RESET: "false"
        DB_MANAGERS: "airflow.providers.fab.auth_manager.models.db.FABDBManager"
        AIRFLOW_2_CMD: >-
          airflow db reset -y
        AIRFLOW_3_CMD: >-
          airflow db migrate --to-revision heads &&
          airflow db downgrade -n 2.7.0 -y &&
          airflow db migrate
      if: env.BACKEND != 'sqlite'
    - name: "Bring compose down again"
      shell: bash
      run: breeze down
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
    - name: "Test ORM migration ${{env.BACKEND}}"
      shell: bash
      run: >
        breeze shell "export AIRFLOW__DATABASE__EXTERNAL_DB_MANAGERS=${{env.DB_MANAGERS}} &&
        airflow db reset -y &&
        airflow db migrate --to-revision heads &&
        airflow db downgrade -n 2.7.0 -y &&
        airflow db migrate"
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
        DB_MANAGERS: "airflow.providers.fab.auth_manager.models.db.FABDBManager"
    - name: "Bring compose down again"
      shell: bash
      run: breeze down
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
    - name: "Test offline migration ${{env.BACKEND}}"
      shell: bash
      run: >
        breeze shell
        "export AIRFLOW__DATABASE__EXTERNAL_DB_MANAGERS=${{env.DB_MANAGERS}} &&
        airflow db reset -y &&
        airflow db downgrade -n 2.7.0 -y &&
        airflow db migrate -s"
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
        DB_MANAGERS: "airflow.providers.fab.auth_manager.models.db.FABDBManager"
      if: env.BACKEND != 'sqlite'
    - name: "Bring any containers left down"
      shell: bash
      run: breeze down
      env:
        COMPOSE_PROJECT_NAME: "docker-compose"
    - name: "Dump logs on failure ${{env.BACKEND}}"
      shell: bash
      run: docker ps -q | xargs docker logs
      if: failure()
