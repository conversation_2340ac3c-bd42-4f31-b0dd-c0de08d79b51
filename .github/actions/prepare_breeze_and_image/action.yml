# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: 'Prepare breeze && current image (CI or PROD)'
description: 'Installs breeze and recreates current python image from artifact'
inputs:
  python:
    description: 'Python version for image to prepare'
    required: true
  image-type:
    description: 'Which image type to prepare (ci/prod)'
    default: "ci"
  platform:
    description: 'Platform for the build - linux/amd64 or linux/arm64'
    required: true
  use-uv:
    description: 'Whether to use uv'
    required: true
outputs:
  host-python-version:
    description: Python version used in host
    value: ${{ steps.breeze.outputs.host-python-version }}
runs:
  using: "composite"
  steps:
    - name: "Prepare and cleanup runner"
      run: ./scripts/ci/prepare_and_cleanup_runner.sh
      shell: bash
    - name: "Install Breeze"
      uses: ./.github/actions/breeze
      with:
        use-uv: ${{ inputs.use-uv }}
      id: breeze
    - name: "Check free space"
      shell: bash
      run: |
        echo "Checking free space!"
        df -H
    - name: "Restore ${{ inputs.image-type }} docker image ${{ inputs.platform }}:${{ inputs.python }}"
      uses: apache/infrastructure-actions/stash/restore@1c35b5ccf8fba5d4c3fdf25a045ca91aa0cbc468
      with:
        key: ${{ inputs.image-type }}-image-save-v3-${{ inputs.platform }}-${{ inputs.python }}
        path: "/mnt/"
        only-current-branch: 'true'
    - name: "Load ${{ inputs.image-type }} image ${{ inputs.platform }}:${{ inputs.python }}"
      env:
        PLATFORM: ${{ inputs.platform }}
        PYTHON: ${{ inputs.python }}
        IMAGE_TYPE: ${{ inputs.image-type }}
      run: >
        breeze ${IMAGE_TYPE}-image load --platform "${PLATFORM}" --python "${PYTHON}" --image-file-dir "/mnt"
      shell: bash
