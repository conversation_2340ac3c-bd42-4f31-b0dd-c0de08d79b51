# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: 'Post tests on success'
description: 'Run post tests actions on success'
inputs:
  codecov-token:
    description: 'Codecov token'
    required: true
    default: ''
  python-version:
    description: 'Python version'
    required: true
    default: ''
runs:
  using: "composite"
  steps:
    - name: "Upload artifact for warnings"
      uses: actions/upload-artifact@v4
      with:
        name: test-warnings-${{ env.JOB_ID }}
        path: ./files/warnings-*.txt
        retention-days: 7
        if-no-files-found: ignore
    - name: "Move coverage artifacts in separate directory"
      if: env.ENABLE_COVERAGE == 'true' && env.TEST_TYPES != 'Helm'
      shell: bash
      run: |
        mkdir ./files/coverage-reports
        mv ./files/coverage*.xml ./files/coverage-reports/ || true
    - name: "Upload all coverage reports to codecov"
      uses: codecov/codecov-action@b9fd7d16f6d7d1b5d2bec1a2887e65ceed900238
      env:
        CODECOV_TOKEN: ${{ inputs.codecov-token }}
      if: env.ENABLE_COVERAGE == 'true' && env.TEST_TYPES != 'Helm' && inputs.python-version != '3.12'
      with:
        name: coverage-${{env.JOB_ID}}
        flags: python-${{ env.PYTHON_MAJOR_MINOR_VERSION }},${{ env.BACKEND }}-${{ env.BACKEND_VERSION }}
        directory: "./files/coverage-reports/"
