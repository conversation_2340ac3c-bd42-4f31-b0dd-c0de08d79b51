# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: 'Post tests on failure'
description: 'Run post tests actions on failure'
runs:
  using: "composite"
  steps:
    - name: "Upload airflow logs"
      uses: actions/upload-artifact@v4
      with:
        name: airflow-logs-${{env.JOB_ID}}
        path: './files/airflow_logs*'
        retention-days: 7
        if-no-files-found: ignore
    - name: "Upload container logs"
      uses: actions/upload-artifact@v4
      with:
        name: container-logs-${{env.JOB_ID}}
        path: "./files/container_logs*"
        retention-days: 7
        if-no-files-found: ignore
    - name: "Upload other logs"
      uses: actions/upload-artifact@v4
      with:
        name: container-logs-${{env.JOB_ID}}
        path: "./files/other_logs*"
        retention-days: 7
        if-no-files-found: ignore
