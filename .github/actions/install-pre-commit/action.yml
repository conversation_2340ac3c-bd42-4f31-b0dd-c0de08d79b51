# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
---
name: 'Install pre-commit'
description: 'Installs pre-commit and related packages'
inputs:
  python-version:
    description: 'Python version to use'
    default: "3.9"
  uv-version:
    description: 'uv version to use'
    default: "0.7.3"  # Keep this comment to allow automatic replacement of uv version
  pre-commit-version:
    description: 'pre-commit version to use'
    default: "4.2.0"  # Keep this comment to allow automatic replacement of pre-commit version
  pre-commit-uv-version:
    description: 'pre-commit-uv version to use'
    default: "4.1.4"  # Keep this comment to allow automatic replacement of pre-commit-uv version
runs:
  using: "composite"
  steps:
    - name: Install pre-commit, uv, and pre-commit-uv
      shell: bash
      env:
        UV_VERSION: ${{inputs.uv-version}}
        PRE_COMMIT_VERSION: ${{inputs.pre-commit-version}}
        PRE_COMMIT_UV_VERSION: ${{inputs.pre-commit-uv-version}}
      run: |
        pip install uv==${UV_VERSION} || true
        uv tool install pre-commit==${PRE_COMMIT_VERSION} --with uv==${UV_VERSION} \
        --with pre-commit-uv==${PRE_COMMIT_UV_VERSION}
      working-directory: ${{ github.workspace }}
    # We need to use tar file with archive to restore all the permissions and symlinks
    - name: "Delete ~.cache"
      run: |
        du ~/ --max-depth=2
        echo
        echo Deleting ~/.cache
        echo
        rm -rf ~/.cache
        echo
      shell: bash
    - name: "Restore pre-commit cache"
      uses: apache/infrastructure-actions/stash/restore@1c35b5ccf8fba5d4c3fdf25a045ca91aa0cbc468
      with:
        key: cache-pre-commit-v4-${{ inputs.python-version }}-${{ hashFiles('.pre-commit-config.yaml') }}
        path: /tmp/
      id: restore-pre-commit-cache
    - name: "Check if pre-commit cache tarball exists"
      shell: bash
      run: |
        if [ -f /tmp/cache-pre-commit.tar.gz ]; then
          echo "✅ Cache tarball found: /tmp/cache-pre-commit.tar.gz"
        else
          echo "❌ Cache tarball missing. Expected /tmp/cache-pre-commit.tar.gz"
          exit 1
        fi
      if: steps.restore-pre-commit-cache.outputs.stash-hit == 'true'
    - name: "Restore .cache from the tar file"
      run: tar -C ~ -xzf /tmp/cache-pre-commit.tar.gz
      shell: bash
      if: steps.restore-pre-commit-cache.outputs.stash-hit == 'true'
    - name: "Show restored files"
      run: |
        echo "Restored files"
        du ~/ --max-depth=2
        echo
      shell: bash
      if: steps.restore-pre-commit-cache.outputs.stash-hit == 'true'
    - name: Install pre-commit hooks
      shell: bash
      run: pre-commit install-hooks || (cat ~/.cache/pre-commit/pre-commit.log && exit 1)
      working-directory: ${{ github.workspace }}
