{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "homepage": "/ui", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint --quiet && tsc --p tsconfig.app.json", "lint:fix": "eslint --fix && tsc --p tsconfig.app.json", "format": "pnpm prettier --write .", "preview": "vite preview", "codegen": "openapi-merge-cli && openapi-rq -i openapi.merged.json -c axios --format prettier -o openapi-gen --operationId", "test": "vitest run", "coverage": "vitest run --coverage"}, "dependencies": {"@chakra-ui/anatomy": "^2.3.4", "@chakra-ui/react": "^3.17.0", "@codemirror/lang-json": "^6.0.1", "@emotion/react": "^11.14.0", "@tanstack/react-query": "^5.75.1", "@tanstack/react-table": "^8.21.3", "@types/debounce-promise": "^3.1.9", "@uiw/codemirror-themes-all": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "@visx/group": "^3.12.0", "@visx/shape": "^3.12.0", "@xyflow/react": "^12.4.4", "axios": "^1.8.4", "chakra-react-select": "6.1.0", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.1.0", "dayjs": "^1.11.13", "debounce-promise": "^3.1.2", "elkjs": "^0.10.0", "html-to-image": "^1.11.13", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.1", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-innertext": "^1.1.5", "react-json-view": "^1.21.3", "react-markdown": "^9.1.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.30.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "use-debounce": "^10.0.4", "usehooks-ts": "^3.1.1", "zustand": "^5.0.4"}, "devDependencies": {"@7nohe/openapi-react-query-codegen": "^1.6.2", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.25.1", "@stylistic/eslint-plugin": "^2.13.0", "@tanstack/eslint-plugin-query": "^5.74.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^22.15.3", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "^2.1.9", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.12.3", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unicorn": "^55.0.0", "globals": "^15.15.0", "happy-dom": "^17.4.6", "msw": "^2.7.5", "openapi-merge-cli": "^1.3.2", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.31.1", "vite": "^5.4.19", "vite-plugin-css-injected-by-js": "^3.5.2", "vitest": "^2.1.9", "web-worker": "^1.5.0"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "esbuild", "msw"]}}