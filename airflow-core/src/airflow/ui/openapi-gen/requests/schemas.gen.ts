// This file is auto-generated by @hey-api/openapi-ts

export const $AppBuilderMenuItemResponse = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    href: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Href",
    },
    category: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Category",
    },
  },
  additionalProperties: true,
  type: "object",
  required: ["name"],
  title: "AppBuilderMenuItemResponse",
  description: "Serializer for AppBuilder Menu Item responses.",
} as const;

export const $AppBuilderViewResponse = {
  properties: {
    name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Name",
    },
    category: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Category",
    },
    view: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "View",
    },
    label: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Label",
    },
  },
  additionalProperties: true,
  type: "object",
  title: "AppBuilderViewResponse",
  description: "Serializer for AppBuilder View responses.",
} as const;

export const $AssetAliasCollectionResponse = {
  properties: {
    asset_aliases: {
      items: {
        $ref: "#/components/schemas/AssetAliasResponse",
      },
      type: "array",
      title: "Asset Aliases",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["asset_aliases", "total_entries"],
  title: "AssetAliasCollectionResponse",
  description: "Asset alias collection response.",
} as const;

export const $AssetAliasResponse = {
  properties: {
    id: {
      type: "integer",
      title: "Id",
    },
    name: {
      type: "string",
      title: "Name",
    },
    group: {
      type: "string",
      title: "Group",
    },
  },
  type: "object",
  required: ["id", "name", "group"],
  title: "AssetAliasResponse",
  description: "Asset alias serializer for responses.",
} as const;

export const $AssetCollectionResponse = {
  properties: {
    assets: {
      items: {
        $ref: "#/components/schemas/AssetResponse",
      },
      type: "array",
      title: "Assets",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["assets", "total_entries"],
  title: "AssetCollectionResponse",
  description: "Asset collection response.",
} as const;

export const $AssetEventCollectionResponse = {
  properties: {
    asset_events: {
      items: {
        $ref: "#/components/schemas/AssetEventResponse",
      },
      type: "array",
      title: "Asset Events",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["asset_events", "total_entries"],
  title: "AssetEventCollectionResponse",
  description: "Asset event collection response.",
} as const;

export const $AssetEventResponse = {
  properties: {
    id: {
      type: "integer",
      title: "Id",
    },
    asset_id: {
      type: "integer",
      title: "Asset Id",
    },
    uri: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Uri",
    },
    name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Name",
    },
    group: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Group",
    },
    extra: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Extra",
    },
    source_task_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Source Task Id",
    },
    source_dag_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Source Dag Id",
    },
    source_run_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Source Run Id",
    },
    source_map_index: {
      type: "integer",
      title: "Source Map Index",
    },
    created_dagruns: {
      items: {
        $ref: "#/components/schemas/DagRunAssetReference",
      },
      type: "array",
      title: "Created Dagruns",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
  },
  type: "object",
  required: ["id", "asset_id", "source_map_index", "created_dagruns", "timestamp"],
  title: "AssetEventResponse",
  description: "Asset event serializer for responses.",
} as const;

export const $AssetResponse = {
  properties: {
    id: {
      type: "integer",
      title: "Id",
    },
    name: {
      type: "string",
      title: "Name",
    },
    uri: {
      type: "string",
      title: "Uri",
    },
    group: {
      type: "string",
      title: "Group",
    },
    extra: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Extra",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    updated_at: {
      type: "string",
      format: "date-time",
      title: "Updated At",
    },
    consuming_dags: {
      items: {
        $ref: "#/components/schemas/DagScheduleAssetReference",
      },
      type: "array",
      title: "Consuming Dags",
    },
    producing_tasks: {
      items: {
        $ref: "#/components/schemas/TaskOutletAssetReference",
      },
      type: "array",
      title: "Producing Tasks",
    },
    aliases: {
      items: {
        $ref: "#/components/schemas/AssetAliasResponse",
      },
      type: "array",
      title: "Aliases",
    },
    last_asset_event: {
      anyOf: [
        {
          $ref: "#/components/schemas/LastAssetEventResponse",
        },
        {
          type: "null",
        },
      ],
    },
  },
  type: "object",
  required: [
    "id",
    "name",
    "uri",
    "group",
    "created_at",
    "updated_at",
    "consuming_dags",
    "producing_tasks",
    "aliases",
  ],
  title: "AssetResponse",
  description: "Asset serializer for responses.",
} as const;

export const $BackfillCollectionResponse = {
  properties: {
    backfills: {
      items: {
        $ref: "#/components/schemas/BackfillResponse",
      },
      type: "array",
      title: "Backfills",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["backfills", "total_entries"],
  title: "BackfillCollectionResponse",
  description: "Backfill Collection serializer for responses.",
} as const;

export const $BackfillPostBody = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    from_date: {
      type: "string",
      format: "date-time",
      title: "From Date",
    },
    to_date: {
      type: "string",
      format: "date-time",
      title: "To Date",
    },
    run_backwards: {
      type: "boolean",
      title: "Run Backwards",
      default: false,
    },
    dag_run_conf: {
      additionalProperties: true,
      type: "object",
      title: "Dag Run Conf",
      default: {},
    },
    reprocess_behavior: {
      $ref: "#/components/schemas/ReprocessBehavior",
      default: "none",
    },
    max_active_runs: {
      type: "integer",
      title: "Max Active Runs",
      default: 10,
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["dag_id", "from_date", "to_date"],
  title: "BackfillPostBody",
  description: "Object used for create backfill request.",
} as const;

export const $BackfillResponse = {
  properties: {
    id: {
      type: "integer",
      minimum: 0,
      title: "Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    from_date: {
      type: "string",
      format: "date-time",
      title: "From Date",
    },
    to_date: {
      type: "string",
      format: "date-time",
      title: "To Date",
    },
    dag_run_conf: {
      additionalProperties: true,
      type: "object",
      title: "Dag Run Conf",
    },
    is_paused: {
      type: "boolean",
      title: "Is Paused",
    },
    reprocess_behavior: {
      $ref: "#/components/schemas/ReprocessBehavior",
    },
    max_active_runs: {
      type: "integer",
      title: "Max Active Runs",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    completed_at: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Completed At",
    },
    updated_at: {
      type: "string",
      format: "date-time",
      title: "Updated At",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: [
    "id",
    "dag_id",
    "from_date",
    "to_date",
    "dag_run_conf",
    "is_paused",
    "reprocess_behavior",
    "max_active_runs",
    "created_at",
    "completed_at",
    "updated_at",
    "dag_display_name",
  ],
  title: "BackfillResponse",
  description: "Base serializer for Backfill.",
} as const;

export const $BaseInfoResponse = {
  properties: {
    status: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Status",
    },
  },
  type: "object",
  required: ["status"],
  title: "BaseInfoResponse",
  description: "Base info serializer for responses.",
} as const;

export const $BulkAction = {
  type: "string",
  enum: ["create", "delete", "update"],
  title: "BulkAction",
  description: "Bulk Action to be performed on the used model.",
} as const;

export const $BulkActionNotOnExistence = {
  type: "string",
  enum: ["fail", "skip"],
  title: "BulkActionNotOnExistence",
  description: "Bulk Action to be taken if the entity does not exist.",
} as const;

export const $BulkActionOnExistence = {
  type: "string",
  enum: ["fail", "skip", "overwrite"],
  title: "BulkActionOnExistence",
  description: "Bulk Action to be taken if the entity already exists or not.",
} as const;

export const $BulkActionResponse = {
  properties: {
    success: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Success",
      description: "A list of unique id/key representing successful operations.",
      default: [],
    },
    errors: {
      items: {
        additionalProperties: true,
        type: "object",
      },
      type: "array",
      title: "Errors",
      description:
        "A list of errors encountered during the operation, each containing details about the issue.",
      default: [],
    },
  },
  type: "object",
  title: "BulkActionResponse",
  description: `Serializer for individual bulk action responses.

Represents the outcome of a single bulk operation (create, update, or delete).
The response includes a list of successful keys and any errors encountered during the operation.
This structure helps users understand which key actions succeeded and which failed.`,
} as const;

export const $BulkBody_ConnectionBody_ = {
  properties: {
    actions: {
      items: {
        oneOf: [
          {
            $ref: "#/components/schemas/BulkCreateAction_ConnectionBody_",
          },
          {
            $ref: "#/components/schemas/BulkUpdateAction_ConnectionBody_",
          },
          {
            $ref: "#/components/schemas/BulkDeleteAction_ConnectionBody_",
          },
        ],
      },
      type: "array",
      title: "Actions",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["actions"],
  title: "BulkBody[ConnectionBody]",
} as const;

export const $BulkBody_PoolBody_ = {
  properties: {
    actions: {
      items: {
        oneOf: [
          {
            $ref: "#/components/schemas/BulkCreateAction_PoolBody_",
          },
          {
            $ref: "#/components/schemas/BulkUpdateAction_PoolBody_",
          },
          {
            $ref: "#/components/schemas/BulkDeleteAction_PoolBody_",
          },
        ],
      },
      type: "array",
      title: "Actions",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["actions"],
  title: "BulkBody[PoolBody]",
} as const;

export const $BulkBody_VariableBody_ = {
  properties: {
    actions: {
      items: {
        oneOf: [
          {
            $ref: "#/components/schemas/BulkCreateAction_VariableBody_",
          },
          {
            $ref: "#/components/schemas/BulkUpdateAction_VariableBody_",
          },
          {
            $ref: "#/components/schemas/BulkDeleteAction_VariableBody_",
          },
        ],
      },
      type: "array",
      title: "Actions",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["actions"],
  title: "BulkBody[VariableBody]",
} as const;

export const $BulkCreateAction_ConnectionBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/ConnectionBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be created.",
    },
    action_on_existence: {
      $ref: "#/components/schemas/BulkActionOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkCreateAction[ConnectionBody]",
} as const;

export const $BulkCreateAction_PoolBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/PoolBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be created.",
    },
    action_on_existence: {
      $ref: "#/components/schemas/BulkActionOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkCreateAction[PoolBody]",
} as const;

export const $BulkCreateAction_VariableBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/VariableBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be created.",
    },
    action_on_existence: {
      $ref: "#/components/schemas/BulkActionOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkCreateAction[VariableBody]",
} as const;

export const $BulkDeleteAction_ConnectionBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Entities",
      description: "A list of entity id/key to be deleted.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkDeleteAction[ConnectionBody]",
} as const;

export const $BulkDeleteAction_PoolBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Entities",
      description: "A list of entity id/key to be deleted.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkDeleteAction[PoolBody]",
} as const;

export const $BulkDeleteAction_VariableBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Entities",
      description: "A list of entity id/key to be deleted.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkDeleteAction[VariableBody]",
} as const;

export const $BulkResponse = {
  properties: {
    create: {
      anyOf: [
        {
          $ref: "#/components/schemas/BulkActionResponse",
        },
        {
          type: "null",
        },
      ],
      description: "Details of the bulk create operation, including successful keys and errors.",
    },
    update: {
      anyOf: [
        {
          $ref: "#/components/schemas/BulkActionResponse",
        },
        {
          type: "null",
        },
      ],
      description: "Details of the bulk update operation, including successful keys and errors.",
    },
    delete: {
      anyOf: [
        {
          $ref: "#/components/schemas/BulkActionResponse",
        },
        {
          type: "null",
        },
      ],
      description: "Details of the bulk delete operation, including successful keys and errors.",
    },
  },
  type: "object",
  title: "BulkResponse",
  description: `Serializer for responses to bulk entity operations.

This represents the results of create, update, and delete actions performed on entity in bulk.
Each action (if requested) is represented as a field containing details about successful keys and any encountered errors.
Fields are populated in the response only if the respective action was part of the request, else are set None.`,
} as const;

export const $BulkUpdateAction_ConnectionBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/ConnectionBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be updated.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkUpdateAction[ConnectionBody]",
} as const;

export const $BulkUpdateAction_PoolBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/PoolBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be updated.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkUpdateAction[PoolBody]",
} as const;

export const $BulkUpdateAction_VariableBody_ = {
  properties: {
    action: {
      $ref: "#/components/schemas/BulkAction",
      description: "The action to be performed on the entities.",
    },
    entities: {
      items: {
        $ref: "#/components/schemas/VariableBody",
      },
      type: "array",
      title: "Entities",
      description: "A list of entities to be updated.",
    },
    action_on_non_existence: {
      $ref: "#/components/schemas/BulkActionNotOnExistence",
      default: "fail",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["action", "entities"],
  title: "BulkUpdateAction[VariableBody]",
} as const;

export const $ClearTaskInstancesBody = {
  properties: {
    dry_run: {
      type: "boolean",
      title: "Dry Run",
      default: true,
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    only_failed: {
      type: "boolean",
      title: "Only Failed",
      default: true,
    },
    only_running: {
      type: "boolean",
      title: "Only Running",
      default: false,
    },
    reset_dag_runs: {
      type: "boolean",
      title: "Reset Dag Runs",
      default: true,
    },
    task_ids: {
      anyOf: [
        {
          items: {
            anyOf: [
              {
                type: "string",
              },
              {
                prefixItems: [
                  {
                    type: "string",
                  },
                  {
                    type: "integer",
                  },
                ],
                type: "array",
                maxItems: 2,
                minItems: 2,
              },
            ],
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Task Ids",
    },
    dag_run_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Run Id",
    },
    include_upstream: {
      type: "boolean",
      title: "Include Upstream",
      default: false,
    },
    include_downstream: {
      type: "boolean",
      title: "Include Downstream",
      default: false,
    },
    include_future: {
      type: "boolean",
      title: "Include Future",
      default: false,
    },
    include_past: {
      type: "boolean",
      title: "Include Past",
      default: false,
    },
  },
  additionalProperties: false,
  type: "object",
  title: "ClearTaskInstancesBody",
  description: "Request body for Clear Task Instances endpoint.",
} as const;

export const $Config = {
  properties: {
    sections: {
      items: {
        $ref: "#/components/schemas/ConfigSection",
      },
      type: "array",
      title: "Sections",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["sections"],
  title: "Config",
  description: "List of config sections with their options.",
} as const;

export const $ConfigOption = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    value: {
      anyOf: [
        {
          type: "string",
        },
        {
          prefixItems: [
            {
              type: "string",
            },
            {
              type: "string",
            },
          ],
          type: "array",
          maxItems: 2,
          minItems: 2,
        },
      ],
      title: "Value",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["key", "value"],
  title: "ConfigOption",
  description: "Config option.",
} as const;

export const $ConfigSection = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    options: {
      items: {
        $ref: "#/components/schemas/ConfigOption",
      },
      type: "array",
      title: "Options",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["name", "options"],
  title: "ConfigSection",
  description: "Config Section Schema.",
} as const;

export const $ConnectionBody = {
  properties: {
    connection_id: {
      type: "string",
      maxLength: 200,
      pattern: "^[\\w.-]+$",
      title: "Connection Id",
    },
    conn_type: {
      type: "string",
      title: "Conn Type",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    host: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Host",
    },
    login: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Login",
    },
    schema: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Schema",
    },
    port: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Port",
    },
    password: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Password",
    },
    extra: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Extra",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["connection_id", "conn_type"],
  title: "ConnectionBody",
  description: "Connection Serializer for requests body.",
} as const;

export const $ConnectionCollectionResponse = {
  properties: {
    connections: {
      items: {
        $ref: "#/components/schemas/ConnectionResponse",
      },
      type: "array",
      title: "Connections",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["connections", "total_entries"],
  title: "ConnectionCollectionResponse",
  description: "Connection Collection serializer for responses.",
} as const;

export const $ConnectionResponse = {
  properties: {
    connection_id: {
      type: "string",
      title: "Connection Id",
    },
    conn_type: {
      type: "string",
      title: "Conn Type",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    host: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Host",
    },
    login: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Login",
    },
    schema: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Schema",
    },
    port: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Port",
    },
    password: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Password",
    },
    extra: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Extra",
    },
  },
  type: "object",
  required: [
    "connection_id",
    "conn_type",
    "description",
    "host",
    "login",
    "schema",
    "port",
    "password",
    "extra",
  ],
  title: "ConnectionResponse",
  description: "Connection serializer for responses.",
} as const;

export const $ConnectionTestResponse = {
  properties: {
    status: {
      type: "boolean",
      title: "Status",
    },
    message: {
      type: "string",
      title: "Message",
    },
  },
  type: "object",
  required: ["status", "message"],
  title: "ConnectionTestResponse",
  description: "Connection Test serializer for responses.",
} as const;

export const $CreateAssetEventsBody = {
  properties: {
    asset_id: {
      type: "integer",
      title: "Asset Id",
    },
    extra: {
      additionalProperties: true,
      type: "object",
      title: "Extra",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["asset_id"],
  title: "CreateAssetEventsBody",
  description: "Create asset events request.",
} as const;

export const $DAGCollectionResponse = {
  properties: {
    dags: {
      items: {
        $ref: "#/components/schemas/DAGResponse",
      },
      type: "array",
      title: "Dags",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["dags", "total_entries"],
  title: "DAGCollectionResponse",
  description: "DAG Collection serializer for responses.",
} as const;

export const $DAGDetailsResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    is_paused: {
      type: "boolean",
      title: "Is Paused",
    },
    is_stale: {
      type: "boolean",
      title: "Is Stale",
    },
    last_parsed_time: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Parsed Time",
    },
    last_expired: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Expired",
    },
    bundle_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Name",
    },
    bundle_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Version",
    },
    relative_fileloc: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Relative Fileloc",
    },
    fileloc: {
      type: "string",
      title: "Fileloc",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    timetable_summary: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Summary",
    },
    timetable_description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Description",
    },
    tags: {
      items: {
        $ref: "#/components/schemas/DagTagResponse",
      },
      type: "array",
      title: "Tags",
    },
    max_active_tasks: {
      type: "integer",
      title: "Max Active Tasks",
    },
    max_active_runs: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Max Active Runs",
    },
    max_consecutive_failed_dag_runs: {
      type: "integer",
      title: "Max Consecutive Failed Dag Runs",
    },
    has_task_concurrency_limits: {
      type: "boolean",
      title: "Has Task Concurrency Limits",
    },
    has_import_errors: {
      type: "boolean",
      title: "Has Import Errors",
    },
    next_dagrun_logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Logical Date",
    },
    next_dagrun_data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval Start",
    },
    next_dagrun_data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval End",
    },
    next_dagrun_run_after: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Run After",
    },
    owners: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Owners",
    },
    catchup: {
      type: "boolean",
      title: "Catchup",
    },
    dag_run_timeout: {
      anyOf: [
        {
          type: "string",
          format: "duration",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Run Timeout",
    },
    asset_expression: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Asset Expression",
    },
    doc_md: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Doc Md",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    is_paused_upon_creation: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Is Paused Upon Creation",
    },
    params: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Params",
    },
    render_template_as_native_obj: {
      type: "boolean",
      title: "Render Template As Native Obj",
    },
    template_search_path: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Template Search Path",
    },
    timezone: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timezone",
    },
    last_parsed: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Parsed",
    },
    default_args: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Default Args",
    },
    owner_links: {
      anyOf: [
        {
          additionalProperties: {
            type: "string",
          },
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Owner Links",
    },
    file_token: {
      type: "string",
      title: "File Token",
      description: "Return file token.",
      readOnly: true,
    },
    concurrency: {
      type: "integer",
      title: "Concurrency",
      description: "Return max_active_tasks as concurrency.",
      readOnly: true,
    },
    latest_dag_version: {
      anyOf: [
        {
          $ref: "#/components/schemas/DagVersionResponse",
        },
        {
          type: "null",
        },
      ],
      description: "Return the latest DagVersion.",
      readOnly: true,
    },
  },
  type: "object",
  required: [
    "dag_id",
    "dag_display_name",
    "is_paused",
    "is_stale",
    "last_parsed_time",
    "last_expired",
    "bundle_name",
    "bundle_version",
    "relative_fileloc",
    "fileloc",
    "description",
    "timetable_summary",
    "timetable_description",
    "tags",
    "max_active_tasks",
    "max_active_runs",
    "max_consecutive_failed_dag_runs",
    "has_task_concurrency_limits",
    "has_import_errors",
    "next_dagrun_logical_date",
    "next_dagrun_data_interval_start",
    "next_dagrun_data_interval_end",
    "next_dagrun_run_after",
    "owners",
    "catchup",
    "dag_run_timeout",
    "asset_expression",
    "doc_md",
    "start_date",
    "end_date",
    "is_paused_upon_creation",
    "params",
    "render_template_as_native_obj",
    "template_search_path",
    "timezone",
    "last_parsed",
    "default_args",
    "file_token",
    "concurrency",
    "latest_dag_version",
  ],
  title: "DAGDetailsResponse",
  description: "Specific serializer for DAG Details responses.",
} as const;

export const $DAGPatchBody = {
  properties: {
    is_paused: {
      type: "boolean",
      title: "Is Paused",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["is_paused"],
  title: "DAGPatchBody",
  description: "Dag Serializer for updatable bodies.",
} as const;

export const $DAGResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    is_paused: {
      type: "boolean",
      title: "Is Paused",
    },
    is_stale: {
      type: "boolean",
      title: "Is Stale",
    },
    last_parsed_time: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Parsed Time",
    },
    last_expired: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Expired",
    },
    bundle_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Name",
    },
    bundle_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Version",
    },
    relative_fileloc: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Relative Fileloc",
    },
    fileloc: {
      type: "string",
      title: "Fileloc",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    timetable_summary: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Summary",
    },
    timetable_description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Description",
    },
    tags: {
      items: {
        $ref: "#/components/schemas/DagTagResponse",
      },
      type: "array",
      title: "Tags",
    },
    max_active_tasks: {
      type: "integer",
      title: "Max Active Tasks",
    },
    max_active_runs: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Max Active Runs",
    },
    max_consecutive_failed_dag_runs: {
      type: "integer",
      title: "Max Consecutive Failed Dag Runs",
    },
    has_task_concurrency_limits: {
      type: "boolean",
      title: "Has Task Concurrency Limits",
    },
    has_import_errors: {
      type: "boolean",
      title: "Has Import Errors",
    },
    next_dagrun_logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Logical Date",
    },
    next_dagrun_data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval Start",
    },
    next_dagrun_data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval End",
    },
    next_dagrun_run_after: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Run After",
    },
    owners: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Owners",
    },
    file_token: {
      type: "string",
      title: "File Token",
      description: "Return file token.",
      readOnly: true,
    },
  },
  type: "object",
  required: [
    "dag_id",
    "dag_display_name",
    "is_paused",
    "is_stale",
    "last_parsed_time",
    "last_expired",
    "bundle_name",
    "bundle_version",
    "relative_fileloc",
    "fileloc",
    "description",
    "timetable_summary",
    "timetable_description",
    "tags",
    "max_active_tasks",
    "max_active_runs",
    "max_consecutive_failed_dag_runs",
    "has_task_concurrency_limits",
    "has_import_errors",
    "next_dagrun_logical_date",
    "next_dagrun_data_interval_start",
    "next_dagrun_data_interval_end",
    "next_dagrun_run_after",
    "owners",
    "file_token",
  ],
  title: "DAGResponse",
  description: "DAG serializer for responses.",
} as const;

export const $DAGRunClearBody = {
  properties: {
    dry_run: {
      type: "boolean",
      title: "Dry Run",
      default: true,
    },
    only_failed: {
      type: "boolean",
      title: "Only Failed",
      default: false,
    },
  },
  additionalProperties: false,
  type: "object",
  title: "DAGRunClearBody",
  description: "DAG Run serializer for clear endpoint body.",
} as const;

export const $DAGRunCollectionResponse = {
  properties: {
    dag_runs: {
      items: {
        $ref: "#/components/schemas/DAGRunResponse",
      },
      type: "array",
      title: "Dag Runs",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["dag_runs", "total_entries"],
  title: "DAGRunCollectionResponse",
  description: "DAG Run Collection serializer for responses.",
} as const;

export const $DAGRunPatchBody = {
  properties: {
    state: {
      anyOf: [
        {
          $ref: "#/components/schemas/DAGRunPatchStates",
        },
        {
          type: "null",
        },
      ],
    },
    note: {
      anyOf: [
        {
          type: "string",
          maxLength: 1000,
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
  },
  additionalProperties: false,
  type: "object",
  title: "DAGRunPatchBody",
  description: "DAG Run Serializer for PATCH requests.",
} as const;

export const $DAGRunPatchStates = {
  type: "string",
  enum: ["queued", "success", "failed"],
  title: "DAGRunPatchStates",
  description: "Enum for DAG Run states when updating a DAG Run.",
} as const;

export const $DAGRunResponse = {
  properties: {
    dag_run_id: {
      type: "string",
      title: "Dag Run Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    queued_at: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Queued At",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval Start",
    },
    data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval End",
    },
    run_after: {
      type: "string",
      format: "date-time",
      title: "Run After",
    },
    last_scheduling_decision: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Scheduling Decision",
    },
    run_type: {
      $ref: "#/components/schemas/DagRunType",
    },
    state: {
      $ref: "#/components/schemas/DagRunState",
    },
    triggered_by: {
      anyOf: [
        {
          $ref: "#/components/schemas/DagRunTriggeredByType",
        },
        {
          type: "null",
        },
      ],
    },
    conf: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Conf",
    },
    note: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
    dag_versions: {
      items: {
        $ref: "#/components/schemas/DagVersionResponse",
      },
      type: "array",
      title: "Dag Versions",
    },
    bundle_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Version",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: [
    "dag_run_id",
    "dag_id",
    "logical_date",
    "queued_at",
    "start_date",
    "end_date",
    "data_interval_start",
    "data_interval_end",
    "run_after",
    "last_scheduling_decision",
    "run_type",
    "state",
    "triggered_by",
    "conf",
    "note",
    "dag_versions",
    "bundle_version",
    "dag_display_name",
  ],
  title: "DAGRunResponse",
  description: "DAG Run serializer for responses.",
} as const;

export const $DAGRunsBatchBody = {
  properties: {
    order_by: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Order By",
    },
    page_offset: {
      type: "integer",
      minimum: 0,
      title: "Page Offset",
      default: 0,
    },
    page_limit: {
      type: "integer",
      minimum: 0,
      title: "Page Limit",
      default: 100,
    },
    dag_ids: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Ids",
    },
    states: {
      anyOf: [
        {
          items: {
            anyOf: [
              {
                $ref: "#/components/schemas/DagRunState",
              },
              {
                type: "null",
              },
            ],
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "States",
    },
    run_after_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Run After Gte",
    },
    run_after_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Run After Lte",
    },
    logical_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date Gte",
    },
    logical_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date Lte",
    },
    start_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date Gte",
    },
    start_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date Lte",
    },
    end_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date Gte",
    },
    end_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date Lte",
    },
  },
  additionalProperties: false,
  type: "object",
  title: "DAGRunsBatchBody",
  description: "List DAG Runs body for batch endpoint.",
} as const;

export const $DAGSourceResponse = {
  properties: {
    content: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Content",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    version_number: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Version Number",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: ["content", "dag_id", "version_number", "dag_display_name"],
  title: "DAGSourceResponse",
  description: "DAG Source serializer for responses.",
} as const;

export const $DAGTagCollectionResponse = {
  properties: {
    tags: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Tags",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["tags", "total_entries"],
  title: "DAGTagCollectionResponse",
  description: "DAG Tags Collection serializer for responses.",
} as const;

export const $DAGVersionCollectionResponse = {
  properties: {
    dag_versions: {
      items: {
        $ref: "#/components/schemas/DagVersionResponse",
      },
      type: "array",
      title: "Dag Versions",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["dag_versions", "total_entries"],
  title: "DAGVersionCollectionResponse",
  description: "DAG Version Collection serializer for responses.",
} as const;

export const $DAGWarningCollectionResponse = {
  properties: {
    dag_warnings: {
      items: {
        $ref: "#/components/schemas/DAGWarningResponse",
      },
      type: "array",
      title: "Dag Warnings",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["dag_warnings", "total_entries"],
  title: "DAGWarningCollectionResponse",
  description: "DAG warning collection serializer for responses.",
} as const;

export const $DAGWarningResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    warning_type: {
      $ref: "#/components/schemas/DagWarningType",
    },
    message: {
      type: "string",
      title: "Message",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
  },
  type: "object",
  required: ["dag_id", "warning_type", "message", "timestamp"],
  title: "DAGWarningResponse",
  description: "DAG Warning serializer for responses.",
} as const;

export const $DagProcessorInfoResponse = {
  properties: {
    status: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Status",
    },
    latest_dag_processor_heartbeat: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Latest Dag Processor Heartbeat",
    },
  },
  type: "object",
  required: ["status", "latest_dag_processor_heartbeat"],
  title: "DagProcessorInfoResponse",
  description: "DagProcessor info serializer for responses.",
} as const;

export const $DagRunAssetReference = {
  properties: {
    run_id: {
      type: "string",
      title: "Run Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    start_date: {
      type: "string",
      format: "date-time",
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    state: {
      type: "string",
      title: "State",
    },
    data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval Start",
    },
    data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval End",
    },
  },
  additionalProperties: false,
  type: "object",
  required: [
    "run_id",
    "dag_id",
    "logical_date",
    "start_date",
    "end_date",
    "state",
    "data_interval_start",
    "data_interval_end",
  ],
  title: "DagRunAssetReference",
  description: "DAGRun serializer for asset responses.",
} as const;

export const $DagRunState = {
  type: "string",
  enum: ["queued", "running", "success", "failed"],
  title: "DagRunState",
  description: `All possible states that a DagRun can be in.

These are "shared" with TaskInstanceState in some parts of the code,
so please ensure that their values always match the ones with the
same name in TaskInstanceState.`,
} as const;

export const $DagRunTriggeredByType = {
  type: "string",
  enum: ["cli", "operator", "rest_api", "ui", "test", "timetable", "asset", "backfill"],
  title: "DagRunTriggeredByType",
  description: "Class with TriggeredBy types for DagRun.",
} as const;

export const $DagRunType = {
  type: "string",
  enum: ["backfill", "scheduled", "manual", "asset_triggered"],
  title: "DagRunType",
  description: "Class with DagRun types.",
} as const;

export const $DagScheduleAssetReference = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    updated_at: {
      type: "string",
      format: "date-time",
      title: "Updated At",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["dag_id", "created_at", "updated_at"],
  title: "DagScheduleAssetReference",
  description: "DAG schedule reference serializer for assets.",
} as const;

export const $DagStatsCollectionResponse = {
  properties: {
    dags: {
      items: {
        $ref: "#/components/schemas/DagStatsResponse",
      },
      type: "array",
      title: "Dags",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["dags", "total_entries"],
  title: "DagStatsCollectionResponse",
  description: "DAG Stats Collection serializer for responses.",
} as const;

export const $DagStatsResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    stats: {
      items: {
        $ref: "#/components/schemas/DagStatsStateResponse",
      },
      type: "array",
      title: "Stats",
    },
  },
  type: "object",
  required: ["dag_id", "stats"],
  title: "DagStatsResponse",
  description: "DAG Stats serializer for responses.",
} as const;

export const $DagStatsStateResponse = {
  properties: {
    state: {
      $ref: "#/components/schemas/DagRunState",
    },
    count: {
      type: "integer",
      title: "Count",
    },
  },
  type: "object",
  required: ["state", "count"],
  title: "DagStatsStateResponse",
  description: "DagStatsState serializer for responses.",
} as const;

export const $DagTagResponse = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
  },
  type: "object",
  required: ["name", "dag_id"],
  title: "DagTagResponse",
  description: "DAG Tag serializer for responses.",
} as const;

export const $DagVersionResponse = {
  properties: {
    id: {
      type: "string",
      format: "uuid",
      title: "Id",
    },
    version_number: {
      type: "integer",
      title: "Version Number",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    bundle_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Name",
    },
    bundle_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Version",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    bundle_url: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Url",
      readOnly: true,
    },
  },
  type: "object",
  required: [
    "id",
    "version_number",
    "dag_id",
    "bundle_name",
    "bundle_version",
    "created_at",
    "dag_display_name",
    "bundle_url",
  ],
  title: "DagVersionResponse",
  description: "Dag Version serializer for responses.",
} as const;

export const $DagWarningType = {
  type: "string",
  enum: ["asset conflict", "non-existent pool"],
  title: "DagWarningType",
  description: `Enum for DAG warning types.

This is the set of allowable values for the \`\`warning_type\`\` field
in the DagWarning model.`,
} as const;

export const $DryRunBackfillCollectionResponse = {
  properties: {
    backfills: {
      items: {
        $ref: "#/components/schemas/DryRunBackfillResponse",
      },
      type: "array",
      title: "Backfills",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["backfills", "total_entries"],
  title: "DryRunBackfillCollectionResponse",
  description: "Backfill collection serializer for responses in dry-run mode.",
} as const;

export const $DryRunBackfillResponse = {
  properties: {
    logical_date: {
      type: "string",
      format: "date-time",
      title: "Logical Date",
    },
  },
  type: "object",
  required: ["logical_date"],
  title: "DryRunBackfillResponse",
  description: "Backfill serializer for responses in dry-run mode.",
} as const;

export const $EventLogCollectionResponse = {
  properties: {
    event_logs: {
      items: {
        $ref: "#/components/schemas/EventLogResponse",
      },
      type: "array",
      title: "Event Logs",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["event_logs", "total_entries"],
  title: "EventLogCollectionResponse",
  description: "Event Log Collection Response.",
} as const;

export const $EventLogResponse = {
  properties: {
    event_log_id: {
      type: "integer",
      title: "Event Log Id",
    },
    when: {
      type: "string",
      format: "date-time",
      title: "When",
    },
    dag_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Id",
    },
    task_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Task Id",
    },
    run_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Run Id",
    },
    map_index: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Map Index",
    },
    try_number: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Try Number",
    },
    event: {
      type: "string",
      title: "Event",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    owner: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Owner",
    },
    extra: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Extra",
    },
    dag_display_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: [
    "event_log_id",
    "when",
    "dag_id",
    "task_id",
    "run_id",
    "map_index",
    "try_number",
    "event",
    "logical_date",
    "owner",
    "extra",
  ],
  title: "EventLogResponse",
  description: "Event Log Response.",
} as const;

export const $ExternalLogUrlResponse = {
  properties: {
    url: {
      type: "string",
      title: "Url",
    },
  },
  type: "object",
  required: ["url"],
  title: "ExternalLogUrlResponse",
  description: "Response for the external log URL endpoint.",
} as const;

export const $ExtraLinkCollectionResponse = {
  properties: {
    extra_links: {
      additionalProperties: {
        anyOf: [
          {
            type: "string",
          },
          {
            type: "null",
          },
        ],
      },
      type: "object",
      title: "Extra Links",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["extra_links", "total_entries"],
  title: "ExtraLinkCollectionResponse",
  description: "Extra Links Response.",
} as const;

export const $FastAPIAppResponse = {
  properties: {
    app: {
      type: "string",
      title: "App",
    },
    url_prefix: {
      type: "string",
      title: "Url Prefix",
    },
    name: {
      type: "string",
      title: "Name",
    },
  },
  additionalProperties: true,
  type: "object",
  required: ["app", "url_prefix", "name"],
  title: "FastAPIAppResponse",
  description: "Serializer for Plugin FastAPI App responses.",
} as const;

export const $FastAPIRootMiddlewareResponse = {
  properties: {
    middleware: {
      type: "string",
      title: "Middleware",
    },
    name: {
      type: "string",
      title: "Name",
    },
  },
  additionalProperties: true,
  type: "object",
  required: ["middleware", "name"],
  title: "FastAPIRootMiddlewareResponse",
  description: "Serializer for Plugin FastAPI root middleware responses.",
} as const;

export const $HTTPExceptionResponse = {
  properties: {
    detail: {
      anyOf: [
        {
          type: "string",
        },
        {
          additionalProperties: true,
          type: "object",
        },
      ],
      title: "Detail",
    },
  },
  type: "object",
  required: ["detail"],
  title: "HTTPExceptionResponse",
  description: "HTTPException Model used for error response.",
} as const;

export const $HTTPValidationError = {
  properties: {
    detail: {
      items: {
        $ref: "#/components/schemas/ValidationError",
      },
      type: "array",
      title: "Detail",
    },
  },
  type: "object",
  title: "HTTPValidationError",
} as const;

export const $HealthInfoResponse = {
  properties: {
    metadatabase: {
      $ref: "#/components/schemas/BaseInfoResponse",
    },
    scheduler: {
      $ref: "#/components/schemas/SchedulerInfoResponse",
    },
    triggerer: {
      $ref: "#/components/schemas/TriggererInfoResponse",
    },
    dag_processor: {
      anyOf: [
        {
          $ref: "#/components/schemas/DagProcessorInfoResponse",
        },
        {
          type: "null",
        },
      ],
    },
  },
  type: "object",
  required: ["metadatabase", "scheduler", "triggerer"],
  title: "HealthInfoResponse",
  description: "Health serializer for responses.",
} as const;

export const $ImportErrorCollectionResponse = {
  properties: {
    import_errors: {
      items: {
        $ref: "#/components/schemas/ImportErrorResponse",
      },
      type: "array",
      title: "Import Errors",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["import_errors", "total_entries"],
  title: "ImportErrorCollectionResponse",
  description: "Import Error Collection Response.",
} as const;

export const $ImportErrorResponse = {
  properties: {
    import_error_id: {
      type: "integer",
      title: "Import Error Id",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
    filename: {
      type: "string",
      title: "Filename",
    },
    bundle_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Name",
    },
    stack_trace: {
      type: "string",
      title: "Stack Trace",
    },
  },
  type: "object",
  required: ["import_error_id", "timestamp", "filename", "bundle_name", "stack_trace"],
  title: "ImportErrorResponse",
  description: "Import Error Response.",
} as const;

export const $JobCollectionResponse = {
  properties: {
    jobs: {
      items: {
        $ref: "#/components/schemas/JobResponse",
      },
      type: "array",
      title: "Jobs",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["jobs", "total_entries"],
  title: "JobCollectionResponse",
  description: "Job Collection Response.",
} as const;

export const $JobResponse = {
  properties: {
    id: {
      type: "integer",
      title: "Id",
    },
    dag_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Id",
    },
    state: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "State",
    },
    job_type: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Job Type",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    latest_heartbeat: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Latest Heartbeat",
    },
    executor_class: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Executor Class",
    },
    hostname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Hostname",
    },
    unixname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Unixname",
    },
    dag_display_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: [
    "id",
    "dag_id",
    "state",
    "job_type",
    "start_date",
    "end_date",
    "latest_heartbeat",
    "executor_class",
    "hostname",
    "unixname",
  ],
  title: "JobResponse",
  description: "Job serializer for responses.",
} as const;

export const $JsonValue = {} as const;

export const $LastAssetEventResponse = {
  properties: {
    id: {
      anyOf: [
        {
          type: "integer",
          minimum: 0,
        },
        {
          type: "null",
        },
      ],
      title: "Id",
    },
    timestamp: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Timestamp",
    },
  },
  type: "object",
  title: "LastAssetEventResponse",
  description: "Last asset event response serializer.",
} as const;

export const $PatchTaskInstanceBody = {
  properties: {
    new_state: {
      anyOf: [
        {
          $ref: "#/components/schemas/TaskInstanceState",
        },
        {
          type: "null",
        },
      ],
    },
    note: {
      anyOf: [
        {
          type: "string",
          maxLength: 1000,
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
    include_upstream: {
      type: "boolean",
      title: "Include Upstream",
      default: false,
    },
    include_downstream: {
      type: "boolean",
      title: "Include Downstream",
      default: false,
    },
    include_future: {
      type: "boolean",
      title: "Include Future",
      default: false,
    },
    include_past: {
      type: "boolean",
      title: "Include Past",
      default: false,
    },
  },
  additionalProperties: false,
  type: "object",
  title: "PatchTaskInstanceBody",
  description: "Request body for Clear Task Instances endpoint.",
} as const;

export const $PluginCollectionResponse = {
  properties: {
    plugins: {
      items: {
        $ref: "#/components/schemas/PluginResponse",
      },
      type: "array",
      title: "Plugins",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["plugins", "total_entries"],
  title: "PluginCollectionResponse",
  description: "Plugin Collection serializer.",
} as const;

export const $PluginImportErrorCollectionResponse = {
  properties: {
    import_errors: {
      items: {
        $ref: "#/components/schemas/PluginImportErrorResponse",
      },
      type: "array",
      title: "Import Errors",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["import_errors", "total_entries"],
  title: "PluginImportErrorCollectionResponse",
  description: "Plugin Import Error Collection serializer.",
} as const;

export const $PluginImportErrorResponse = {
  properties: {
    source: {
      type: "string",
      title: "Source",
    },
    error: {
      type: "string",
      title: "Error",
    },
  },
  type: "object",
  required: ["source", "error"],
  title: "PluginImportErrorResponse",
  description: "Plugin Import Error serializer for responses.",
} as const;

export const $PluginResponse = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    macros: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Macros",
    },
    flask_blueprints: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Flask Blueprints",
    },
    fastapi_apps: {
      items: {
        $ref: "#/components/schemas/FastAPIAppResponse",
      },
      type: "array",
      title: "Fastapi Apps",
    },
    fastapi_root_middlewares: {
      items: {
        $ref: "#/components/schemas/FastAPIRootMiddlewareResponse",
      },
      type: "array",
      title: "Fastapi Root Middlewares",
    },
    appbuilder_views: {
      items: {
        $ref: "#/components/schemas/AppBuilderViewResponse",
      },
      type: "array",
      title: "Appbuilder Views",
    },
    appbuilder_menu_items: {
      items: {
        $ref: "#/components/schemas/AppBuilderMenuItemResponse",
      },
      type: "array",
      title: "Appbuilder Menu Items",
    },
    global_operator_extra_links: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Global Operator Extra Links",
    },
    operator_extra_links: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Operator Extra Links",
    },
    source: {
      type: "string",
      title: "Source",
    },
    listeners: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Listeners",
    },
    timetables: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Timetables",
    },
  },
  type: "object",
  required: [
    "name",
    "macros",
    "flask_blueprints",
    "fastapi_apps",
    "fastapi_root_middlewares",
    "appbuilder_views",
    "appbuilder_menu_items",
    "global_operator_extra_links",
    "operator_extra_links",
    "source",
    "listeners",
    "timetables",
  ],
  title: "PluginResponse",
  description: "Plugin serializer.",
} as const;

export const $PoolBody = {
  properties: {
    name: {
      type: "string",
      maxLength: 256,
      title: "Name",
    },
    slots: {
      type: "integer",
      title: "Slots",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    include_deferred: {
      type: "boolean",
      title: "Include Deferred",
      default: false,
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["name", "slots"],
  title: "PoolBody",
  description: "Pool serializer for post bodies.",
} as const;

export const $PoolCollectionResponse = {
  properties: {
    pools: {
      items: {
        $ref: "#/components/schemas/PoolResponse",
      },
      type: "array",
      title: "Pools",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["pools", "total_entries"],
  title: "PoolCollectionResponse",
  description: "Pool Collection serializer for responses.",
} as const;

export const $PoolPatchBody = {
  properties: {
    pool: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Pool",
    },
    slots: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Slots",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    include_deferred: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Include Deferred",
    },
  },
  additionalProperties: false,
  type: "object",
  title: "PoolPatchBody",
  description: "Pool serializer for patch bodies.",
} as const;

export const $PoolResponse = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    slots: {
      type: "integer",
      title: "Slots",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    include_deferred: {
      type: "boolean",
      title: "Include Deferred",
    },
    occupied_slots: {
      type: "integer",
      title: "Occupied Slots",
    },
    running_slots: {
      type: "integer",
      title: "Running Slots",
    },
    queued_slots: {
      type: "integer",
      title: "Queued Slots",
    },
    scheduled_slots: {
      type: "integer",
      title: "Scheduled Slots",
    },
    open_slots: {
      type: "integer",
      title: "Open Slots",
    },
    deferred_slots: {
      type: "integer",
      title: "Deferred Slots",
    },
  },
  type: "object",
  required: [
    "name",
    "slots",
    "description",
    "include_deferred",
    "occupied_slots",
    "running_slots",
    "queued_slots",
    "scheduled_slots",
    "open_slots",
    "deferred_slots",
  ],
  title: "PoolResponse",
  description: "Pool serializer for responses.",
} as const;

export const $ProviderCollectionResponse = {
  properties: {
    providers: {
      items: {
        $ref: "#/components/schemas/ProviderResponse",
      },
      type: "array",
      title: "Providers",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["providers", "total_entries"],
  title: "ProviderCollectionResponse",
  description: "Provider Collection serializer for responses.",
} as const;

export const $ProviderResponse = {
  properties: {
    package_name: {
      type: "string",
      title: "Package Name",
    },
    description: {
      type: "string",
      title: "Description",
    },
    version: {
      type: "string",
      title: "Version",
    },
  },
  type: "object",
  required: ["package_name", "description", "version"],
  title: "ProviderResponse",
  description: "Provider serializer for responses.",
} as const;

export const $QueuedEventCollectionResponse = {
  properties: {
    queued_events: {
      items: {
        $ref: "#/components/schemas/QueuedEventResponse",
      },
      type: "array",
      title: "Queued Events",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["queued_events", "total_entries"],
  title: "QueuedEventCollectionResponse",
  description: "Queued Event Collection serializer for responses.",
} as const;

export const $QueuedEventResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    asset_id: {
      type: "integer",
      title: "Asset Id",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: ["dag_id", "asset_id", "created_at", "dag_display_name"],
  title: "QueuedEventResponse",
  description: "Queued Event serializer for responses..",
} as const;

export const $ReprocessBehavior = {
  type: "string",
  enum: ["failed", "completed", "none"],
  title: "ReprocessBehavior",
  description: `Internal enum for setting reprocess behavior in a backfill.

:meta private:`,
} as const;

export const $SchedulerInfoResponse = {
  properties: {
    status: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Status",
    },
    latest_scheduler_heartbeat: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Latest Scheduler Heartbeat",
    },
  },
  type: "object",
  required: ["status", "latest_scheduler_heartbeat"],
  title: "SchedulerInfoResponse",
  description: "Scheduler info serializer for responses.",
} as const;

export const $StructuredLogMessage = {
  properties: {
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
    event: {
      type: "string",
      title: "Event",
    },
  },
  additionalProperties: true,
  type: "object",
  required: ["event"],
  title: "StructuredLogMessage",
  description: "An individual log message.",
} as const;

export const $TaskCollectionResponse = {
  properties: {
    tasks: {
      items: {
        $ref: "#/components/schemas/TaskResponse",
      },
      type: "array",
      title: "Tasks",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["tasks", "total_entries"],
  title: "TaskCollectionResponse",
  description: "Task collection serializer for responses.",
} as const;

export const $TaskDependencyCollectionResponse = {
  properties: {
    dependencies: {
      items: {
        $ref: "#/components/schemas/TaskDependencyResponse",
      },
      type: "array",
      title: "Dependencies",
    },
  },
  type: "object",
  required: ["dependencies"],
  title: "TaskDependencyCollectionResponse",
  description: "Task scheduling dependencies collection serializer for responses.",
} as const;

export const $TaskDependencyResponse = {
  properties: {
    name: {
      type: "string",
      title: "Name",
    },
    reason: {
      type: "string",
      title: "Reason",
    },
  },
  type: "object",
  required: ["name", "reason"],
  title: "TaskDependencyResponse",
  description: "Task Dependency serializer for responses.",
} as const;

export const $TaskInstanceCollectionResponse = {
  properties: {
    task_instances: {
      items: {
        $ref: "#/components/schemas/TaskInstanceResponse",
      },
      type: "array",
      title: "Task Instances",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["task_instances", "total_entries"],
  title: "TaskInstanceCollectionResponse",
  description: "Task Instance Collection serializer for responses.",
} as const;

export const $TaskInstanceHistoryCollectionResponse = {
  properties: {
    task_instances: {
      items: {
        $ref: "#/components/schemas/TaskInstanceHistoryResponse",
      },
      type: "array",
      title: "Task Instances",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["task_instances", "total_entries"],
  title: "TaskInstanceHistoryCollectionResponse",
  description: "TaskInstanceHistory Collection serializer for responses.",
} as const;

export const $TaskInstanceHistoryResponse = {
  properties: {
    task_id: {
      type: "string",
      title: "Task Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    dag_run_id: {
      type: "string",
      title: "Dag Run Id",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    duration: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Duration",
    },
    state: {
      anyOf: [
        {
          $ref: "#/components/schemas/TaskInstanceState",
        },
        {
          type: "null",
        },
      ],
    },
    try_number: {
      type: "integer",
      title: "Try Number",
    },
    max_tries: {
      type: "integer",
      title: "Max Tries",
    },
    task_display_name: {
      type: "string",
      title: "Task Display Name",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    hostname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Hostname",
    },
    unixname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Unixname",
    },
    pool: {
      type: "string",
      title: "Pool",
    },
    pool_slots: {
      type: "integer",
      title: "Pool Slots",
    },
    queue: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Queue",
    },
    priority_weight: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Priority Weight",
    },
    operator: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Operator",
    },
    queued_when: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Queued When",
    },
    scheduled_when: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Scheduled When",
    },
    pid: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Pid",
    },
    executor: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Executor",
    },
    executor_config: {
      type: "string",
      title: "Executor Config",
    },
    dag_version: {
      anyOf: [
        {
          $ref: "#/components/schemas/DagVersionResponse",
        },
        {
          type: "null",
        },
      ],
    },
  },
  type: "object",
  required: [
    "task_id",
    "dag_id",
    "dag_run_id",
    "map_index",
    "start_date",
    "end_date",
    "duration",
    "state",
    "try_number",
    "max_tries",
    "task_display_name",
    "dag_display_name",
    "hostname",
    "unixname",
    "pool",
    "pool_slots",
    "queue",
    "priority_weight",
    "operator",
    "queued_when",
    "scheduled_when",
    "pid",
    "executor",
    "executor_config",
    "dag_version",
  ],
  title: "TaskInstanceHistoryResponse",
  description: "TaskInstanceHistory serializer for responses.",
} as const;

export const $TaskInstanceResponse = {
  properties: {
    id: {
      type: "string",
      title: "Id",
    },
    task_id: {
      type: "string",
      title: "Task Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    dag_run_id: {
      type: "string",
      title: "Dag Run Id",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    run_after: {
      type: "string",
      format: "date-time",
      title: "Run After",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    duration: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Duration",
    },
    state: {
      anyOf: [
        {
          $ref: "#/components/schemas/TaskInstanceState",
        },
        {
          type: "null",
        },
      ],
    },
    try_number: {
      type: "integer",
      title: "Try Number",
    },
    max_tries: {
      type: "integer",
      title: "Max Tries",
    },
    task_display_name: {
      type: "string",
      title: "Task Display Name",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    hostname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Hostname",
    },
    unixname: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Unixname",
    },
    pool: {
      type: "string",
      title: "Pool",
    },
    pool_slots: {
      type: "integer",
      title: "Pool Slots",
    },
    queue: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Queue",
    },
    priority_weight: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Priority Weight",
    },
    operator: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Operator",
    },
    queued_when: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Queued When",
    },
    scheduled_when: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Scheduled When",
    },
    pid: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Pid",
    },
    executor: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Executor",
    },
    executor_config: {
      type: "string",
      title: "Executor Config",
    },
    note: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
    rendered_map_index: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Rendered Map Index",
    },
    rendered_fields: {
      additionalProperties: true,
      type: "object",
      title: "Rendered Fields",
    },
    trigger: {
      anyOf: [
        {
          $ref: "#/components/schemas/TriggerResponse",
        },
        {
          type: "null",
        },
      ],
    },
    triggerer_job: {
      anyOf: [
        {
          $ref: "#/components/schemas/JobResponse",
        },
        {
          type: "null",
        },
      ],
    },
    dag_version: {
      anyOf: [
        {
          $ref: "#/components/schemas/DagVersionResponse",
        },
        {
          type: "null",
        },
      ],
    },
  },
  type: "object",
  required: [
    "id",
    "task_id",
    "dag_id",
    "dag_run_id",
    "map_index",
    "logical_date",
    "run_after",
    "start_date",
    "end_date",
    "duration",
    "state",
    "try_number",
    "max_tries",
    "task_display_name",
    "dag_display_name",
    "hostname",
    "unixname",
    "pool",
    "pool_slots",
    "queue",
    "priority_weight",
    "operator",
    "queued_when",
    "scheduled_when",
    "pid",
    "executor",
    "executor_config",
    "note",
    "rendered_map_index",
    "trigger",
    "triggerer_job",
    "dag_version",
  ],
  title: "TaskInstanceResponse",
  description: "TaskInstance serializer for responses.",
} as const;

export const $TaskInstanceState = {
  type: "string",
  enum: [
    "removed",
    "scheduled",
    "queued",
    "running",
    "success",
    "restarting",
    "failed",
    "up_for_retry",
    "up_for_reschedule",
    "upstream_failed",
    "skipped",
    "deferred",
  ],
  title: "TaskInstanceState",
  description: `All possible states that a Task Instance can be in.

Note that None is also allowed, so always use this in a type hint with Optional.`,
} as const;

export const $TaskInstancesBatchBody = {
  properties: {
    dag_ids: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Ids",
    },
    dag_run_ids: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Run Ids",
    },
    task_ids: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Task Ids",
    },
    state: {
      anyOf: [
        {
          items: {
            anyOf: [
              {
                $ref: "#/components/schemas/TaskInstanceState",
              },
              {
                type: "null",
              },
            ],
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "State",
    },
    run_after_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Run After Gte",
    },
    run_after_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Run After Lte",
    },
    logical_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date Gte",
    },
    logical_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date Lte",
    },
    start_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date Gte",
    },
    start_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date Lte",
    },
    end_date_gte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date Gte",
    },
    end_date_lte: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date Lte",
    },
    duration_gte: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Duration Gte",
    },
    duration_lte: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Duration Lte",
    },
    pool: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Pool",
    },
    queue: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Queue",
    },
    executor: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Executor",
    },
    page_offset: {
      type: "integer",
      minimum: 0,
      title: "Page Offset",
      default: 0,
    },
    page_limit: {
      type: "integer",
      minimum: 0,
      title: "Page Limit",
      default: 100,
    },
    order_by: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Order By",
    },
  },
  additionalProperties: false,
  type: "object",
  title: "TaskInstancesBatchBody",
  description: "Task Instance body for get batch.",
} as const;

export const $TaskInstancesLogResponse = {
  properties: {
    content: {
      anyOf: [
        {
          items: {
            $ref: "#/components/schemas/StructuredLogMessage",
          },
          type: "array",
        },
        {
          items: {
            type: "string",
          },
          type: "array",
        },
      ],
      title: "Content",
    },
    continuation_token: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Continuation Token",
    },
  },
  type: "object",
  required: ["content", "continuation_token"],
  title: "TaskInstancesLogResponse",
  description: "Log serializer for responses.",
} as const;

export const $TaskOutletAssetReference = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    task_id: {
      type: "string",
      title: "Task Id",
    },
    created_at: {
      type: "string",
      format: "date-time",
      title: "Created At",
    },
    updated_at: {
      type: "string",
      format: "date-time",
      title: "Updated At",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["dag_id", "task_id", "created_at", "updated_at"],
  title: "TaskOutletAssetReference",
  description: "Task outlet reference serializer for assets.",
} as const;

export const $TaskResponse = {
  properties: {
    task_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Task Id",
    },
    task_display_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Task Display Name",
    },
    owner: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Owner",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    trigger_rule: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Trigger Rule",
    },
    depends_on_past: {
      type: "boolean",
      title: "Depends On Past",
    },
    wait_for_downstream: {
      type: "boolean",
      title: "Wait For Downstream",
    },
    retries: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Retries",
    },
    queue: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Queue",
    },
    pool: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Pool",
    },
    pool_slots: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Pool Slots",
    },
    execution_timeout: {
      anyOf: [
        {
          $ref: "#/components/schemas/TimeDelta",
        },
        {
          type: "null",
        },
      ],
    },
    retry_delay: {
      anyOf: [
        {
          $ref: "#/components/schemas/TimeDelta",
        },
        {
          type: "null",
        },
      ],
    },
    retry_exponential_backoff: {
      type: "boolean",
      title: "Retry Exponential Backoff",
    },
    priority_weight: {
      anyOf: [
        {
          type: "number",
        },
        {
          type: "null",
        },
      ],
      title: "Priority Weight",
    },
    weight_rule: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Weight Rule",
    },
    ui_color: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Ui Color",
    },
    ui_fgcolor: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Ui Fgcolor",
    },
    template_fields: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Template Fields",
    },
    downstream_task_ids: {
      anyOf: [
        {
          items: {
            type: "string",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Downstream Task Ids",
    },
    doc_md: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Doc Md",
    },
    operator_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Operator Name",
    },
    params: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Params",
    },
    class_ref: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Class Ref",
    },
    is_mapped: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Is Mapped",
    },
    extra_links: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Extra Links",
      description: "Extract and return extra_links.",
      readOnly: true,
    },
  },
  type: "object",
  required: [
    "task_id",
    "task_display_name",
    "owner",
    "start_date",
    "end_date",
    "trigger_rule",
    "depends_on_past",
    "wait_for_downstream",
    "retries",
    "queue",
    "pool",
    "pool_slots",
    "execution_timeout",
    "retry_delay",
    "retry_exponential_backoff",
    "priority_weight",
    "weight_rule",
    "ui_color",
    "ui_fgcolor",
    "template_fields",
    "downstream_task_ids",
    "doc_md",
    "operator_name",
    "params",
    "class_ref",
    "is_mapped",
    "extra_links",
  ],
  title: "TaskResponse",
  description: "Task serializer for responses.",
} as const;

export const $TimeDelta = {
  properties: {
    __type: {
      type: "string",
      title: "Type",
      default: "TimeDelta",
    },
    days: {
      type: "integer",
      title: "Days",
    },
    seconds: {
      type: "integer",
      title: "Seconds",
    },
    microseconds: {
      type: "integer",
      title: "Microseconds",
    },
  },
  type: "object",
  required: ["days", "seconds", "microseconds"],
  title: "TimeDelta",
  description: "TimeDelta can be used to interact with datetime.timedelta objects.",
} as const;

export const $TriggerDAGRunPostBody = {
  properties: {
    dag_run_id: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Dag Run Id",
    },
    data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval Start",
    },
    data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval End",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    run_after: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Run After",
    },
    conf: {
      additionalProperties: true,
      type: "object",
      title: "Conf",
    },
    note: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["logical_date"],
  title: "TriggerDAGRunPostBody",
  description: "Trigger DAG Run Serializer for POST body.",
} as const;

export const $TriggerResponse = {
  properties: {
    id: {
      type: "integer",
      title: "Id",
    },
    classpath: {
      type: "string",
      title: "Classpath",
    },
    kwargs: {
      type: "string",
      title: "Kwargs",
    },
    created_date: {
      type: "string",
      format: "date-time",
      title: "Created Date",
    },
    triggerer_id: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Triggerer Id",
    },
  },
  type: "object",
  required: ["id", "classpath", "kwargs", "created_date", "triggerer_id"],
  title: "TriggerResponse",
  description: "Trigger serializer for responses.",
} as const;

export const $TriggererInfoResponse = {
  properties: {
    status: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Status",
    },
    latest_triggerer_heartbeat: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Latest Triggerer Heartbeat",
    },
  },
  type: "object",
  required: ["status", "latest_triggerer_heartbeat"],
  title: "TriggererInfoResponse",
  description: "Triggerer info serializer for responses.",
} as const;

export const $UiPluginCollectionResponse = {
  properties: {
    plugins: {
      items: {
        $ref: "#/components/schemas/UiPluginResponse",
      },
      type: "array",
      title: "Plugins",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["plugins", "total_entries"],
  title: "UiPluginCollectionResponse",
  description: "UI Plugin Collection serializer.",
} as const;

export const $UiPluginResponse = {
  properties: {
    slug: {
      type: "string",
      title: "Slug",
    },
    label: {
      type: "string",
      title: "Label",
    },
    icon: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Icon",
    },
    entry: {
      type: "string",
      title: "Entry",
    },
    type: {
      type: "string",
      enum: ["iframe", "module"],
      title: "Type",
    },
    permissions: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Permissions",
    },
    plugin_name: {
      type: "string",
      title: "Plugin Name",
    },
  },
  type: "object",
  required: ["slug", "label", "entry", "type", "permissions", "plugin_name"],
  title: "UiPluginResponse",
  description: "UI Plugin serializer for responses.",
} as const;

export const $ValidationError = {
  properties: {
    loc: {
      items: {
        anyOf: [
          {
            type: "string",
          },
          {
            type: "integer",
          },
        ],
      },
      type: "array",
      title: "Location",
    },
    msg: {
      type: "string",
      title: "Message",
    },
    type: {
      type: "string",
      title: "Error Type",
    },
  },
  type: "object",
  required: ["loc", "msg", "type"],
  title: "ValidationError",
} as const;

export const $VariableBody = {
  properties: {
    key: {
      type: "string",
      maxLength: 250,
      title: "Key",
    },
    value: {
      $ref: "#/components/schemas/JsonValue",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["key", "value"],
  title: "VariableBody",
  description: "Variable serializer for bodies.",
} as const;

export const $VariableCollectionResponse = {
  properties: {
    variables: {
      items: {
        $ref: "#/components/schemas/VariableResponse",
      },
      type: "array",
      title: "Variables",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["variables", "total_entries"],
  title: "VariableCollectionResponse",
  description: "Variable Collection serializer for responses.",
} as const;

export const $VariableResponse = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    value: {
      type: "string",
      title: "Value",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    is_encrypted: {
      type: "boolean",
      title: "Is Encrypted",
    },
  },
  type: "object",
  required: ["key", "value", "description", "is_encrypted"],
  title: "VariableResponse",
  description: "Variable serializer for responses.",
} as const;

export const $VersionInfo = {
  properties: {
    version: {
      type: "string",
      title: "Version",
    },
    git_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Git Version",
    },
  },
  type: "object",
  required: ["version", "git_version"],
  title: "VersionInfo",
  description: "Version information serializer for responses.",
} as const;

export const $XComCollectionResponse = {
  properties: {
    xcom_entries: {
      items: {
        $ref: "#/components/schemas/XComResponse",
      },
      type: "array",
      title: "Xcom Entries",
    },
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
  },
  type: "object",
  required: ["xcom_entries", "total_entries"],
  title: "XComCollectionResponse",
  description: "XCom Collection serializer for responses.",
} as const;

export const $XComCreateBody = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    value: {
      title: "Value",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
      default: -1,
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["key", "value"],
  title: "XComCreateBody",
  description: "Payload serializer for creating an XCom entry.",
} as const;

export const $XComResponse = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
    },
    task_id: {
      type: "string",
      title: "Task Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    run_id: {
      type: "string",
      title: "Run Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
  },
  type: "object",
  required: [
    "key",
    "timestamp",
    "logical_date",
    "map_index",
    "task_id",
    "dag_id",
    "run_id",
    "dag_display_name",
  ],
  title: "XComResponse",
  description: "Serializer for a xcom item.",
} as const;

export const $XComResponseNative = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
    },
    task_id: {
      type: "string",
      title: "Task Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    run_id: {
      type: "string",
      title: "Run Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    value: {
      title: "Value",
    },
  },
  type: "object",
  required: [
    "key",
    "timestamp",
    "logical_date",
    "map_index",
    "task_id",
    "dag_id",
    "run_id",
    "dag_display_name",
    "value",
  ],
  title: "XComResponseNative",
  description: "XCom response serializer with native return type.",
} as const;

export const $XComResponseString = {
  properties: {
    key: {
      type: "string",
      title: "Key",
    },
    timestamp: {
      type: "string",
      format: "date-time",
      title: "Timestamp",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
    },
    task_id: {
      type: "string",
      title: "Task Id",
    },
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    run_id: {
      type: "string",
      title: "Run Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    value: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Value",
    },
  },
  type: "object",
  required: [
    "key",
    "timestamp",
    "logical_date",
    "map_index",
    "task_id",
    "dag_id",
    "run_id",
    "dag_display_name",
    "value",
  ],
  title: "XComResponseString",
  description: "XCom response serializer with string return type.",
} as const;

export const $XComUpdateBody = {
  properties: {
    value: {
      title: "Value",
    },
    map_index: {
      type: "integer",
      title: "Map Index",
      default: -1,
    },
  },
  additionalProperties: false,
  type: "object",
  required: ["value"],
  title: "XComUpdateBody",
  description: "Payload serializer for updating an XCom entry.",
} as const;

export const $BaseEdgeResponse = {
  properties: {
    source_id: {
      type: "string",
      title: "Source Id",
    },
    target_id: {
      type: "string",
      title: "Target Id",
    },
  },
  type: "object",
  required: ["source_id", "target_id"],
  title: "BaseEdgeResponse",
  description: "Base Edge serializer for responses.",
} as const;

export const $BaseGraphResponse = {
  properties: {
    edges: {
      items: {
        $ref: "#/components/schemas/BaseEdgeResponse",
      },
      type: "array",
      title: "Edges",
    },
    nodes: {
      items: {
        $ref: "#/components/schemas/BaseNodeResponse",
      },
      type: "array",
      title: "Nodes",
    },
  },
  type: "object",
  required: ["edges", "nodes"],
  title: "BaseGraphResponse",
  description: "Base Graph serializer for responses.",
} as const;

export const $BaseNodeResponse = {
  properties: {
    id: {
      type: "string",
      title: "Id",
    },
    label: {
      type: "string",
      title: "Label",
    },
    type: {
      type: "string",
      enum: [
        "join",
        "task",
        "asset-condition",
        "asset",
        "asset-alias",
        "asset-name-ref",
        "asset-uri-ref",
        "dag",
        "sensor",
        "trigger",
      ],
      title: "Type",
    },
  },
  type: "object",
  required: ["id", "label", "type"],
  title: "BaseNodeResponse",
  description: "Base Node serializer for responses.",
} as const;

export const $ConfigResponse = {
  properties: {
    page_size: {
      type: "integer",
      title: "Page Size",
    },
    auto_refresh_interval: {
      type: "integer",
      title: "Auto Refresh Interval",
    },
    hide_paused_dags_by_default: {
      type: "boolean",
      title: "Hide Paused Dags By Default",
    },
    instance_name: {
      type: "string",
      title: "Instance Name",
    },
    enable_swagger_ui: {
      type: "boolean",
      title: "Enable Swagger Ui",
    },
    require_confirmation_dag_change: {
      type: "boolean",
      title: "Require Confirmation Dag Change",
    },
    default_wrap: {
      type: "boolean",
      title: "Default Wrap",
    },
    test_connection: {
      type: "string",
      title: "Test Connection",
    },
    dashboard_alert: {
      items: {
        $ref: "#/components/schemas/UIAlert",
      },
      type: "array",
      title: "Dashboard Alert",
    },
    show_external_log_redirect: {
      type: "boolean",
      title: "Show External Log Redirect",
    },
    external_log_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "External Log Name",
    },
  },
  type: "object",
  required: [
    "page_size",
    "auto_refresh_interval",
    "hide_paused_dags_by_default",
    "instance_name",
    "enable_swagger_ui",
    "require_confirmation_dag_change",
    "default_wrap",
    "test_connection",
    "dashboard_alert",
    "show_external_log_redirect",
  ],
  title: "ConfigResponse",
  description: "configuration serializer.",
} as const;

export const $ConnectionHookFieldBehavior = {
  properties: {
    hidden: {
      type: "boolean",
      title: "Hidden",
      description: "Flag if the form field should be hidden.",
      default: false,
    },
    title: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Title",
      description:
        "Label / title for the field that should be displayed, if re-labelling is needed. Use `None` to display standard title.",
    },
    placeholder: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Placeholder",
      description: "Placeholder text that should be populated to the form.",
    },
  },
  type: "object",
  title: "ConnectionHookFieldBehavior",
  description: "A class to store the behavior of each standard field of a Hook.",
} as const;

export const $ConnectionHookMetaData = {
  properties: {
    connection_type: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Connection Type",
    },
    hook_class_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Hook Class Name",
    },
    default_conn_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Default Conn Name",
    },
    hook_name: {
      type: "string",
      title: "Hook Name",
    },
    standard_fields: {
      anyOf: [
        {
          $ref: "#/components/schemas/StandardHookFields",
        },
        {
          type: "null",
        },
      ],
    },
    extra_fields: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Extra Fields",
    },
  },
  type: "object",
  required: [
    "connection_type",
    "hook_class_name",
    "default_conn_name",
    "hook_name",
    "standard_fields",
    "extra_fields",
  ],
  title: "ConnectionHookMetaData",
  description: `Response model for Hook information == Connection type meta data.

It is used to transfer providers information loaded by providers_manager such that
the API server/Web UI can use this data to render connection form UI.`,
} as const;

export const $DAGRunStates = {
  properties: {
    queued: {
      type: "integer",
      title: "Queued",
    },
    running: {
      type: "integer",
      title: "Running",
    },
    success: {
      type: "integer",
      title: "Success",
    },
    failed: {
      type: "integer",
      title: "Failed",
    },
  },
  type: "object",
  required: ["queued", "running", "success", "failed"],
  title: "DAGRunStates",
  description: "DAG Run States for responses.",
} as const;

export const $DAGRunTypes = {
  properties: {
    backfill: {
      type: "integer",
      title: "Backfill",
    },
    scheduled: {
      type: "integer",
      title: "Scheduled",
    },
    manual: {
      type: "integer",
      title: "Manual",
    },
    asset_triggered: {
      type: "integer",
      title: "Asset Triggered",
    },
  },
  type: "object",
  required: ["backfill", "scheduled", "manual", "asset_triggered"],
  title: "DAGRunTypes",
  description: "DAG Run Types for responses.",
} as const;

export const $DAGWithLatestDagRunsCollectionResponse = {
  properties: {
    total_entries: {
      type: "integer",
      title: "Total Entries",
    },
    dags: {
      items: {
        $ref: "#/components/schemas/DAGWithLatestDagRunsResponse",
      },
      type: "array",
      title: "Dags",
    },
  },
  type: "object",
  required: ["total_entries", "dags"],
  title: "DAGWithLatestDagRunsCollectionResponse",
  description: "DAG with latest dag runs collection response serializer.",
} as const;

export const $DAGWithLatestDagRunsResponse = {
  properties: {
    dag_id: {
      type: "string",
      title: "Dag Id",
    },
    dag_display_name: {
      type: "string",
      title: "Dag Display Name",
    },
    is_paused: {
      type: "boolean",
      title: "Is Paused",
    },
    is_stale: {
      type: "boolean",
      title: "Is Stale",
    },
    last_parsed_time: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Parsed Time",
    },
    last_expired: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Last Expired",
    },
    bundle_name: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Name",
    },
    bundle_version: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Bundle Version",
    },
    relative_fileloc: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Relative Fileloc",
    },
    fileloc: {
      type: "string",
      title: "Fileloc",
    },
    description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Description",
    },
    timetable_summary: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Summary",
    },
    timetable_description: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Timetable Description",
    },
    tags: {
      items: {
        $ref: "#/components/schemas/DagTagResponse",
      },
      type: "array",
      title: "Tags",
    },
    max_active_tasks: {
      type: "integer",
      title: "Max Active Tasks",
    },
    max_active_runs: {
      anyOf: [
        {
          type: "integer",
        },
        {
          type: "null",
        },
      ],
      title: "Max Active Runs",
    },
    max_consecutive_failed_dag_runs: {
      type: "integer",
      title: "Max Consecutive Failed Dag Runs",
    },
    has_task_concurrency_limits: {
      type: "boolean",
      title: "Has Task Concurrency Limits",
    },
    has_import_errors: {
      type: "boolean",
      title: "Has Import Errors",
    },
    next_dagrun_logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Logical Date",
    },
    next_dagrun_data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval Start",
    },
    next_dagrun_data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Data Interval End",
    },
    next_dagrun_run_after: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Next Dagrun Run After",
    },
    owners: {
      items: {
        type: "string",
      },
      type: "array",
      title: "Owners",
    },
    asset_expression: {
      anyOf: [
        {
          additionalProperties: true,
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Asset Expression",
    },
    latest_dag_runs: {
      items: {
        $ref: "#/components/schemas/DAGRunResponse",
      },
      type: "array",
      title: "Latest Dag Runs",
    },
    file_token: {
      type: "string",
      title: "File Token",
      description: "Return file token.",
      readOnly: true,
    },
  },
  type: "object",
  required: [
    "dag_id",
    "dag_display_name",
    "is_paused",
    "is_stale",
    "last_parsed_time",
    "last_expired",
    "bundle_name",
    "bundle_version",
    "relative_fileloc",
    "fileloc",
    "description",
    "timetable_summary",
    "timetable_description",
    "tags",
    "max_active_tasks",
    "max_active_runs",
    "max_consecutive_failed_dag_runs",
    "has_task_concurrency_limits",
    "has_import_errors",
    "next_dagrun_logical_date",
    "next_dagrun_data_interval_start",
    "next_dagrun_data_interval_end",
    "next_dagrun_run_after",
    "owners",
    "asset_expression",
    "latest_dag_runs",
    "file_token",
  ],
  title: "DAGWithLatestDagRunsResponse",
  description: "DAG with latest dag runs response serializer.",
} as const;

export const $DashboardDagStatsResponse = {
  properties: {
    active_dag_count: {
      type: "integer",
      title: "Active Dag Count",
    },
    failed_dag_count: {
      type: "integer",
      title: "Failed Dag Count",
    },
    running_dag_count: {
      type: "integer",
      title: "Running Dag Count",
    },
    queued_dag_count: {
      type: "integer",
      title: "Queued Dag Count",
    },
  },
  type: "object",
  required: ["active_dag_count", "failed_dag_count", "running_dag_count", "queued_dag_count"],
  title: "DashboardDagStatsResponse",
  description: "Dashboard DAG Stats serializer for responses.",
} as const;

export const $EdgeResponse = {
  properties: {
    source_id: {
      type: "string",
      title: "Source Id",
    },
    target_id: {
      type: "string",
      title: "Target Id",
    },
    is_setup_teardown: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Is Setup Teardown",
    },
    label: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Label",
    },
    is_source_asset: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Is Source Asset",
    },
  },
  type: "object",
  required: ["source_id", "target_id"],
  title: "EdgeResponse",
  description: "Edge serializer for responses.",
} as const;

export const $ExtraMenuItem = {
  properties: {
    text: {
      type: "string",
      title: "Text",
    },
    href: {
      type: "string",
      title: "Href",
    },
  },
  type: "object",
  required: ["text", "href"],
  title: "ExtraMenuItem",
} as const;

export const $GridDAGRunwithTIs = {
  properties: {
    dag_run_id: {
      type: "string",
      title: "Dag Run Id",
    },
    queued_at: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Queued At",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    run_after: {
      type: "string",
      format: "date-time",
      title: "Run After",
    },
    state: {
      $ref: "#/components/schemas/DagRunState",
    },
    run_type: {
      $ref: "#/components/schemas/DagRunType",
    },
    logical_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Logical Date",
    },
    data_interval_start: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval Start",
    },
    data_interval_end: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Data Interval End",
    },
    note: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
    task_instances: {
      items: {
        $ref: "#/components/schemas/GridTaskInstanceSummary",
      },
      type: "array",
      title: "Task Instances",
    },
  },
  type: "object",
  required: [
    "dag_run_id",
    "queued_at",
    "start_date",
    "end_date",
    "run_after",
    "state",
    "run_type",
    "logical_date",
    "data_interval_start",
    "data_interval_end",
    "note",
    "task_instances",
  ],
  title: "GridDAGRunwithTIs",
  description: "DAG Run model for the Grid UI.",
} as const;

export const $GridResponse = {
  properties: {
    dag_runs: {
      items: {
        $ref: "#/components/schemas/GridDAGRunwithTIs",
      },
      type: "array",
      title: "Dag Runs",
    },
    structure: {
      $ref: "#/components/schemas/StructureDataResponse",
    },
  },
  type: "object",
  required: ["dag_runs", "structure"],
  title: "GridResponse",
  description: "Response model for the Grid UI.",
} as const;

export const $GridTaskInstanceSummary = {
  properties: {
    task_id: {
      type: "string",
      title: "Task Id",
    },
    try_number: {
      type: "integer",
      title: "Try Number",
    },
    start_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Start Date",
    },
    end_date: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "End Date",
    },
    queued_dttm: {
      anyOf: [
        {
          type: "string",
          format: "date-time",
        },
        {
          type: "null",
        },
      ],
      title: "Queued Dttm",
    },
    child_states: {
      anyOf: [
        {
          additionalProperties: {
            type: "integer",
          },
          type: "object",
        },
        {
          type: "null",
        },
      ],
      title: "Child States",
    },
    task_count: {
      type: "integer",
      title: "Task Count",
    },
    state: {
      anyOf: [
        {
          $ref: "#/components/schemas/TaskInstanceState",
        },
        {
          type: "null",
        },
      ],
    },
    note: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Note",
    },
  },
  type: "object",
  required: [
    "task_id",
    "try_number",
    "start_date",
    "end_date",
    "queued_dttm",
    "child_states",
    "task_count",
    "state",
    "note",
  ],
  title: "GridTaskInstanceSummary",
  description: "Task Instance Summary model for the Grid UI.",
} as const;

export const $HistoricalMetricDataResponse = {
  properties: {
    dag_run_types: {
      $ref: "#/components/schemas/DAGRunTypes",
    },
    dag_run_states: {
      $ref: "#/components/schemas/DAGRunStates",
    },
    task_instance_states: {
      $ref: "#/components/schemas/TaskInstanceStateCount",
    },
  },
  type: "object",
  required: ["dag_run_types", "dag_run_states", "task_instance_states"],
  title: "HistoricalMetricDataResponse",
  description: "Historical Metric Data serializer for responses.",
} as const;

export const $MenuItem = {
  type: "string",
  enum: [
    "Assets",
    "Audit Log",
    "Config",
    "Connections",
    "Dags",
    "Docs",
    "Plugins",
    "Pools",
    "Providers",
    "Variables",
    "XComs",
  ],
  title: "MenuItem",
  description: "Define all menu items defined in the menu.",
} as const;

export const $MenuItemCollectionResponse = {
  properties: {
    authorized_menu_items: {
      items: {
        $ref: "#/components/schemas/MenuItem",
      },
      type: "array",
      title: "Authorized Menu Items",
    },
    extra_menu_items: {
      items: {
        $ref: "#/components/schemas/ExtraMenuItem",
      },
      type: "array",
      title: "Extra Menu Items",
    },
  },
  type: "object",
  required: ["authorized_menu_items", "extra_menu_items"],
  title: "MenuItemCollectionResponse",
  description: "Menu Item Collection serializer for responses.",
} as const;

export const $NodeResponse = {
  properties: {
    id: {
      type: "string",
      title: "Id",
    },
    label: {
      type: "string",
      title: "Label",
    },
    type: {
      type: "string",
      enum: [
        "join",
        "task",
        "asset-condition",
        "asset",
        "asset-alias",
        "asset-name-ref",
        "asset-uri-ref",
        "dag",
        "sensor",
        "trigger",
      ],
      title: "Type",
    },
    children: {
      anyOf: [
        {
          items: {
            $ref: "#/components/schemas/NodeResponse",
          },
          type: "array",
        },
        {
          type: "null",
        },
      ],
      title: "Children",
    },
    is_mapped: {
      anyOf: [
        {
          type: "boolean",
        },
        {
          type: "null",
        },
      ],
      title: "Is Mapped",
    },
    tooltip: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Tooltip",
    },
    setup_teardown_type: {
      anyOf: [
        {
          type: "string",
          enum: ["setup", "teardown"],
        },
        {
          type: "null",
        },
      ],
      title: "Setup Teardown Type",
    },
    operator: {
      anyOf: [
        {
          type: "string",
        },
        {
          type: "null",
        },
      ],
      title: "Operator",
    },
    asset_condition_type: {
      anyOf: [
        {
          type: "string",
          enum: ["or-gate", "and-gate"],
        },
        {
          type: "null",
        },
      ],
      title: "Asset Condition Type",
    },
  },
  type: "object",
  required: ["id", "label", "type"],
  title: "NodeResponse",
  description: "Node serializer for responses.",
} as const;

export const $StandardHookFields = {
  properties: {
    description: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
    url_schema: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
    host: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
    port: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
    login: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
    password: {
      anyOf: [
        {
          $ref: "#/components/schemas/ConnectionHookFieldBehavior",
        },
        {
          type: "null",
        },
      ],
    },
  },
  type: "object",
  required: ["description", "url_schema", "host", "port", "login", "password"],
  title: "StandardHookFields",
  description: "Standard fields of a Hook that a form will render.",
} as const;

export const $StructureDataResponse = {
  properties: {
    edges: {
      items: {
        $ref: "#/components/schemas/EdgeResponse",
      },
      type: "array",
      title: "Edges",
    },
    nodes: {
      items: {
        $ref: "#/components/schemas/NodeResponse",
      },
      type: "array",
      title: "Nodes",
    },
  },
  type: "object",
  required: ["edges", "nodes"],
  title: "StructureDataResponse",
  description: "Structure Data serializer for responses.",
} as const;

export const $TaskInstanceStateCount = {
  properties: {
    no_status: {
      type: "integer",
      title: "No Status",
    },
    removed: {
      type: "integer",
      title: "Removed",
    },
    scheduled: {
      type: "integer",
      title: "Scheduled",
    },
    queued: {
      type: "integer",
      title: "Queued",
    },
    running: {
      type: "integer",
      title: "Running",
    },
    success: {
      type: "integer",
      title: "Success",
    },
    restarting: {
      type: "integer",
      title: "Restarting",
    },
    failed: {
      type: "integer",
      title: "Failed",
    },
    up_for_retry: {
      type: "integer",
      title: "Up For Retry",
    },
    up_for_reschedule: {
      type: "integer",
      title: "Up For Reschedule",
    },
    upstream_failed: {
      type: "integer",
      title: "Upstream Failed",
    },
    skipped: {
      type: "integer",
      title: "Skipped",
    },
    deferred: {
      type: "integer",
      title: "Deferred",
    },
  },
  type: "object",
  required: [
    "no_status",
    "removed",
    "scheduled",
    "queued",
    "running",
    "success",
    "restarting",
    "failed",
    "up_for_retry",
    "up_for_reschedule",
    "upstream_failed",
    "skipped",
    "deferred",
  ],
  title: "TaskInstanceStateCount",
  description: "TaskInstance serializer for responses.",
} as const;

export const $UIAlert = {
  properties: {
    text: {
      type: "string",
      title: "Text",
    },
    category: {
      type: "string",
      enum: ["info", "warning", "error"],
      title: "Category",
    },
  },
  type: "object",
  required: ["text", "category"],
  title: "UIAlert",
  description: "Optional alert to be shown at the top of the page.",
} as const;
