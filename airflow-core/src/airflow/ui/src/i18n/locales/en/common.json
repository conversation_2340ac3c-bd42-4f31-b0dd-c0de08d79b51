{"defaultToGraphView": "Default to graph view", "defaultToGridView": "Default to grid view", "logout": "Logout", "switchToDarkMode": "Switch to Dark Mode", "switchToLightMode": "Switch to Light Mode", "timezone": "timezone", "user": "User", "language": {"chinese": "Traditional Chinese", "english": "English", "select": "Select Language"}, "nav": {"home": "Home", "assets": "Assets", "browse": "Browse", "admin": "Admin", "docs": "Docs", "plugins": "Plugins"}, "browse": {"auditLog": "<PERSON>t Log", "xcoms": "XComs"}, "admin": {"Variables": "Variables", "Pools": "Pools", "Providers": "Providers", "Plugins": "Plugins", "Connections": "Connections", "Config": "Config"}, "timeRange": {"duration": "Duration", "lastHour": "Last Hour", "last12Hours": "Last 12 Hours", "last24Hours": "Last 24 Hours", "pastWeek": "Past Week"}, "docs": {"documentation": "Documentation", "githubRepo": "GitHub Repo", "restApiReference": "REST API Reference"}, "states": {"queued": "Queued", "running": "Running", "success": "Success", "failed": "Failed", "skipped": "Skipped", "removed": "Removed", "scheduled": "Scheduled", "restarting": "Restarting", "up_for_retry": "Up For Retry", "up_for_reschedule": "Up For Reschedule", "upstream_failed": "Upstream Failed", "deferred": "Deferred", "no_status": "No Status"}, "dagRun_one": "<PERSON><PERSON>", "dagRun_other": "<PERSON>g <PERSON>s", "taskInstance_one": "Task Instance", "taskInstance_other": "Task Instances", "assetEvent_one": "Asset Event", "assetEvent_other": "Asset Events", "triggered": "Triggered", "pools": {"open": "Open", "running": "Running", "queued": "Queued", "scheduled": "Scheduled", "deferred": "Deferred", "pools_one": "pool", "pools_other": "pools"}}