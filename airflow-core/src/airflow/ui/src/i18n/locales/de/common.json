{"defaultToGraphView": "Graph-Ansicht als Standard", "defaultToGridView": "Gitter-Ansicht als Standard", "logout": "Abmelden", "switchToDarkMode": "Zum Dunkelmodus wechseln", "switchToLightMode": "Zum Hellmodus wechseln", "timezone": "Zeitzone", "user": "<PERSON><PERSON><PERSON>", "language": {"chinese": "Traditionelles Chinesisch", "english": "<PERSON><PERSON><PERSON>", "german": "De<PERSON>ch", "select": "Sprache wählen"}, "nav": {"home": "Start", "assets": "Datensets (Assets)", "browse": "B<PERSON>en", "admin": "Verwaltung", "docs": "Dokumentation", "plugins": "Plug-ins"}, "browse": {"auditLog": "Prüf-Log", "xcoms": "Task Kommunikation (XComs)"}, "admin": {"Variables": "Variablen", "Pools": "Pools", "Providers": "Providers", "Plugins": "Plug-ins", "Connections": "Verbindungen", "Config": "Konfiguration"}, "timeRange": {"duration": "Laufzeit", "lastHour": "Letzte Stunde", "last12Hours": "Letzte 12 Stunden", "last24Hours": "Letzte 24 Stunden", "pastWeek": "Letzte Woche"}, "docs": {"documentation": "Dokumentation", "githubRepo": "GitHub Ablage", "restApiReference": "REST API Referenz"}, "states": {"queued": "Wartend", "running": "<PERSON><PERSON><PERSON>", "success": "Erfolgreich", "failed": "Fehlgeschlagen", "skipped": "Übersprungen", "removed": "Entfernt", "scheduled": "<PERSON><PERSON><PERSON>", "restarting": "<PERSON><PERSON>", "up_for_retry": "Wartet auf neuen Versuch", "up_for_reschedule": "Wartet auf Neuplanung", "upstream_failed": "Vorgelagerte fehlgeschlagen", "deferred": "<PERSON><PERSON><PERSON><PERSON>", "no_status": "<PERSON><PERSON>"}, "dagRun_one": "<PERSON><PERSON>", "dagRun_other": "<PERSON><PERSON>", "taskInstance_one": "Task Instanz", "taskInstance_other": "Task Instanzen", "assetEvent_one": "<PERSON><PERSON><PERSON><PERSON> (Asset)", "assetEvent_other": "Ereignisse zu Datensets (Asset)", "triggered": "Angestoßen", "pools": {"open": "<PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON>", "queued": "Wartend", "scheduled": "<PERSON><PERSON><PERSON>", "deferred": "<PERSON><PERSON><PERSON><PERSON>", "pools_one": "Pool", "pools_other": "Pools"}}