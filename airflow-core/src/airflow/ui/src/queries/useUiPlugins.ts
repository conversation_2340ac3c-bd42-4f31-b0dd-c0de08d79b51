/*!
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export interface UiPlugin {
  slug: string;
  label: string;
  icon?: string;
  entry: string;
  type: "iframe" | "module";
  permissions: string[];
  plugin_name: string;
}

export interface UiPluginsResponse {
  plugins: UiPlugin[];
  total_entries: number;
}

export const useUiPlugins = () => {
  return useQuery<UiPluginsResponse>({
    queryKey: ["uiPlugins"],
    queryFn: async () => {
      const response = await axios.get("/api/v2/plugins/ui-plugins");
      return response.data;
    },
  });
};
