# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from __future__ import annotations

from typing import Any

from fastapi import Depends, status

from airflow.api_fastapi.common.router import AirflowRouter
from airflow.api_fastapi.core_api.datamodels.ui.config import ConfigResponse
from airflow.api_fastapi.core_api.openapi.exceptions import create_openapi_http_exception_doc
from airflow.api_fastapi.core_api.security import requires_authenticated
from airflow.configuration import conf
from airflow.settings import DASHBOARD_UIALERTS
from airflow.utils.log.log_reader import TaskLogReader

config_router = AirflowRouter(tags=["Config"])

WEBSERVER_CONFIG_KEYS = [
    "enable_swagger_ui",
]

API_CONFIG_KEYS = [
    "hide_paused_dags_by_default",
    "page_size",
    "default_wrap",
    "auto_refresh_interval",
    "require_confirmation_dag_change",
]


@config_router.get(
    "/config",
    responses=create_openapi_http_exception_doc([status.HTTP_404_NOT_FOUND]),
    dependencies=[Depends(requires_authenticated())],
)
def get_configs() -> ConfigResponse:
    """Get configs for UI."""
    conf_dict = conf.as_dict()

    # Add fallback values for webserver config keys
    webserver_config_defaults = {
        "enable_swagger_ui": True,
    }

    config = {}
    for key in WEBSERVER_CONFIG_KEYS:
        value = conf_dict.get("webserver", {}).get(key)
        if value is None:
            if key == "enable_swagger_ui":
                config[key] = conf.getboolean("webserver", key, fallback=webserver_config_defaults[key])
            else:
                config[key] = conf.get("webserver", key, fallback=webserver_config_defaults.get(key))
        else:
            config[key] = value

    # Add fallback values for API config keys to prevent None values
    api_config_defaults = {
        "hide_paused_dags_by_default": False,
        "page_size": 25,
        "default_wrap": False,
        "auto_refresh_interval": 3,
        "require_confirmation_dag_change": False,
    }

    for key in API_CONFIG_KEYS:
        value = conf_dict.get("api", {}).get(key)
        if value is None:
            # Use conf.get with fallback to ensure we get proper typed values
            if key == "hide_paused_dags_by_default":
                value = conf.getboolean("api", key, fallback=api_config_defaults[key])
            elif key in ["page_size", "auto_refresh_interval"]:
                value = conf.getint("api", key, fallback=api_config_defaults[key])
            elif key in ["default_wrap", "require_confirmation_dag_change"]:
                value = conf.getboolean("api", key, fallback=api_config_defaults[key])
            else:
                value = conf.get("api", key, fallback=api_config_defaults.get(key))
        config[key] = value

    task_log_reader = TaskLogReader()
    additional_config: dict[str, Any] = {
        "instance_name": conf.get("webserver", "instance_name", fallback="Airflow"),
        "audit_view_included_events": conf.get("webserver", "audit_view_included_events", fallback=""),
        "audit_view_excluded_events": conf.get("webserver", "audit_view_excluded_events", fallback=""),
        "test_connection": conf.get("core", "test_connection", fallback="Disabled"),
        "dashboard_alert": DASHBOARD_UIALERTS,
        "show_external_log_redirect": task_log_reader.supports_external_link,
        "external_log_name": getattr(task_log_reader.log_handler, "log_name", None),
    }

    config.update({key: value for key, value in additional_config.items()})

    return ConfigResponse.model_validate(config)
