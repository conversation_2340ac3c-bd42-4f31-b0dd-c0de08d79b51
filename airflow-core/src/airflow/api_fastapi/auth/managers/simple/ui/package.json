{"name": "simple-auth-manager-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 5174", "build": "vite build", "lint": "eslint --quiet && tsc --p tsconfig.app.json", "lint:fix": "eslint --fix && tsc --p tsconfig.app.json", "format": "pnpm prettier --write .", "preview": "vite preview", "codegen": "openapi-rq -i \"../openapi/v2-simple-auth-manager-generated.yaml\" -c axios --format prettier -o openapi-gen --operationId", "test": "vitest run", "coverage": "vitest run --coverage"}, "dependencies": {"@chakra-ui/react": "^3.19.1", "@tanstack/react-query": "^5.76.1", "axios": "^1.9.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.0"}, "devDependencies": {"@7nohe/openapi-react-query-codegen": "^1.6.2", "@eslint/compat": "^1.2.9", "@eslint/js": "^9.27.0", "@stylistic/eslint-plugin": "^4.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unicorn": "^59.0.1", "happy-dom": "^17.4.7", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5", "vite-plugin-css-injected-by-js": "^3.5.2", "vitest": "^3.1.4"}}