 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

Public API
==========

Airflow public API authentication
---------------------------------

The Airflow public API uses JWT (JSON Web Token) for authenticating API requests.
Each request made to the Airflow API must include a valid JWT token in the ``Authorization`` header to verify the
identity and permissions of the client.

Generate a JWT token
^^^^^^^^^^^^^^^^^^^^

To interact with the Airflow API, clients must first authenticate and obtain a JWT token.
The token can be generated by making a ``POST`` request to the ``/auth/token`` endpoint, passing the necessary
credentials (e.g., username and password). The ``/auth/token`` endpoint is provided by the auth manager, therefore,
please read the documentation of the auth manager configured in your environment for more details.

* :doc:`/core-concepts/auth-manager/simple/token`
* :doc:`apache-airflow-providers-fab:auth-manager/token`

Example
*******

Request

.. code-block:: bash

    ENDPOINT_URL="http://localhost:8080/"
    curl -X POST ${ENDPOINT_URL}/auth/token \
      -H "Content-Type: application/json" \
      -d '{
        "username": "your-username",
        "password": "your-password"
      }'

Response

.. code-block:: json

  {
    "access_token": "<JWT-TOKEN>"
  }

Use the JWT token to call Airflow public API

.. code-block:: bash

    ENDPOINT_URL="http://localhost:8080/"
    curl -X GET ${ENDPOINT_URL}/api/v2/dags \
      -H "Authorization: Bearer <JWT-TOKEN>"

Enabling CORS
-------------

`Cross-origin resource sharing (CORS) <https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS>`_
is a browser security feature that restricts HTTP requests that are initiated
from scripts running in the browser.

``Access-Control-Allow-Headers``, ``Access-Control-Allow-Methods``, and
``Access-Control-Allow-Origin`` headers can be added by setting values for
``access_control_allow_headers``, ``access_control_allow_methods``, and
``access_control_allow_origins`` options in the ``[api]`` section of the
``airflow.cfg`` file.

.. code-block:: ini

    [api]
    access_control_allow_headers = origin, content-type, accept
    access_control_allow_methods = POST, GET, OPTIONS, DELETE
    access_control_allow_origins = https://exampleclientapp1.com https://exampleclientapp2.com

Page size limit
---------------

To protect against requests that may lead to application instability, the stable API has a limit of items in response.
The default is 100 items, but you can change it using ``maximum_page_limit``  option in ``[api]``
section in the ``airflow.cfg`` file.
