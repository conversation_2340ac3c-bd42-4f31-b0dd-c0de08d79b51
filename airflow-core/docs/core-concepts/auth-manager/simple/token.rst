 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.

Generate JWT token with simple auth manager
===========================================

.. note::
    This guide only applies if your environment is configured with simple auth manager.

In order to use the :doc:`Airflow public API </stable-rest-api-ref>`, you need a JWT token for authentication.
You can then include this token in your Airflow public API requests.
To generate a JWT token, use the ``Create Token`` API in :doc:`sam-token-api-ref`.

Example
'''''''

.. code-block:: bash

    ENDPOINT_URL="http://localhost:8080/"
    curl -X 'POST' \
        "${ENDPOINT_URL}/auth/token" \
        -H 'Content-Type: application/json' \
        -d '{
        "username": "<username>",
        "password": "<password>"
        }'

This process will return a token that you can use in the Airflow public API requests.

If ``[core] simple_auth_manager_all_admins`` is set to True, you can also generate a token with no credentials.

.. code-block:: bash

    ENDPOINT_URL="http://localhost:8080/"
    curl -X 'GET' "${ENDPOINT_URL}/auth/token"
