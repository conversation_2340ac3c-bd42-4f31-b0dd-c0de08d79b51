# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Administration and deployment security -> security

administration-and-deployment/security/index.rst security/index.rst
administration-and-deployment/security/kerberos.rst security/kerberos.rst

# Moving FAB auth manager related docs to providers
## The ``../`` indicates that it will move to the root of the docs directory, unlike the rest of
## the entries here which move to docs/apache-airflow.
## It's okay to include ``/stable/``, because there's no relationship between a version of
## Airflow and the version of any provider package.

administration-and-deployment/security/access-control.rst ../apache-airflow-providers-fab/stable/auth-manager/access-control.rst
administration-and-deployment/security/access-control/index.rst ../apache-airflow-providers-fab/stable/auth-manager/access-control.rst
administration-and-deployment/security/api.rst security/api.rst
administration-and-deployment/security/audit_logs.rst security/audit_logs.rst
administration-and-deployment/security/flower.rst security/flower.rst
administration-and-deployment/security/webserver.rst ../apache-airflow-providers-fab/stable/auth-manager/security.rst
administration-and-deployment/security/workload.rst security/workload.rst
administration-and-deployment/security/secrets/secrets-backends/index.rst security/secrets/secrets-backends/index.rst
administration-and-deployment/security/secrets/secrets-backends/local-filesystem-secrets-backend.rst security/secrets/secrets-backends/local-filesystem-secrets-backend.rst
administration-and-deployment/security/secrets/fernet.rst security/secrets/fernet.rst
administration-and-deployment/security/secrets/index.rst security/secrets/index.rst
administration-and-deployment/security/secrets/mask-sensitive-values.rst security/secrets/mask-sensitive-values.rst

# Security
howto/use-alternative-secrets-backend.rst security/secrets/secrets-backend/index.rst
security.rst security/index.rst

# Move the documentation from core to FAB provider
security/access-control.rst ../apache-airflow-providers-fab/stable/auth-manager/access-control.rst

# Operators guides
howto/operator/external.rst howto/operator/external_task_sensor.rst
howto/secure-connections.rst howto/connection.rst
howto/connection/index.rst howto/connection.rst
howto/customize-dag-ui-page-instance-name.rst howto/customize-ui.rst#customizing-dag-ui-header-and-airflow-page-titles
howto/customize-state-colors-ui.rst howto/customize-ui.rst#customizing-state-colours

# Web UI
howto/add-new-role.rst ../apache-airflow-providers-fab/stable/auth-manager/access-control.rst

# Set up a database
howto/initialize-database.rst howto/set-up-database.rst

# Logging & Monitoring
howto/check-health.rst administration-and-deployment/logging-monitoring/check-health.rst
errors.rst administration-and-deployment/logging-monitoring/errors.rst
howto/write-logs.rst administration-and-deployment/logging-monitoring/logging-tasks.rst
metrics.rst administration-and-deployment/logging-monitoring/metrics.rst
traces.rst administration-and-deployment/logging-monitoring/traces.rst
howto/tracking-user-activity.rst administration-and-deployment/logging-monitoring/tracking-user-activity.rst

# Quick start
start/docker.rst howto/docker-compose/index.rst
start/local.rst start.rst
start/index.rst start.rst

# References
cli-ref.rst cli-and-env-variables-ref.rst
_api/index.rst public-airflow-interface.rst
deprecated-rest-api-ref.rst rest-api-ref.rst
macros-ref.rst templates-ref.rst

# Concepts
concepts.rst core-concepts/index.rst
scheduler.rst administration-and-deployment/scheduler.rst

# Installation
installation.rst installation/index.rst
upgrade-check.rst installation/upgrade-check.rst

# Release Notes
changelog.rst release_notes.rst

# Tutorials
tutorial.rst tutorial/index.rst
tutorial_taskflow_api.rst tutorial/taskflow.rst

## Docs Structure Refactor
# indexes
logging-monitoring/index.rst administration-and-deployment/logging-monitoring/index.rst
concepts/index.rst core-concepts/index.rst
executor/index.rst core-concepts/executor/index.rst
upgrading-from-1-10/index.rst howto/upgrading-from-1-10/index.rst
core-concepts/auth-manager.rst core-concepts/auth-manager/index.rst

listeners.rst administration-and-deployment/listeners.rst
kubernetes.rst administration-and-deployment/kubernetes.rst
executor/kubernetes.rst core-concepts/executor/kubernetes.rst
plugins.rst administration-and-deployment/plugins.rst
authoring-and-scheduling/plugins.rst administration-and-deployment/plugins.rst
modules_management.rst administration-and-deployment/modules_management.rst
dag-serialization.rst administration-and-deployment/dag-serialization.rst
timezone.rst authoring-and-scheduling/timezone.rst
production-deployment.rst administration-and-deployment/production-deployment.rst
usage-cli.rst howto/usage-cli.rst
lineage.rst administration-and-deployment/lineage.rst
dag-run.rst core-concepts/dag-run.rst
concepts/taskflow.rst core-concepts/taskflow.rst
logging-monitoring/tracking-user-activity.rst administration-and-deployment/logging-monitoring/tracking-user-activity.rst
logging-monitoring/errors.rst administration-and-deployment/logging-monitoring/errors.rst
logging-monitoring/logging-architecture.rst administration-and-deployment/logging-monitoring/logging-architecture.rst
logging-monitoring/callbacks.rst administration-and-deployment/logging-monitoring/callbacks.rst
logging-monitoring/logging-tasks.rst administration-and-deployment/logging-monitoring/logging-tasks.rst
logging-monitoring/check-health.rst administration-and-deployment/logging-monitoring/check-health.rst
logging-monitoring/metrics.rst administration-and-deployment/logging-monitoring/metrics.rst
logging-monitoring/traces.rst administration-and-deployment/logging-monitoring/traces.rst
concepts/params.rst core-concepts/params.rst
concepts/dynamic-task-mapping.rst authoring-and-scheduling/dynamic-task-mapping.rst
concepts/sensors.rst core-concepts/sensors.rst
concepts/variables.rst core-concepts/variables.rst
concepts/overview.rst core-concepts/overview.rst
concepts/connections.rst authoring-and-scheduling/connections.rst
concepts/tasks.rst core-concepts/tasks.rst
concepts/timetable.rst authoring-and-scheduling/timetable.rst
concepts/operators.rst core-concepts/operators.rst
concepts/xcoms.rst core-concepts/xcoms.rst
concepts/scheduler.rst administration-and-deployment/scheduler.rst
concepts/pools.rst administration-and-deployment/pools.rst
concepts/priority-weight.rst administration-and-deployment/priority-weight.rst
concepts/deferring.rst authoring-and-scheduling/deferring.rst
concepts/datasets.rst authoring-and-scheduling/asset-scheduling.rst
concepts/cluster-policies.rst administration-and-deployment/cluster-policies.rst
concepts/dags.rst core-concepts/dags.rst
executor/local_kubernetes.rst core-concepts/executor/local_kubernetes.rst
executor/celery_kubernetes.rst core-concepts/executor/celery_kubernetes.rst
executor/dask.rst core-concepts/executor/dask.rst
executor/debug.rst core-concepts/executor/debug.rst
executor/celery.rst core-concepts/executor/celery.rst
executor/local.rst core-concepts/executor/local.rst
upgrading-from-1-10/upgrade-check.rst howto/upgrading-from-1-10/upgrade-check.rst
core-concepts/dag-run.rst authoring-and-scheduling/cron.rst
core-concepts/executor/debug.rst core-concepts/debug.rst
concepts/dagfile-processing.rst administration-and-deployment/dagfile-processing.rst
authoring-and-scheduling/dagfile-processing.rst administration-and-deployment/dagfile-processing.rst
authoring-and-scheduling/datasets.rst authoring-and-scheduling/asset-scheduling.rst

# Moving provider executor docs to providers
## The ``../`` indicates that it will move to the root of the docs directory, unlike the rest of
## the entries here which move to docs/apache-airflow.
## It's okay to include ``/stable/``, because there's no relationship between a version of
## Airflow and the version of any provider package.
### Kubernetes Executors
core-concepts/executor/kubernetes.rst ../apache-airflow-providers-cncf-kubernetes/stable/kubernetes_executor.html
core-concepts/executor/local_kubernetes.rst ../apache-airflow-providers-cncf-kubernetes/stable/local_kubernetes_executor.html

### Celery Executors
core-concepts/executor/celery_kubernetes.rst ../apache-airflow-providers-celery/stable/celery_kubernetes_executor.html
core-concepts/executor/celery.rst ../apache-airflow-providers-celery/stable/celery_executor.html

# Python API
python-api-ref.rst public-airflow-interface.rst

# Typos
howto/define_extra_link.rst howto/define-extra-link.rst

# Use test config (it's not a howto for users but a howto for developers so we redirect it back to index)
howto/use-test-config.rst index.rst

# Operators/Sensors moved to standard providers

howto/operator/bash.rst ../apache-airflow-providers-standard/stable/operators/bash.rst
howto/operator/datetime.rst ../apache-airflow-providers-standard/stable/operators/datetime.rst
howto/operator/external_task_sensor.rst ../apache-airflow-providers-standard/stable/sensors/external_task_sensor.rst
howto/operator/file.rst ../apache-airflow-providers-standard/stable/sensors/file.rst
howto/operator/python.rst ../apache-airflow-providers-standard/stable/operators/python.rst
howto/operator/time.rst ../apache-airflow-providers-standard/stable/sensors/datetime.rst
howto/operator/weekday.rst ../apache-airflow-providers-standard/stable/operators/datetime.rst#branchdayofweekoperator
