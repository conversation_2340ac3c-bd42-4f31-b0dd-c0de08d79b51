{{/*
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
*/}}

################################
## Airflow Fernet Key Secret
#################################
{{- if not .Values.fernetKeySecretName }}
{{- $generated_fernet_key := (randAlphaNum 32 | b64enc) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-fernet-key
  labels:
    tier: airflow
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service }}
    {{- with .Values.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    "helm.sh/hook": "pre-install"
    "helm.sh/hook-delete-policy": "before-hook-creation"
    "helm.sh/hook-weight": "0"
    {{- with .Values.fernetKeySecretAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
type: Opaque
data:
  fernet-key: {{ (default $generated_fernet_key .Values.fernetKey) | b64enc | quote }}
{{- end }}
