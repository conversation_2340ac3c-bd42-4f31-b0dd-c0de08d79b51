#!/usr/bin/env bash
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
set -euo pipefail

MY_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
AIRFLOW_SOURCES=$(cd "${MY_DIR}/../.." && pwd)

BREEZE_BINARY=breeze
COLOR_RED=$'\e[31m'
COLOR_GREEN=$'\e[32m'
COLOR_YELLOW=$'\e[33m'
COLOR_BLUE=$'\e[34m'
COLOR_RESET=$'\e[0m'

UV_VERSION="0.7.7"

function manual_instructions() {
    echo
    echo "${COLOR_BLUE}Please run those commands manually (you might need to restart shell between them)${COLOR_RESET}"
    echo
    echo "    python -m pip install \"uv==${UV_VERSION}\""
    echo "    uv tool install -e '${AIRFLOW_SOURCES}/dev/breeze/'"
    echo "    breeze setup autocomplete --force"
    echo
    echo "   After that, both pipx and breeze should be available on your path"
    echo
    exit 1
}

function check_breeze_installed() {
    set +e
    command -v "${BREEZE_BINARY}" >/dev/null 2>/dev/null
    local breeze_on_path=$?
    command -v "uv" >/dev/null 2>/dev/null
    local uv_on_path=$?
    set -e
    if [[ ${breeze_on_path} != "0" || ${uv_on_path} != "0"  ]]; then
        echo
        if [[ ${uv_on_path} != 0 ]]; then
            echo "${COLOR_RED}The 'uv' is not on path. It should be installed and 'uv' should be available on your PATH.${COLOR_RESET}"
            export TIMEOUT=0
            if "${MY_DIR}/confirm" "Installing uv?"; then
                python -m pip install "uv==${UV_VERSION}" --upgrade
                echo
                echo "${COLOR_YELLOW}Please close and re-open the shell and retry. You might need to add 'uv' to the PATH!${COLOR_RESET}"
                echo
                exit
            else
                manual_instructions
            fi
        fi
        if [[ ${breeze_on_path} != 0 ]]; then
            echo "${COLOR_RED}The '${BREEZE_BINARY}' is not on path. Breeze should be installed and 'breeze' should be available on your PATH!${COLOR_RESET}"
            export TIMEOUT=0
            echo
            echo "${COLOR_YELLOW}Installing Breeze. This will install breeze via uv and modify your ${SHELL} to run it${COLOR_RESET}"
            echo
            if "${MY_DIR}/confirm" "installing and modifying the startup scripts"; then
                uv tool install -e "${AIRFLOW_SOURCES}/dev/breeze/" --force
                ${BREEZE_BINARY} setup-autocomplete --force --answer yes
                echo
                echo "${COLOR_YELLOW}Please close and re-open the shell and retry. Then rerun your last command!${COLOR_RESET}"
                echo
                exit
            else
                manual_instructions
            fi
        fi
    fi
}

check_breeze_installed

echo
echo "${COLOR_GREEN}Breeze is correctly installed! You can run it via 'breeze' command. Go ahead and develop Airflow.${COLOR_RESET}"
echo
