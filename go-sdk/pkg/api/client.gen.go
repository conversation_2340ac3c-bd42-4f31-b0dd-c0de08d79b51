// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/ashb/oapi-resty-codegen version v0.0.0-20250508101518-564f8c686e08 DO NOT EDIT.
// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"resty.dev/v3"
)

const (
	JWTBearerScopes = "JWTBearer.Scopes"
)

// Defines values for DagRunState.
const (
	DagRunStateFailed  DagRunState = "failed"
	DagRunStateQueued  DagRunState = "queued"
	DagRunStateRunning DagRunState = "running"
	DagRunStateSuccess DagRunState = "success"
)

// Defines values for DagRunType.
const (
	AssetTriggered DagRunType = "asset_triggered"
	Backfill       DagRunType = "backfill"
	Manual         DagRunType = "manual"
	Scheduled      DagRunType = "scheduled"
)

// Defines values for IntermediateTIState.
const (
	IntermediateTIStateDeferred        IntermediateTIState = "deferred"
	IntermediateTIStateQueued          IntermediateTIState = "queued"
	IntermediateTIStateRestarting      IntermediateTIState = "restarting"
	IntermediateTIStateScheduled       IntermediateTIState = "scheduled"
	IntermediateTIStateUpForReschedule IntermediateTIState = "up_for_reschedule"
	IntermediateTIStateUpForRetry      IntermediateTIState = "up_for_retry"
	IntermediateTIStateUpstreamFailed  IntermediateTIState = "upstream_failed"
)

// Defines values for TIDeferredStatePayloadState.
const (
	Deferred TIDeferredStatePayloadState = "deferred"
)

// Defines values for TIEnterRunningPayloadState.
const (
	Running TIEnterRunningPayloadState = "running"
)

// Defines values for TIRescheduleStatePayloadState.
const (
	UpForReschedule TIRescheduleStatePayloadState = "up_for_reschedule"
)

// Defines values for TIRetryStatePayloadState.
const (
	UpForRetry TIRetryStatePayloadState = "up_for_retry"
)

// Defines values for TISuccessStatePayloadState.
const (
	TISuccessStatePayloadStateSuccess TISuccessStatePayloadState = "success"
)

// Defines values for TerminalStateNonSuccess.
const (
	Failed  TerminalStateNonSuccess = "failed"
	Removed TerminalStateNonSuccess = "removed"
	Skipped TerminalStateNonSuccess = "skipped"
)

// Defines values for TerminalTIState.
const (
	TerminalTIStateFailed  TerminalTIState = "failed"
	TerminalTIStateRemoved TerminalTIState = "removed"
	TerminalTIStateSkipped TerminalTIState = "skipped"
	TerminalTIStateSuccess TerminalTIState = "success"
)

// AssetAliasReferenceAssetEventDagRun Schema for AssetAliasModel used in AssetEventDagRunReference.
type AssetAliasReferenceAssetEventDagRun struct {
	Name string `json:"name"`
}

// AssetEventDagRunReference Schema for AssetEvent model used in DagRun.
type AssetEventDagRunReference struct {
	// Asset Schema for AssetModel used in AssetEventDagRunReference.
	Asset          AssetReferenceAssetEventDagRun        `json:"asset"`
	Extra          map[string]interface{}                `json:"extra"`
	SourceAliases  []AssetAliasReferenceAssetEventDagRun `json:"source_aliases"`
	SourceDagId    *string                               `json:"source_dag_id"`
	SourceMapIndex *int                                  `json:"source_map_index"`
	SourceRunId    *string                               `json:"source_run_id"`
	SourceTaskId   *string                               `json:"source_task_id"`
	Timestamp      time.Time                             `json:"timestamp"`
}

// AssetEventResponse Asset event schema with fields that are needed for Runtime.
type AssetEventResponse struct {
	// Asset Asset schema for responses with fields that are needed for Runtime.
	Asset          AssetResponse           `json:"asset"`
	CreatedDagruns []DagRunAssetReference  `json:"created_dagruns"`
	Extra          *map[string]interface{} `json:"extra"`
	Id             int                     `json:"id"`
	SourceDagId    *string                 `json:"source_dag_id"`
	SourceMapIndex *int                    `json:"source_map_index,omitempty"`
	SourceRunId    *string                 `json:"source_run_id"`
	SourceTaskId   *string                 `json:"source_task_id"`
	Timestamp      time.Time               `json:"timestamp"`
}

// AssetEventsResponse Collection of AssetEventResponse.
type AssetEventsResponse struct {
	AssetEvents []AssetEventResponse `json:"asset_events"`
}

// AssetProfile Profile of an asset-like object.
//
// Asset will have name, uri defined, with type set to 'Asset'.
// AssetNameRef will have name defined, type set to 'AssetNameRef'.
// AssetUriRef will have uri defined, type set to 'AssetUriRef'.
// AssetAlias will have name defined, type set to 'AssetAlias'.
//
// Note that 'type' here is distinct from 'asset_type' the user declares on an
// Asset (or subclass). This field is for distinguishing between different
// asset-related types (Asset, AssetRef, or AssetAlias).
type AssetProfile struct {
	Name *string `json:"name"`
	Type string  `json:"type"`
	Uri  *string `json:"uri"`
}

// AssetReferenceAssetEventDagRun Schema for AssetModel used in AssetEventDagRunReference.
type AssetReferenceAssetEventDagRun struct {
	Extra map[string]interface{} `json:"extra"`
	Name  string                 `json:"name"`
	Uri   string                 `json:"uri"`
}

// AssetResponse Asset schema for responses with fields that are needed for Runtime.
type AssetResponse struct {
	Extra *map[string]interface{} `json:"extra"`
	Group string                  `json:"group"`
	Name  string                  `json:"name"`
	Uri   string                  `json:"uri"`
}

// BundleInfo Schema for telling task which bundle to run with.
type BundleInfo struct {
	Name    string  `json:"name"`
	Version *string `json:"version"`
}

// ConnectionResponse Connection schema for responses with fields that are needed for Runtime.
type ConnectionResponse struct {
	ConnId   string  `json:"conn_id"`
	ConnType string  `json:"conn_type"`
	Extra    *string `json:"extra"`
	Host     *string `json:"host"`
	Login    *string `json:"login"`
	Password *string `json:"password"`
	Port     *int    `json:"port"`
	Schema   *string `json:"schema"`
}

// DagRun Schema for DagRun model with minimal required fields needed for Runtime.
type DagRun struct {
	ClearNumber         *int                        `json:"clear_number,omitempty"`
	Conf                *map[string]interface{}     `json:"conf,omitempty"`
	ConsumedAssetEvents []AssetEventDagRunReference `json:"consumed_asset_events"`
	DagId               string                      `json:"dag_id"`
	DataIntervalEnd     *time.Time                  `json:"data_interval_end"`
	DataIntervalStart   *time.Time                  `json:"data_interval_start"`
	EndDate             *time.Time                  `json:"end_date"`
	LogicalDate         *time.Time                  `json:"logical_date"`
	RunAfter            time.Time                   `json:"run_after"`
	RunId               string                      `json:"run_id"`

	// RunType Class with DagRun types.
	RunType   DagRunType `json:"run_type"`
	StartDate time.Time  `json:"start_date"`
}

// DagRunAssetReference DagRun serializer for asset responses.
type DagRunAssetReference struct {
	DagId             string     `json:"dag_id"`
	DataIntervalEnd   *time.Time `json:"data_interval_end"`
	DataIntervalStart *time.Time `json:"data_interval_start"`
	EndDate           *time.Time `json:"end_date"`
	LogicalDate       *time.Time `json:"logical_date"`
	RunId             string     `json:"run_id"`
	StartDate         time.Time  `json:"start_date"`
	State             string     `json:"state"`
}

// DagRunState All possible states that a DagRun can be in.
//
// These are "shared" with TaskInstanceState in some parts of the code,
// so please ensure that their values always match the ones with the
// same name in TaskInstanceState.
type DagRunState string

// DagRunStateResponse Schema for DAG Run State response.
type DagRunStateResponse struct {
	// State All possible states that a DagRun can be in.
	//
	// These are "shared" with TaskInstanceState in some parts of the code,
	// so please ensure that their values always match the ones with the
	// same name in TaskInstanceState.
	State DagRunState `json:"state"`
}

// DagRunType Class with DagRun types.
type DagRunType string

// HTTPValidationError defines model for HTTPValidationError.
type HTTPValidationError struct {
	Detail *[]ValidationError `json:"detail,omitempty"`
}

// IntermediateTIState States that a Task Instance can be in that indicate it is not yet in a terminal or running state.
type IntermediateTIState string

// JsonValue defines model for JsonValue.
type JsonValue struct {
	union json.RawMessage
}

// JsonValue0 defines model for .
type JsonValue0 = string

// JsonValue1 defines model for .
type JsonValue1 = float32

// JsonValue2 defines model for .
type JsonValue2 = int

// JsonValue3 defines model for .
type JsonValue3 = map[string]interface{}

// JsonValue4 defines model for .
type JsonValue4 = []interface{}

// JsonValue5 defines model for .
type JsonValue5 = bool

// PrevSuccessfulDagRunResponse Schema for response with previous successful DagRun information for Task Template Context.
type PrevSuccessfulDagRunResponse struct {
	DataIntervalEnd   *time.Time `json:"data_interval_end"`
	DataIntervalStart *time.Time `json:"data_interval_start"`
	EndDate           *time.Time `json:"end_date"`
	StartDate         *time.Time `json:"start_date"`
}

// TIDeferredStatePayload Schema for updating TaskInstance to a deferred state.
type TIDeferredStatePayload struct {
	Classpath      string                                `json:"classpath"`
	NextKwargs     *TIDeferredStatePayload_NextKwargs    `json:"next_kwargs,omitempty"`
	NextMethod     string                                `json:"next_method"`
	State          TIDeferredStatePayloadState           `json:"state"`
	TriggerKwargs  *TIDeferredStatePayload_TriggerKwargs `json:"trigger_kwargs,omitempty"`
	TriggerTimeout *string                               `json:"trigger_timeout"`
}

// TIDeferredStatePayloadNextKwargs0 defines model for .
type TIDeferredStatePayloadNextKwargs0 map[string]interface{}

// TIDeferredStatePayloadNextKwargs1 defines model for .
type TIDeferredStatePayloadNextKwargs1 = string

// TIDeferredStatePayload_NextKwargs defines model for TIDeferredStatePayload.NextKwargs.
type TIDeferredStatePayload_NextKwargs struct {
	union json.RawMessage
}

// TIDeferredStatePayloadState defines model for TIDeferredStatePayload.State.
type TIDeferredStatePayloadState string

// TIDeferredStatePayloadTriggerKwargs0 defines model for .
type TIDeferredStatePayloadTriggerKwargs0 map[string]interface{}

// TIDeferredStatePayloadTriggerKwargs1 defines model for .
type TIDeferredStatePayloadTriggerKwargs1 = string

// TIDeferredStatePayload_TriggerKwargs defines model for TIDeferredStatePayload.TriggerKwargs.
type TIDeferredStatePayload_TriggerKwargs struct {
	union json.RawMessage
}

// TIEnterRunningPayload Schema for updating TaskInstance to 'RUNNING' state with minimal required fields.
type TIEnterRunningPayload struct {
	Hostname  string                     `json:"hostname"`
	Pid       int                        `json:"pid"`
	StartDate time.Time                  `json:"start_date"`
	State     TIEnterRunningPayloadState `json:"state"`
	Unixname  string                     `json:"unixname"`
}

// TIEnterRunningPayloadState defines model for TIEnterRunningPayload.State.
type TIEnterRunningPayloadState string

// TIHeartbeatInfo Schema for TaskInstance heartbeat endpoint.
type TIHeartbeatInfo struct {
	Hostname string `json:"hostname"`
	Pid      int    `json:"pid"`
}

// TIRescheduleStatePayload Schema for updating TaskInstance to a up_for_reschedule state.
type TIRescheduleStatePayload struct {
	EndDate        time.Time                     `json:"end_date"`
	RescheduleDate time.Time                     `json:"reschedule_date"`
	State          TIRescheduleStatePayloadState `json:"state"`
}

// TIRescheduleStatePayloadState defines model for TIRescheduleStatePayload.State.
type TIRescheduleStatePayloadState string

// TIRetryStatePayload Schema for updating TaskInstance to up_for_retry.
type TIRetryStatePayload struct {
	EndDate time.Time                `json:"end_date"`
	State   TIRetryStatePayloadState `json:"state"`
}

// TIRetryStatePayloadState defines model for TIRetryStatePayload.State.
type TIRetryStatePayloadState string

// TIRunContext Response schema for TaskInstance run context.
type TIRunContext struct {
	Connections *[]ConnectionResponse `json:"connections,omitempty"`

	// DagRun Schema for DagRun model with minimal required fields needed for Runtime.
	DagRun              DagRun                   `json:"dag_run"`
	MaxTries            int                      `json:"max_tries"`
	NextKwargs          *TIRunContext_NextKwargs `json:"next_kwargs"`
	NextMethod          *string                  `json:"next_method"`
	ShouldRetry         bool                     `json:"should_retry"`
	TaskRescheduleCount *int                     `json:"task_reschedule_count,omitempty"`
	UpstreamMapIndexes  *map[string]int          `json:"upstream_map_indexes"`
	Variables           *[]VariableResponse      `json:"variables,omitempty"`
	XcomKeysToClear     *[]string                `json:"xcom_keys_to_clear,omitempty"`
}

// TIRunContextNextKwargs0 defines model for .
type TIRunContextNextKwargs0 map[string]interface{}

// TIRunContextNextKwargs1 defines model for .
type TIRunContextNextKwargs1 = string

// TIRunContext_NextKwargs defines model for TIRunContext.NextKwargs.
type TIRunContext_NextKwargs struct {
	union json.RawMessage
}

// TISkippedDownstreamTasksStatePayload Schema for updating downstream tasks to a skipped state.
type TISkippedDownstreamTasksStatePayload struct {
	Tasks []TISkippedDownstreamTasksStatePayload_Tasks_Item `json:"tasks"`
}

// TISkippedDownstreamTasksStatePayloadTasks0 defines model for .
type TISkippedDownstreamTasksStatePayloadTasks0 = string

// TISkippedDownstreamTasksStatePayloadTasks1 defines model for .
type TISkippedDownstreamTasksStatePayloadTasks1 = []interface{}

// TISkippedDownstreamTasksStatePayload_Tasks_Item defines model for TISkippedDownstreamTasksStatePayload.tasks.Item.
type TISkippedDownstreamTasksStatePayload_Tasks_Item struct {
	union json.RawMessage
}

// TISuccessStatePayload Schema for updating TaskInstance to success state.
type TISuccessStatePayload struct {
	EndDate      time.Time                  `json:"end_date"`
	OutletEvents *[]map[string]interface{}  `json:"outlet_events,omitempty"`
	State        TISuccessStatePayloadState `json:"state"`
	TaskOutlets  *[]AssetProfile            `json:"task_outlets,omitempty"`
}

// TISuccessStatePayloadState defines model for TISuccessStatePayload.State.
type TISuccessStatePayloadState string

// TITargetStatePayload Schema for updating TaskInstance to a target state, excluding terminal and running states.
type TITargetStatePayload struct {
	// State States that a Task Instance can be in that indicate it is not yet in a terminal or running state.
	State IntermediateTIState `json:"state"`
}

// TITerminalStatePayload Schema for updating TaskInstance to a terminal state except SUCCESS state.
type TITerminalStatePayload struct {
	EndDate time.Time `json:"end_date"`

	// State TaskInstance states that can be reported without extra information.
	State TerminalStateNonSuccess `json:"state"`
}

// TIUpdateStatePayload defines model for TIUpdateStatePayload.
type TIUpdateStatePayload struct {
	union json.RawMessage
}

// TaskInstance Schema for TaskInstance model with minimal required fields needed for Runtime.
type TaskInstance struct {
	ContextCarrier *map[string]interface{} `json:"context_carrier"`
	DagId          string                  `json:"dag_id"`
	Hostname       *string                 `json:"hostname"`
	Id             openapi_types.UUID      `json:"id"`
	MapIndex       *int                    `json:"map_index,omitempty"`
	RunId          string                  `json:"run_id"`
	TaskId         string                  `json:"task_id"`
	TryNumber      int                     `json:"try_number"`
}

// TaskStatesResponse Response for task states with run_id, task and state.
type TaskStatesResponse struct {
	TaskStates map[string]interface{} `json:"task_states"`
}

// TerminalStateNonSuccess TaskInstance states that can be reported without extra information.
type TerminalStateNonSuccess string

// TerminalTIState defines model for TerminalTIState.
type TerminalTIState string

// TriggerDAGRunPayload Schema for Trigger DAG Run API request.
type TriggerDAGRunPayload struct {
	Conf        *map[string]interface{} `json:"conf,omitempty"`
	LogicalDate *time.Time              `json:"logical_date"`
	ResetDagRun *bool                   `json:"reset_dag_run,omitempty"`
}

// ValidationError defines model for ValidationError.
type ValidationError struct {
	Loc  []ValidationError_Loc_Item `json:"loc"`
	Msg  string                     `json:"msg"`
	Type string                     `json:"type"`
}

// ValidationErrorLoc0 defines model for .
type ValidationErrorLoc0 = string

// ValidationErrorLoc1 defines model for .
type ValidationErrorLoc1 = int

// ValidationError_Loc_Item defines model for ValidationError.loc.Item.
type ValidationError_Loc_Item struct {
	union json.RawMessage
}

// VariablePostBody Request body schema for creating variables.
type VariablePostBody struct {
	Description *string `json:"description"`
	Val         *string `json:"val"`
}

// VariableResponse Variable schema for responses with fields that are needed for Runtime.
type VariableResponse struct {
	Key   string  `json:"key"`
	Value *string `json:"value"`
}

// XComResponse XCom schema for responses with fields that are needed for Runtime.
type XComResponse struct {
	Key   string     `json:"key"`
	Value *JsonValue `json:"value"`
}

// GetAssetEventByAssetNameUriParams defines parameters for GetAssetEventByAssetNameUri.
type GetAssetEventByAssetNameUriParams struct {
	// Name The name of the Asset
	Name string `form:"name" json:"name"`

	// Uri The URI of the Asset
	Uri string `form:"uri" json:"uri"`
}

// GetAssetEventByAssetAliasParams defines parameters for GetAssetEventByAssetAlias.
type GetAssetEventByAssetAliasParams struct {
	// Name The name of the Asset Alias
	Name string `form:"name" json:"name"`
}

// GetAssetByNameParams defines parameters for GetAssetByName.
type GetAssetByNameParams struct {
	// Name The name of the Asset
	Name string `form:"name" json:"name"`
}

// GetAssetByUriParams defines parameters for GetAssetByUri.
type GetAssetByUriParams struct {
	// Uri The URI of the Asset
	Uri string `form:"uri" json:"uri"`
}

// GetDrCountParams defines parameters for GetDrCount.
type GetDrCountParams struct {
	DagId        string       `form:"dag_id" json:"dag_id"`
	LogicalDates *[]time.Time `form:"logical_dates,omitempty" json:"logical_dates,omitempty"`
	RunIds       *[]string    `form:"run_ids,omitempty" json:"run_ids,omitempty"`
	States       *[]string    `form:"states,omitempty" json:"states,omitempty"`
}

// GetTaskInstanceCountParams defines parameters for GetTaskInstanceCount.
type GetTaskInstanceCountParams struct {
	DagId        string       `form:"dag_id" json:"dag_id"`
	TaskIds      *[]string    `form:"task_ids,omitempty" json:"task_ids,omitempty"`
	TaskGroupId  *string      `form:"task_group_id,omitempty" json:"task_group_id,omitempty"`
	LogicalDates *[]time.Time `form:"logical_dates,omitempty" json:"logical_dates,omitempty"`
	RunIds       *[]string    `form:"run_ids,omitempty" json:"run_ids,omitempty"`
	States       *[]string    `form:"states,omitempty" json:"states,omitempty"`
}

// GetTaskInstanceStatesParams defines parameters for GetTaskInstanceStates.
type GetTaskInstanceStatesParams struct {
	DagId        string       `form:"dag_id" json:"dag_id"`
	TaskIds      *[]string    `form:"task_ids,omitempty" json:"task_ids,omitempty"`
	TaskGroupId  *string      `form:"task_group_id,omitempty" json:"task_group_id,omitempty"`
	LogicalDates *[]time.Time `form:"logical_dates,omitempty" json:"logical_dates,omitempty"`
	RunIds       *[]string    `form:"run_ids,omitempty" json:"run_ids,omitempty"`
}

// TaskInstancePutRenderedFieldsJSONBody defines parameters for TaskInstancePutRenderedFields.
type TaskInstancePutRenderedFieldsJSONBody map[string]*JsonValue

// TaskInstanceUpdateStateJSONBody defines parameters for TaskInstanceUpdateState.
type TaskInstanceUpdateStateJSONBody = TIUpdateStatePayload

// DeleteXcomParams defines parameters for DeleteXcom.
type DeleteXcomParams struct {
	MapIndex *int `form:"map_index,omitempty" json:"map_index,omitempty"`
}

// GetXcomParams defines parameters for GetXcom.
type GetXcomParams struct {
	MapIndex          *int  `form:"map_index,omitempty" json:"map_index,omitempty"`
	IncludePriorDates *bool `form:"include_prior_dates,omitempty" json:"include_prior_dates,omitempty"`
}

// HeadXcomParams defines parameters for HeadXcom.
type HeadXcomParams struct {
	MapIndex *int `form:"map_index,omitempty" json:"map_index,omitempty"`
}

// SetXcomParams defines parameters for SetXcom.
type SetXcomParams struct {
	MapIndex *int `form:"map_index,omitempty" json:"map_index,omitempty"`

	// MappedLength Number of mapped tasks this value expands into
	MappedLength *int `form:"mapped_length,omitempty" json:"mapped_length,omitempty"`
}

// TriggerDagRunJSONRequestBody defines body for TriggerDagRun for application/json ContentType.
type TriggerDagRunJSONRequestBody = TriggerDAGRunPayload

// TaskInstanceHeartbeatJSONRequestBody defines body for TaskInstanceHeartbeat for application/json ContentType.
type TaskInstanceHeartbeatJSONRequestBody = TIHeartbeatInfo

// TaskInstancePutRenderedFieldsJSONRequestBody defines body for TaskInstancePutRenderedFields for application/json ContentType.
type TaskInstancePutRenderedFieldsJSONRequestBody TaskInstancePutRenderedFieldsJSONBody

// TaskInstanceRunJSONRequestBody defines body for TaskInstanceRun for application/json ContentType.
type TaskInstanceRunJSONRequestBody = TIEnterRunningPayload

// TaskInstanceSkipDownstreamJSONRequestBody defines body for TaskInstanceSkipDownstream for application/json ContentType.
type TaskInstanceSkipDownstreamJSONRequestBody = TISkippedDownstreamTasksStatePayload

// TaskInstanceUpdateStateJSONRequestBody defines body for TaskInstanceUpdateState for application/json ContentType.
type TaskInstanceUpdateStateJSONRequestBody = TaskInstanceUpdateStateJSONBody

// PutVariableJSONRequestBody defines body for PutVariable for application/json ContentType.
type PutVariableJSONRequestBody = VariablePostBody

// SetXcomJSONRequestBody defines body for SetXcom for application/json ContentType.
type SetXcomJSONRequestBody = JsonValue

// AsJsonValue0 returns the union data inside the JsonValue as a JsonValue0
func (t JsonValue) AsJsonValue0() (JsonValue0, error) {
	var body JsonValue0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue0 overwrites any union data inside the JsonValue as the provided JsonValue0
func (t *JsonValue) FromJsonValue0(v JsonValue0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue0 performs a merge with any union data inside the JsonValue, using the provided JsonValue0
func (t *JsonValue) MergeJsonValue0(v JsonValue0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsJsonValue1 returns the union data inside the JsonValue as a JsonValue1
func (t JsonValue) AsJsonValue1() (JsonValue1, error) {
	var body JsonValue1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue1 overwrites any union data inside the JsonValue as the provided JsonValue1
func (t *JsonValue) FromJsonValue1(v JsonValue1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue1 performs a merge with any union data inside the JsonValue, using the provided JsonValue1
func (t *JsonValue) MergeJsonValue1(v JsonValue1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsJsonValue2 returns the union data inside the JsonValue as a JsonValue2
func (t JsonValue) AsJsonValue2() (JsonValue2, error) {
	var body JsonValue2
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue2 overwrites any union data inside the JsonValue as the provided JsonValue2
func (t *JsonValue) FromJsonValue2(v JsonValue2) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue2 performs a merge with any union data inside the JsonValue, using the provided JsonValue2
func (t *JsonValue) MergeJsonValue2(v JsonValue2) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsJsonValue3 returns the union data inside the JsonValue as a JsonValue3
func (t JsonValue) AsJsonValue3() (JsonValue3, error) {
	var body JsonValue3
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue3 overwrites any union data inside the JsonValue as the provided JsonValue3
func (t *JsonValue) FromJsonValue3(v JsonValue3) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue3 performs a merge with any union data inside the JsonValue, using the provided JsonValue3
func (t *JsonValue) MergeJsonValue3(v JsonValue3) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsJsonValue4 returns the union data inside the JsonValue as a JsonValue4
func (t JsonValue) AsJsonValue4() (JsonValue4, error) {
	var body JsonValue4
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue4 overwrites any union data inside the JsonValue as the provided JsonValue4
func (t *JsonValue) FromJsonValue4(v JsonValue4) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue4 performs a merge with any union data inside the JsonValue, using the provided JsonValue4
func (t *JsonValue) MergeJsonValue4(v JsonValue4) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsJsonValue5 returns the union data inside the JsonValue as a JsonValue5
func (t JsonValue) AsJsonValue5() (JsonValue5, error) {
	var body JsonValue5
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromJsonValue5 overwrites any union data inside the JsonValue as the provided JsonValue5
func (t *JsonValue) FromJsonValue5(v JsonValue5) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeJsonValue5 performs a merge with any union data inside the JsonValue, using the provided JsonValue5
func (t *JsonValue) MergeJsonValue5(v JsonValue5) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t JsonValue) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *JsonValue) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsTIDeferredStatePayloadNextKwargs0 returns the union data inside the TIDeferredStatePayload_NextKwargs as a TIDeferredStatePayloadNextKwargs0
func (t TIDeferredStatePayload_NextKwargs) AsTIDeferredStatePayloadNextKwargs0() (TIDeferredStatePayloadNextKwargs0, error) {
	var body TIDeferredStatePayloadNextKwargs0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIDeferredStatePayloadNextKwargs0 overwrites any union data inside the TIDeferredStatePayload_NextKwargs as the provided TIDeferredStatePayloadNextKwargs0
func (t *TIDeferredStatePayload_NextKwargs) FromTIDeferredStatePayloadNextKwargs0(v TIDeferredStatePayloadNextKwargs0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIDeferredStatePayloadNextKwargs0 performs a merge with any union data inside the TIDeferredStatePayload_NextKwargs, using the provided TIDeferredStatePayloadNextKwargs0
func (t *TIDeferredStatePayload_NextKwargs) MergeTIDeferredStatePayloadNextKwargs0(v TIDeferredStatePayloadNextKwargs0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIDeferredStatePayloadNextKwargs1 returns the union data inside the TIDeferredStatePayload_NextKwargs as a TIDeferredStatePayloadNextKwargs1
func (t TIDeferredStatePayload_NextKwargs) AsTIDeferredStatePayloadNextKwargs1() (TIDeferredStatePayloadNextKwargs1, error) {
	var body TIDeferredStatePayloadNextKwargs1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIDeferredStatePayloadNextKwargs1 overwrites any union data inside the TIDeferredStatePayload_NextKwargs as the provided TIDeferredStatePayloadNextKwargs1
func (t *TIDeferredStatePayload_NextKwargs) FromTIDeferredStatePayloadNextKwargs1(v TIDeferredStatePayloadNextKwargs1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIDeferredStatePayloadNextKwargs1 performs a merge with any union data inside the TIDeferredStatePayload_NextKwargs, using the provided TIDeferredStatePayloadNextKwargs1
func (t *TIDeferredStatePayload_NextKwargs) MergeTIDeferredStatePayloadNextKwargs1(v TIDeferredStatePayloadNextKwargs1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t TIDeferredStatePayload_NextKwargs) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *TIDeferredStatePayload_NextKwargs) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsTIDeferredStatePayloadTriggerKwargs0 returns the union data inside the TIDeferredStatePayload_TriggerKwargs as a TIDeferredStatePayloadTriggerKwargs0
func (t TIDeferredStatePayload_TriggerKwargs) AsTIDeferredStatePayloadTriggerKwargs0() (TIDeferredStatePayloadTriggerKwargs0, error) {
	var body TIDeferredStatePayloadTriggerKwargs0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIDeferredStatePayloadTriggerKwargs0 overwrites any union data inside the TIDeferredStatePayload_TriggerKwargs as the provided TIDeferredStatePayloadTriggerKwargs0
func (t *TIDeferredStatePayload_TriggerKwargs) FromTIDeferredStatePayloadTriggerKwargs0(v TIDeferredStatePayloadTriggerKwargs0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIDeferredStatePayloadTriggerKwargs0 performs a merge with any union data inside the TIDeferredStatePayload_TriggerKwargs, using the provided TIDeferredStatePayloadTriggerKwargs0
func (t *TIDeferredStatePayload_TriggerKwargs) MergeTIDeferredStatePayloadTriggerKwargs0(v TIDeferredStatePayloadTriggerKwargs0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIDeferredStatePayloadTriggerKwargs1 returns the union data inside the TIDeferredStatePayload_TriggerKwargs as a TIDeferredStatePayloadTriggerKwargs1
func (t TIDeferredStatePayload_TriggerKwargs) AsTIDeferredStatePayloadTriggerKwargs1() (TIDeferredStatePayloadTriggerKwargs1, error) {
	var body TIDeferredStatePayloadTriggerKwargs1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIDeferredStatePayloadTriggerKwargs1 overwrites any union data inside the TIDeferredStatePayload_TriggerKwargs as the provided TIDeferredStatePayloadTriggerKwargs1
func (t *TIDeferredStatePayload_TriggerKwargs) FromTIDeferredStatePayloadTriggerKwargs1(v TIDeferredStatePayloadTriggerKwargs1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIDeferredStatePayloadTriggerKwargs1 performs a merge with any union data inside the TIDeferredStatePayload_TriggerKwargs, using the provided TIDeferredStatePayloadTriggerKwargs1
func (t *TIDeferredStatePayload_TriggerKwargs) MergeTIDeferredStatePayloadTriggerKwargs1(v TIDeferredStatePayloadTriggerKwargs1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t TIDeferredStatePayload_TriggerKwargs) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *TIDeferredStatePayload_TriggerKwargs) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsTIRunContextNextKwargs0 returns the union data inside the TIRunContext_NextKwargs as a TIRunContextNextKwargs0
func (t TIRunContext_NextKwargs) AsTIRunContextNextKwargs0() (TIRunContextNextKwargs0, error) {
	var body TIRunContextNextKwargs0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIRunContextNextKwargs0 overwrites any union data inside the TIRunContext_NextKwargs as the provided TIRunContextNextKwargs0
func (t *TIRunContext_NextKwargs) FromTIRunContextNextKwargs0(v TIRunContextNextKwargs0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIRunContextNextKwargs0 performs a merge with any union data inside the TIRunContext_NextKwargs, using the provided TIRunContextNextKwargs0
func (t *TIRunContext_NextKwargs) MergeTIRunContextNextKwargs0(v TIRunContextNextKwargs0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIRunContextNextKwargs1 returns the union data inside the TIRunContext_NextKwargs as a TIRunContextNextKwargs1
func (t TIRunContext_NextKwargs) AsTIRunContextNextKwargs1() (TIRunContextNextKwargs1, error) {
	var body TIRunContextNextKwargs1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIRunContextNextKwargs1 overwrites any union data inside the TIRunContext_NextKwargs as the provided TIRunContextNextKwargs1
func (t *TIRunContext_NextKwargs) FromTIRunContextNextKwargs1(v TIRunContextNextKwargs1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIRunContextNextKwargs1 performs a merge with any union data inside the TIRunContext_NextKwargs, using the provided TIRunContextNextKwargs1
func (t *TIRunContext_NextKwargs) MergeTIRunContextNextKwargs1(v TIRunContextNextKwargs1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t TIRunContext_NextKwargs) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *TIRunContext_NextKwargs) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsTISkippedDownstreamTasksStatePayloadTasks0 returns the union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item as a TISkippedDownstreamTasksStatePayloadTasks0
func (t TISkippedDownstreamTasksStatePayload_Tasks_Item) AsTISkippedDownstreamTasksStatePayloadTasks0() (TISkippedDownstreamTasksStatePayloadTasks0, error) {
	var body TISkippedDownstreamTasksStatePayloadTasks0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTISkippedDownstreamTasksStatePayloadTasks0 overwrites any union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item as the provided TISkippedDownstreamTasksStatePayloadTasks0
func (t *TISkippedDownstreamTasksStatePayload_Tasks_Item) FromTISkippedDownstreamTasksStatePayloadTasks0(v TISkippedDownstreamTasksStatePayloadTasks0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTISkippedDownstreamTasksStatePayloadTasks0 performs a merge with any union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item, using the provided TISkippedDownstreamTasksStatePayloadTasks0
func (t *TISkippedDownstreamTasksStatePayload_Tasks_Item) MergeTISkippedDownstreamTasksStatePayloadTasks0(v TISkippedDownstreamTasksStatePayloadTasks0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTISkippedDownstreamTasksStatePayloadTasks1 returns the union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item as a TISkippedDownstreamTasksStatePayloadTasks1
func (t TISkippedDownstreamTasksStatePayload_Tasks_Item) AsTISkippedDownstreamTasksStatePayloadTasks1() (TISkippedDownstreamTasksStatePayloadTasks1, error) {
	var body TISkippedDownstreamTasksStatePayloadTasks1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTISkippedDownstreamTasksStatePayloadTasks1 overwrites any union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item as the provided TISkippedDownstreamTasksStatePayloadTasks1
func (t *TISkippedDownstreamTasksStatePayload_Tasks_Item) FromTISkippedDownstreamTasksStatePayloadTasks1(v TISkippedDownstreamTasksStatePayloadTasks1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTISkippedDownstreamTasksStatePayloadTasks1 performs a merge with any union data inside the TISkippedDownstreamTasksStatePayload_Tasks_Item, using the provided TISkippedDownstreamTasksStatePayloadTasks1
func (t *TISkippedDownstreamTasksStatePayload_Tasks_Item) MergeTISkippedDownstreamTasksStatePayloadTasks1(v TISkippedDownstreamTasksStatePayloadTasks1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t TISkippedDownstreamTasksStatePayload_Tasks_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *TISkippedDownstreamTasksStatePayload_Tasks_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsTITerminalStatePayload returns the union data inside the TIUpdateStatePayload as a TITerminalStatePayload
func (t TIUpdateStatePayload) AsTITerminalStatePayload() (TITerminalStatePayload, error) {
	var body TITerminalStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTITerminalStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TITerminalStatePayload
func (t *TIUpdateStatePayload) FromTITerminalStatePayload(v TITerminalStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTITerminalStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TITerminalStatePayload
func (t *TIUpdateStatePayload) MergeTITerminalStatePayload(v TITerminalStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTISuccessStatePayload returns the union data inside the TIUpdateStatePayload as a TISuccessStatePayload
func (t TIUpdateStatePayload) AsTISuccessStatePayload() (TISuccessStatePayload, error) {
	var body TISuccessStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTISuccessStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TISuccessStatePayload
func (t *TIUpdateStatePayload) FromTISuccessStatePayload(v TISuccessStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTISuccessStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TISuccessStatePayload
func (t *TIUpdateStatePayload) MergeTISuccessStatePayload(v TISuccessStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTITargetStatePayload returns the union data inside the TIUpdateStatePayload as a TITargetStatePayload
func (t TIUpdateStatePayload) AsTITargetStatePayload() (TITargetStatePayload, error) {
	var body TITargetStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTITargetStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TITargetStatePayload
func (t *TIUpdateStatePayload) FromTITargetStatePayload(v TITargetStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTITargetStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TITargetStatePayload
func (t *TIUpdateStatePayload) MergeTITargetStatePayload(v TITargetStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIDeferredStatePayload returns the union data inside the TIUpdateStatePayload as a TIDeferredStatePayload
func (t TIUpdateStatePayload) AsTIDeferredStatePayload() (TIDeferredStatePayload, error) {
	var body TIDeferredStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIDeferredStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TIDeferredStatePayload
func (t *TIUpdateStatePayload) FromTIDeferredStatePayload(v TIDeferredStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIDeferredStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TIDeferredStatePayload
func (t *TIUpdateStatePayload) MergeTIDeferredStatePayload(v TIDeferredStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIRescheduleStatePayload returns the union data inside the TIUpdateStatePayload as a TIRescheduleStatePayload
func (t TIUpdateStatePayload) AsTIRescheduleStatePayload() (TIRescheduleStatePayload, error) {
	var body TIRescheduleStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIRescheduleStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TIRescheduleStatePayload
func (t *TIUpdateStatePayload) FromTIRescheduleStatePayload(v TIRescheduleStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIRescheduleStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TIRescheduleStatePayload
func (t *TIUpdateStatePayload) MergeTIRescheduleStatePayload(v TIRescheduleStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsTIRetryStatePayload returns the union data inside the TIUpdateStatePayload as a TIRetryStatePayload
func (t TIUpdateStatePayload) AsTIRetryStatePayload() (TIRetryStatePayload, error) {
	var body TIRetryStatePayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromTIRetryStatePayload overwrites any union data inside the TIUpdateStatePayload as the provided TIRetryStatePayload
func (t *TIUpdateStatePayload) FromTIRetryStatePayload(v TIRetryStatePayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeTIRetryStatePayload performs a merge with any union data inside the TIUpdateStatePayload, using the provided TIRetryStatePayload
func (t *TIUpdateStatePayload) MergeTIRetryStatePayload(v TIRetryStatePayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t TIUpdateStatePayload) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *TIUpdateStatePayload) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsValidationErrorLoc0 returns the union data inside the ValidationError_Loc_Item as a ValidationErrorLoc0
func (t ValidationError_Loc_Item) AsValidationErrorLoc0() (ValidationErrorLoc0, error) {
	var body ValidationErrorLoc0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromValidationErrorLoc0 overwrites any union data inside the ValidationError_Loc_Item as the provided ValidationErrorLoc0
func (t *ValidationError_Loc_Item) FromValidationErrorLoc0(v ValidationErrorLoc0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeValidationErrorLoc0 performs a merge with any union data inside the ValidationError_Loc_Item, using the provided ValidationErrorLoc0
func (t *ValidationError_Loc_Item) MergeValidationErrorLoc0(v ValidationErrorLoc0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsValidationErrorLoc1 returns the union data inside the ValidationError_Loc_Item as a ValidationErrorLoc1
func (t ValidationError_Loc_Item) AsValidationErrorLoc1() (ValidationErrorLoc1, error) {
	var body ValidationErrorLoc1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromValidationErrorLoc1 overwrites any union data inside the ValidationError_Loc_Item as the provided ValidationErrorLoc1
func (t *ValidationError_Loc_Item) FromValidationErrorLoc1(v ValidationErrorLoc1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeValidationErrorLoc1 performs a merge with any union data inside the ValidationError_Loc_Item, using the provided ValidationErrorLoc1
func (t *ValidationError_Loc_Item) MergeValidationErrorLoc1(v ValidationErrorLoc1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ValidationError_Loc_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ValidationError_Loc_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	*resty.Client

	RequestMiddleware []resty.RequestMiddleware
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

func NewClient(server string, opts ...ClientOption) (ClientInterface, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
		Client: resty.New(),
	}
	client.Client.SetBaseURL(client.Server)
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}

	return &client, nil
}

// WithClient allows overriding the default [resty.Client], which is
// automatically created using http.Client.
//
// If this is used the `server` base URL argument passed in will not be respected anymore
func WithClient(r *resty.Client) ClientOption {
	return func(c *Client) error {
		c.Client = r
		return nil
	}
}

// WithRoundTripper method sets custom http.Transport or any http.RoundTripper
// compatible interface implementation in the Resty client
func WithRoundTripper(transport http.RoundTripper) ClientOption {
	return func(c *Client) error {
		c.Client.SetTransport(transport)
		return nil
	}
}

// WithRequestMiddleware allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestMiddleware(mw resty.RequestMiddleware) ClientOption {
	return func(c *Client) error {
		c.RequestMiddleware = append(c.RequestMiddleware, mw)
		c.Client = c.Client.AddRequestMiddleware(mw)
		return nil
	}
}

func (c *Client) AssetEvents() AssetEventsClient {
	return &assetEventsClient{c.Client}
}
func (c *Client) Assets() AssetsClient {
	return &assetsClient{c.Client}
}
func (c *Client) Connections() ConnectionsClient {
	return &connectionsClient{c.Client}
}
func (c *Client) DagRuns() DagRunsClient {
	return &dagRunsClient{c.Client}
}
func (c *Client) TaskInstances() TaskInstancesClient {
	return &taskInstancesClient{c.Client}
}
func (c *Client) TaskReschedules() TaskReschedulesClient {
	return &taskReschedulesClient{c.Client}
}
func (c *Client) Variables() VariablesClient {
	return &variablesClient{c.Client}
}
func (c *Client) Xcoms() XcomsClient {
	return &xcomsClient{c.Client}
}

type ClientInterface interface {
	// AssetEvents deals with all the Asset Events endpoints
	AssetEvents() AssetEventsClient
	// Assets deals with all the Assets endpoints
	Assets() AssetsClient
	// Connections deals with all the Connections endpoints
	Connections() ConnectionsClient
	// DagRuns deals with all the Dag Runs endpoints
	DagRuns() DagRunsClient
	// TaskInstances deals with all the Task Instances endpoints
	TaskInstances() TaskInstancesClient
	// TaskReschedules deals with all the Task Reschedules endpoints
	TaskReschedules() TaskReschedulesClient
	// Variables deals with all the Variables endpoints
	Variables() VariablesClient
	// Xcoms deals with all the XComs endpoints
	Xcoms() XcomsClient
}

type GeneralHTTPError struct {
	Response *resty.Response
	JSON     map[string]any
	Text     string
}

var errorTypes = map[int]string{
	1: "informational response",
	3: "redirect response",
	4: "client error",
	5: "server error",
}

func (e GeneralHTTPError) Error() string {
	var b strings.Builder
	kind, ok := errorTypes[e.Response.StatusCode()/100]
	if !ok {
		kind = "unknown HTTP error"
	}
	fmt.Fprintf(&b, "%s '%s'", kind, e.Response.Status())
	if e.JSON != nil {
		fmt.Fprintf(&b, " %v", e.JSON)
	} else {
		fmt.Fprintf(&b, " content=%q", e.Text)
	}
	return b.String()
}

func HandleError(client *resty.Client, resp *resty.Response) error {
	if !resp.IsError() {
		return nil
	}

	e := GeneralHTTPError{Response: resp}

	// fmt.Printf("HandleError: IsRead: %v Error: %T\n", resp.IsRead, resp.Error())
	e.Text = resp.String()
	if resp.Header().Get("content-type") == "application/json" {
		if json.Unmarshal([]byte(e.Text), &e.JSON) == nil {
			e.Text = ""
		}
	}

	// Set the parsed error back into the object so `resp.Error()` returns the populated one!
	resp.Request.SetError(&e)

	return &e
}

type assetEventsClient struct {
	*resty.Client
}

// GetByAssetNameUriResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *assetEventsClient) GetByAssetNameUriResponse(ctx context.Context, params *GetAssetEventByAssetNameUriParams) (resp *resty.Response, err error) {
	var res AssetEventsResponse

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "name", runtime.ParamLocationQuery, params.Name)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "uri", runtime.ParamLocationQuery, params.Uri)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("name", param0).
		SetQueryParam("uri", param1).
		SetResult(&res).
		Get("asset-events/by-asset")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *assetEventsClient) GetByAssetNameUri(ctx context.Context, params *GetAssetEventByAssetNameUriParams) (*AssetEventsResponse, error) {
	res, err := c.GetByAssetNameUriResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*AssetEventsResponse), nil
}

// GetByAssetAliasResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *assetEventsClient) GetByAssetAliasResponse(ctx context.Context, params *GetAssetEventByAssetAliasParams) (resp *resty.Response, err error) {
	var res AssetEventsResponse

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "name", runtime.ParamLocationQuery, params.Name)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("name", param0).
		SetResult(&res).
		Get("asset-events/by-asset-alias")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *assetEventsClient) GetByAssetAlias(ctx context.Context, params *GetAssetEventByAssetAliasParams) (*AssetEventsResponse, error) {
	res, err := c.GetByAssetAliasResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*AssetEventsResponse), nil
}

type assetsClient struct {
	*resty.Client
}

// GetByNameResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *assetsClient) GetByNameResponse(ctx context.Context, params *GetAssetByNameParams) (resp *resty.Response, err error) {
	var res AssetResponse

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "name", runtime.ParamLocationQuery, params.Name)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("name", param0).
		SetResult(&res).
		Get("assets/by-name")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *assetsClient) GetByName(ctx context.Context, params *GetAssetByNameParams) (*AssetResponse, error) {
	res, err := c.GetByNameResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*AssetResponse), nil
}

// GetByUriResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *assetsClient) GetByUriResponse(ctx context.Context, params *GetAssetByUriParams) (resp *resty.Response, err error) {
	var res AssetResponse

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "uri", runtime.ParamLocationQuery, params.Uri)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("uri", param0).
		SetResult(&res).
		Get("assets/by-uri")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *assetsClient) GetByUri(ctx context.Context, params *GetAssetByUriParams) (*AssetResponse, error) {
	res, err := c.GetByUriResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*AssetResponse), nil
}

type connectionsClient struct {
	*resty.Client
}

// GetResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *connectionsClient) GetResponse(ctx context.Context, connectionId string) (resp *resty.Response, err error) {
	var res ConnectionResponse

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "connection_id", runtime.ParamLocationPath, connectionId)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("connection_id", pathParam0).
		SetResult(&res).
		Get("connections/{connection_id}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *connectionsClient) Get(ctx context.Context, connectionId string) (*ConnectionResponse, error) {
	res, err := c.GetResponse(ctx, connectionId)
	if err != nil {
		return nil, err
	}

	return res.Result().(*ConnectionResponse), nil
}

type dagRunsClient struct {
	*resty.Client
}

// GetDrCountResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *dagRunsClient) GetDrCountResponse(ctx context.Context, params *GetDrCountParams) (resp *resty.Response, err error) {
	var res int

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "dag_id", runtime.ParamLocationQuery, params.DagId)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "logical_dates", runtime.ParamLocationQuery, params.LogicalDates)
	if err != nil {
		return nil, err
	}
	var param2 string

	param2, err = runtime.StyleParamWithLocation("form", true, "run_ids", runtime.ParamLocationQuery, params.RunIds)
	if err != nil {
		return nil, err
	}
	var param3 string

	param3, err = runtime.StyleParamWithLocation("form", true, "states", runtime.ParamLocationQuery, params.States)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("dag_id", param0).
		SetQueryParam("logical_dates", param1).
		SetQueryParam("run_ids", param2).
		SetQueryParam("states", param3).
		SetResult(&res).
		Get("dag-runs/count")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *dagRunsClient) GetDrCount(ctx context.Context, params *GetDrCountParams) (*int, error) {
	res, err := c.GetDrCountResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*int), nil
}

// TriggerResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *dagRunsClient) TriggerResponse(ctx context.Context, dagId string, runId string, body *TriggerDAGRunPayload) (resp *resty.Response, err error) {

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("Trigger requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetBody(body).
		Post("dag-runs/{dag_id}/{run_id}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *dagRunsClient) Trigger(ctx context.Context, dagId string, runId string, body *TriggerDAGRunPayload) error {
	_, err := c.TriggerResponse(ctx, dagId, runId, body)
	return err
}

// ClearResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *dagRunsClient) ClearResponse(ctx context.Context, dagId string, runId string) (resp *resty.Response, err error) {

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		Post("dag-runs/{dag_id}/{run_id}/clear")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *dagRunsClient) Clear(ctx context.Context, dagId string, runId string) error {
	_, err := c.ClearResponse(ctx, dagId, runId)
	return err
}

// GetStateResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *dagRunsClient) GetStateResponse(ctx context.Context, dagId string, runId string) (resp *resty.Response, err error) {
	var res DagRunStateResponse

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetResult(&res).
		Get("dag-runs/{dag_id}/{run_id}/state")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *dagRunsClient) GetState(ctx context.Context, dagId string, runId string) (*DagRunStateResponse, error) {
	res, err := c.GetStateResponse(ctx, dagId, runId)
	if err != nil {
		return nil, err
	}

	return res.Result().(*DagRunStateResponse), nil
}

type taskInstancesClient struct {
	*resty.Client
}

// GetCountResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) GetCountResponse(ctx context.Context, params *GetTaskInstanceCountParams) (resp *resty.Response, err error) {
	var res int

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "dag_id", runtime.ParamLocationQuery, params.DagId)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "task_ids", runtime.ParamLocationQuery, params.TaskIds)
	if err != nil {
		return nil, err
	}
	var param2 string

	param2, err = runtime.StyleParamWithLocation("form", true, "task_group_id", runtime.ParamLocationQuery, params.TaskGroupId)
	if err != nil {
		return nil, err
	}
	var param3 string

	param3, err = runtime.StyleParamWithLocation("form", true, "logical_dates", runtime.ParamLocationQuery, params.LogicalDates)
	if err != nil {
		return nil, err
	}
	var param4 string

	param4, err = runtime.StyleParamWithLocation("form", true, "run_ids", runtime.ParamLocationQuery, params.RunIds)
	if err != nil {
		return nil, err
	}
	var param5 string

	param5, err = runtime.StyleParamWithLocation("form", true, "states", runtime.ParamLocationQuery, params.States)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("dag_id", param0).
		SetQueryParam("task_ids", param1).
		SetQueryParam("task_group_id", param2).
		SetQueryParam("logical_dates", param3).
		SetQueryParam("run_ids", param4).
		SetQueryParam("states", param5).
		SetResult(&res).
		Get("task-instances/count")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) GetCount(ctx context.Context, params *GetTaskInstanceCountParams) (*int, error) {
	res, err := c.GetCountResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*int), nil
}

// GetStatesResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) GetStatesResponse(ctx context.Context, params *GetTaskInstanceStatesParams) (resp *resty.Response, err error) {
	var res TaskStatesResponse

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "dag_id", runtime.ParamLocationQuery, params.DagId)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "task_ids", runtime.ParamLocationQuery, params.TaskIds)
	if err != nil {
		return nil, err
	}
	var param2 string

	param2, err = runtime.StyleParamWithLocation("form", true, "task_group_id", runtime.ParamLocationQuery, params.TaskGroupId)
	if err != nil {
		return nil, err
	}
	var param3 string

	param3, err = runtime.StyleParamWithLocation("form", true, "logical_dates", runtime.ParamLocationQuery, params.LogicalDates)
	if err != nil {
		return nil, err
	}
	var param4 string

	param4, err = runtime.StyleParamWithLocation("form", true, "run_ids", runtime.ParamLocationQuery, params.RunIds)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetQueryParam("dag_id", param0).
		SetQueryParam("task_ids", param1).
		SetQueryParam("task_group_id", param2).
		SetQueryParam("logical_dates", param3).
		SetQueryParam("run_ids", param4).
		SetResult(&res).
		Get("task-instances/states")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) GetStates(ctx context.Context, params *GetTaskInstanceStatesParams) (*TaskStatesResponse, error) {
	res, err := c.GetStatesResponse(ctx, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*TaskStatesResponse), nil
}

// HeartbeatResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) HeartbeatResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIHeartbeatInfo) (resp *resty.Response, err error) {

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("Heartbeat requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetBody(body).
		Put("task-instances/{task_instance_id}/heartbeat")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) Heartbeat(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIHeartbeatInfo) error {
	_, err := c.HeartbeatResponse(ctx, taskInstanceId, body)
	return err
}

// GetPreviousSuccessfulDagrunResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) GetPreviousSuccessfulDagrunResponse(ctx context.Context, taskInstanceId openapi_types.UUID) (resp *resty.Response, err error) {
	var res PrevSuccessfulDagRunResponse

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetResult(&res).
		Get("task-instances/{task_instance_id}/previous-successful-dagrun")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) GetPreviousSuccessfulDagrun(ctx context.Context, taskInstanceId openapi_types.UUID) (*PrevSuccessfulDagRunResponse, error) {
	res, err := c.GetPreviousSuccessfulDagrunResponse(ctx, taskInstanceId)
	if err != nil {
		return nil, err
	}

	return res.Result().(*PrevSuccessfulDagRunResponse), nil
}

// PutRenderedFieldsResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) PutRenderedFieldsResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *map[string]*JsonValue) (resp *resty.Response, err error) {
	var res interface{}

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("PutRenderedFields requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetBody(body).
		SetResult(&res).
		Put("task-instances/{task_instance_id}/rtif")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) PutRenderedFields(ctx context.Context, taskInstanceId openapi_types.UUID, body *map[string]*JsonValue) (*interface{}, error) {
	res, err := c.PutRenderedFieldsResponse(ctx, taskInstanceId, body)
	if err != nil {
		return nil, err
	}

	return res.Result().(*interface{}), nil
}

// RunResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) RunResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIEnterRunningPayload) (resp *resty.Response, err error) {
	var res TIRunContext

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("Run requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetBody(body).
		SetResult(&res).
		Patch("task-instances/{task_instance_id}/run")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) Run(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIEnterRunningPayload) (*TIRunContext, error) {
	res, err := c.RunResponse(ctx, taskInstanceId, body)
	if err != nil {
		return nil, err
	}

	return res.Result().(*TIRunContext), nil
}

// SkipDownstreamResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) SkipDownstreamResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TISkippedDownstreamTasksStatePayload) (resp *resty.Response, err error) {

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("SkipDownstream requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetBody(body).
		Patch("task-instances/{task_instance_id}/skip-downstream")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) SkipDownstream(ctx context.Context, taskInstanceId openapi_types.UUID, body *TISkippedDownstreamTasksStatePayload) error {
	_, err := c.SkipDownstreamResponse(ctx, taskInstanceId, body)
	return err
}

// UpdateStateResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskInstancesClient) UpdateStateResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIUpdateStatePayload) (resp *resty.Response, err error) {

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("UpdateState requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetBody(body).
		Patch("task-instances/{task_instance_id}/state")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskInstancesClient) UpdateState(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIUpdateStatePayload) error {
	_, err := c.UpdateStateResponse(ctx, taskInstanceId, body)
	return err
}

type taskReschedulesClient struct {
	*resty.Client
}

// GetStartDateResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *taskReschedulesClient) GetStartDateResponse(ctx context.Context, taskInstanceId openapi_types.UUID) (resp *resty.Response, err error) {
	var res time.Time

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "task_instance_id", runtime.ParamLocationPath, taskInstanceId)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("task_instance_id", pathParam0).
		SetResult(&res).
		Get("task-reschedules/{task_instance_id}/start_date")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *taskReschedulesClient) GetStartDate(ctx context.Context, taskInstanceId openapi_types.UUID) (*time.Time, error) {
	res, err := c.GetStartDateResponse(ctx, taskInstanceId)
	if err != nil {
		return nil, err
	}

	return res.Result().(*time.Time), nil
}

type variablesClient struct {
	*resty.Client
}

// DeleteResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *variablesClient) DeleteResponse(ctx context.Context, variableKey string) (resp *resty.Response, err error) {
	var res HTTPValidationError

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "variable_key", runtime.ParamLocationPath, variableKey)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("variable_key", pathParam0).
		SetResult(&res).
		Delete("variables/{variable_key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *variablesClient) Delete(ctx context.Context, variableKey string) (*HTTPValidationError, error) {
	res, err := c.DeleteResponse(ctx, variableKey)
	if err != nil {
		return nil, err
	}

	return res.Result().(*HTTPValidationError), nil
}

// GetResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *variablesClient) GetResponse(ctx context.Context, variableKey string) (resp *resty.Response, err error) {
	var res VariableResponse

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "variable_key", runtime.ParamLocationPath, variableKey)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("variable_key", pathParam0).
		SetResult(&res).
		Get("variables/{variable_key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *variablesClient) Get(ctx context.Context, variableKey string) (*VariableResponse, error) {
	res, err := c.GetResponse(ctx, variableKey)
	if err != nil {
		return nil, err
	}

	return res.Result().(*VariableResponse), nil
}

// PutResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *variablesClient) PutResponse(ctx context.Context, variableKey string, body *VariablePostBody) (resp *resty.Response, err error) {
	var res interface{}

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "variable_key", runtime.ParamLocationPath, variableKey)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("Put requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("variable_key", pathParam0).
		SetBody(body).
		SetResult(&res).
		Put("variables/{variable_key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *variablesClient) Put(ctx context.Context, variableKey string, body *VariablePostBody) (*interface{}, error) {
	res, err := c.PutResponse(ctx, variableKey, body)
	if err != nil {
		return nil, err
	}

	return res.Result().(*interface{}), nil
}

type xcomsClient struct {
	*resty.Client
}

// DeleteResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *xcomsClient) DeleteResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *DeleteXcomParams) (resp *resty.Response, err error) {
	var res interface{}

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}
	var pathParam2 string

	pathParam2, err = runtime.StyleParamWithLocation("simple", false, "task_id", runtime.ParamLocationPath, taskId)
	if err != nil {
		return nil, err
	}
	var pathParam3 string

	pathParam3, err = runtime.StyleParamWithLocation("simple", false, "key", runtime.ParamLocationPath, key)
	if err != nil {
		return nil, err
	}

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "map_index", runtime.ParamLocationQuery, params.MapIndex)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetPathParam("task_id", pathParam2).
		SetPathParam("key", pathParam3).
		SetQueryParam("map_index", param0).
		SetResult(&res).
		Delete("xcoms/{dag_id}/{run_id}/{task_id}/{key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *xcomsClient) Delete(ctx context.Context, dagId string, runId string, taskId string, key string, params *DeleteXcomParams) (*interface{}, error) {
	res, err := c.DeleteResponse(ctx, dagId, runId, taskId, key, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*interface{}), nil
}

// GetResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *xcomsClient) GetResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *GetXcomParams) (resp *resty.Response, err error) {
	var res XComResponse

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}
	var pathParam2 string

	pathParam2, err = runtime.StyleParamWithLocation("simple", false, "task_id", runtime.ParamLocationPath, taskId)
	if err != nil {
		return nil, err
	}
	var pathParam3 string

	pathParam3, err = runtime.StyleParamWithLocation("simple", false, "key", runtime.ParamLocationPath, key)
	if err != nil {
		return nil, err
	}

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "map_index", runtime.ParamLocationQuery, params.MapIndex)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "include_prior_dates", runtime.ParamLocationQuery, params.IncludePriorDates)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetPathParam("task_id", pathParam2).
		SetPathParam("key", pathParam3).
		SetQueryParam("map_index", param0).
		SetQueryParam("include_prior_dates", param1).
		SetResult(&res).
		Get("xcoms/{dag_id}/{run_id}/{task_id}/{key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *xcomsClient) Get(ctx context.Context, dagId string, runId string, taskId string, key string, params *GetXcomParams) (*XComResponse, error) {
	res, err := c.GetResponse(ctx, dagId, runId, taskId, key, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*XComResponse), nil
}

// HeadResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *xcomsClient) HeadResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *HeadXcomParams) (resp *resty.Response, err error) {
	var res interface{}

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}
	var pathParam2 string

	pathParam2, err = runtime.StyleParamWithLocation("simple", false, "task_id", runtime.ParamLocationPath, taskId)
	if err != nil {
		return nil, err
	}
	var pathParam3 string

	pathParam3, err = runtime.StyleParamWithLocation("simple", false, "key", runtime.ParamLocationPath, key)
	if err != nil {
		return nil, err
	}

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "map_index", runtime.ParamLocationQuery, params.MapIndex)
	if err != nil {
		return nil, err
	}

	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetPathParam("task_id", pathParam2).
		SetPathParam("key", pathParam3).
		SetQueryParam("map_index", param0).
		SetResult(&res).
		Head("xcoms/{dag_id}/{run_id}/{task_id}/{key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *xcomsClient) Head(ctx context.Context, dagId string, runId string, taskId string, key string, params *HeadXcomParams) (*interface{}, error) {
	res, err := c.HeadResponse(ctx, dagId, runId, taskId, key, params)
	if err != nil {
		return nil, err
	}

	return res.Result().(*interface{}), nil
}

// SetResponse performs the HTTP request and returns the lower level [resty.Response]
func (c *xcomsClient) SetResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *SetXcomParams, body *JsonValue) (resp *resty.Response, err error) {
	var res interface{}

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "dag_id", runtime.ParamLocationPath, dagId)
	if err != nil {
		return nil, err
	}
	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "run_id", runtime.ParamLocationPath, runId)
	if err != nil {
		return nil, err
	}
	var pathParam2 string

	pathParam2, err = runtime.StyleParamWithLocation("simple", false, "task_id", runtime.ParamLocationPath, taskId)
	if err != nil {
		return nil, err
	}
	var pathParam3 string

	pathParam3, err = runtime.StyleParamWithLocation("simple", false, "key", runtime.ParamLocationPath, key)
	if err != nil {
		return nil, err
	}

	var param0 string

	param0, err = runtime.StyleParamWithLocation("form", true, "map_index", runtime.ParamLocationQuery, params.MapIndex)
	if err != nil {
		return nil, err
	}
	var param1 string

	param1, err = runtime.StyleParamWithLocation("form", true, "mapped_length", runtime.ParamLocationQuery, params.MappedLength)
	if err != nil {
		return nil, err
	}

	if body == nil {
		return nil, fmt.Errorf("Set requires a non-nil body argument")
	}
	resp, err = c.R().
		SetContext(ctx).
		SetPathParam("dag_id", pathParam0).
		SetPathParam("run_id", pathParam1).
		SetPathParam("task_id", pathParam2).
		SetPathParam("key", pathParam3).
		SetQueryParam("map_index", param0).
		SetQueryParam("mapped_length", param1).
		SetBody(body).
		SetResult(&res).
		Post("xcoms/{dag_id}/{run_id}/{task_id}/{key}")
	if err != nil {
		return resp, err
	}
	return resp, HandleError(c.Client, resp)
}

func (c *xcomsClient) Set(ctx context.Context, dagId string, runId string, taskId string, key string, params *SetXcomParams, body *JsonValue) (*interface{}, error) {
	res, err := c.SetResponse(ctx, dagId, runId, taskId, key, params, body)
	if err != nil {
		return nil, err
	}

	return res.Result().(*interface{}), nil
}

type AssetEventsClient interface {
	GetByAssetNameUri(ctx context.Context, params *GetAssetEventByAssetNameUriParams) (*AssetEventsResponse, error)
	// GetByAssetNameUriResponse is a lower level version of [GetByAssetNameUri] and provides access to the raw [resty.Response]
	GetByAssetNameUriResponse(ctx context.Context, params *GetAssetEventByAssetNameUriParams) (*resty.Response, error)

	GetByAssetAlias(ctx context.Context, params *GetAssetEventByAssetAliasParams) (*AssetEventsResponse, error)
	// GetByAssetAliasResponse is a lower level version of [GetByAssetAlias] and provides access to the raw [resty.Response]
	GetByAssetAliasResponse(ctx context.Context, params *GetAssetEventByAssetAliasParams) (*resty.Response, error)
}

var _ AssetEventsClient = (*assetEventsClient)(nil)

type AssetsClient interface {
	// Get an Airflow Asset by `name`.
	GetByName(ctx context.Context, params *GetAssetByNameParams) (*AssetResponse, error)
	// GetByNameResponse is a lower level version of [GetByName] and provides access to the raw [resty.Response]
	GetByNameResponse(ctx context.Context, params *GetAssetByNameParams) (*resty.Response, error)

	// Get an Airflow Asset by `uri`.
	GetByUri(ctx context.Context, params *GetAssetByUriParams) (*AssetResponse, error)
	// GetByUriResponse is a lower level version of [GetByUri] and provides access to the raw [resty.Response]
	GetByUriResponse(ctx context.Context, params *GetAssetByUriParams) (*resty.Response, error)
}

var _ AssetsClient = (*assetsClient)(nil)

type ConnectionsClient interface {
	// Get an Airflow connection.
	Get(ctx context.Context, connectionId string) (*ConnectionResponse, error)
	// GetResponse is a lower level version of [Get] and provides access to the raw [resty.Response]
	GetResponse(ctx context.Context, connectionId string) (*resty.Response, error)
}

var _ ConnectionsClient = (*connectionsClient)(nil)

type DagRunsClient interface {
	// Get the count of DAG runs matching the given criteria.
	GetDrCount(ctx context.Context, params *GetDrCountParams) (*int, error)
	// GetDrCountResponse is a lower level version of [GetDrCount] and provides access to the raw [resty.Response]
	GetDrCountResponse(ctx context.Context, params *GetDrCountParams) (*resty.Response, error)

	// Trigger a DAG Run.
	Trigger(ctx context.Context, dagId string, runId string, body *TriggerDAGRunPayload) error
	// TriggerResponse is a lower level version of [Trigger] and provides access to the raw [resty.Response]
	TriggerResponse(ctx context.Context, dagId string, runId string, body *TriggerDAGRunPayload) (*resty.Response, error)

	// Clear a DAG Run.
	Clear(ctx context.Context, dagId string, runId string) error
	// ClearResponse is a lower level version of [Clear] and provides access to the raw [resty.Response]
	ClearResponse(ctx context.Context, dagId string, runId string) (*resty.Response, error)

	// Get a DAG Run State.
	GetState(ctx context.Context, dagId string, runId string) (*DagRunStateResponse, error)
	// GetStateResponse is a lower level version of [GetState] and provides access to the raw [resty.Response]
	GetStateResponse(ctx context.Context, dagId string, runId string) (*resty.Response, error)
}

var _ DagRunsClient = (*dagRunsClient)(nil)

type TaskInstancesClient interface {
	// Get the count of task instances matching the given criteria.
	GetCount(ctx context.Context, params *GetTaskInstanceCountParams) (*int, error)
	// GetCountResponse is a lower level version of [GetCount] and provides access to the raw [resty.Response]
	GetCountResponse(ctx context.Context, params *GetTaskInstanceCountParams) (*resty.Response, error)

	// Get the states for Task Instances with the given criteria.
	GetStates(ctx context.Context, params *GetTaskInstanceStatesParams) (*TaskStatesResponse, error)
	// GetStatesResponse is a lower level version of [GetStates] and provides access to the raw [resty.Response]
	GetStatesResponse(ctx context.Context, params *GetTaskInstanceStatesParams) (*resty.Response, error)

	// Update the heartbeat of a TaskInstance to mark it as alive & still running.
	Heartbeat(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIHeartbeatInfo) error
	// HeartbeatResponse is a lower level version of [Heartbeat] and provides access to the raw [resty.Response]
	HeartbeatResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIHeartbeatInfo) (*resty.Response, error)

	// Get the previous successful DagRun for a TaskInstance.
	//
	// The data from this endpoint is used to get values for Task Context.
	GetPreviousSuccessfulDagrun(ctx context.Context, taskInstanceId openapi_types.UUID) (*PrevSuccessfulDagRunResponse, error)
	// GetPreviousSuccessfulDagrunResponse is a lower level version of [GetPreviousSuccessfulDagrun] and provides access to the raw [resty.Response]
	GetPreviousSuccessfulDagrunResponse(ctx context.Context, taskInstanceId openapi_types.UUID) (*resty.Response, error)

	// Add an RTIF entry for a task instance, sent by the worker.
	PutRenderedFields(ctx context.Context, taskInstanceId openapi_types.UUID, body *map[string]*JsonValue) (*interface{}, error)
	// PutRenderedFieldsResponse is a lower level version of [PutRenderedFields] and provides access to the raw [resty.Response]
	PutRenderedFieldsResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *map[string]*JsonValue) (*resty.Response, error)

	// Run a TaskInstance.
	//
	// This endpoint is used to start a TaskInstance that is in the QUEUED state.
	Run(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIEnterRunningPayload) (*TIRunContext, error)
	// RunResponse is a lower level version of [Run] and provides access to the raw [resty.Response]
	RunResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIEnterRunningPayload) (*resty.Response, error)

	SkipDownstream(ctx context.Context, taskInstanceId openapi_types.UUID, body *TISkippedDownstreamTasksStatePayload) error
	// SkipDownstreamResponse is a lower level version of [SkipDownstream] and provides access to the raw [resty.Response]
	SkipDownstreamResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TISkippedDownstreamTasksStatePayload) (*resty.Response, error)

	// Update the state of a TaskInstance.
	//
	// Not all state transitions are valid, and transitioning to some states requires extra information to be
	// passed along. (Check out the datamodels for details, the rendered docs might not reflect this accurately)
	UpdateState(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIUpdateStatePayload) error
	// UpdateStateResponse is a lower level version of [UpdateState] and provides access to the raw [resty.Response]
	UpdateStateResponse(ctx context.Context, taskInstanceId openapi_types.UUID, body *TIUpdateStatePayload) (*resty.Response, error)
}

var _ TaskInstancesClient = (*taskInstancesClient)(nil)

type TaskReschedulesClient interface {
	// Get the first reschedule date if found, None if no records exist.
	GetStartDate(ctx context.Context, taskInstanceId openapi_types.UUID) (*time.Time, error)
	// GetStartDateResponse is a lower level version of [GetStartDate] and provides access to the raw [resty.Response]
	GetStartDateResponse(ctx context.Context, taskInstanceId openapi_types.UUID) (*resty.Response, error)
}

var _ TaskReschedulesClient = (*taskReschedulesClient)(nil)

type VariablesClient interface {
	// Delete an Airflow Variable.
	Delete(ctx context.Context, variableKey string) (*HTTPValidationError, error)
	// DeleteResponse is a lower level version of [Delete] and provides access to the raw [resty.Response]
	DeleteResponse(ctx context.Context, variableKey string) (*resty.Response, error)

	// Get an Airflow Variable.
	Get(ctx context.Context, variableKey string) (*VariableResponse, error)
	// GetResponse is a lower level version of [Get] and provides access to the raw [resty.Response]
	GetResponse(ctx context.Context, variableKey string) (*resty.Response, error)

	// Set an Airflow Variable.
	Put(ctx context.Context, variableKey string, body *VariablePostBody) (*interface{}, error)
	// PutResponse is a lower level version of [Put] and provides access to the raw [resty.Response]
	PutResponse(ctx context.Context, variableKey string, body *VariablePostBody) (*resty.Response, error)
}

var _ VariablesClient = (*variablesClient)(nil)

type XcomsClient interface {
	// Delete a single XCom Value
	Delete(ctx context.Context, dagId string, runId string, taskId string, key string, params *DeleteXcomParams) (*interface{}, error)
	// DeleteResponse is a lower level version of [Delete] and provides access to the raw [resty.Response]
	DeleteResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *DeleteXcomParams) (*resty.Response, error)

	// Get a single XCom Value
	Get(ctx context.Context, dagId string, runId string, taskId string, key string, params *GetXcomParams) (*XComResponse, error)
	// GetResponse is a lower level version of [Get] and provides access to the raw [resty.Response]
	GetResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *GetXcomParams) (*resty.Response, error)

	// Returns the count of mapped XCom values found in the `Content-Range` response header
	Head(ctx context.Context, dagId string, runId string, taskId string, key string, params *HeadXcomParams) (*interface{}, error)
	// HeadResponse is a lower level version of [Head] and provides access to the raw [resty.Response]
	HeadResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *HeadXcomParams) (*resty.Response, error)

	// Set an Airflow XCom.
	Set(ctx context.Context, dagId string, runId string, taskId string, key string, params *SetXcomParams, body *JsonValue) (*interface{}, error)
	// SetResponse is a lower level version of [Set] and provides access to the raw [resty.Response]
	SetResponse(ctx context.Context, dagId string, runId string, taskId string, key string, params *SetXcomParams, body *JsonValue) (*resty.Response, error)
}

var _ XcomsClient = (*xcomsClient)(nil)
