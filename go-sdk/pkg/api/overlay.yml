# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
---
# yamllint disable rule:line-length
overlay: 1.0.0
info:
  title: OpenAPI Overlay
  version: 0.0.0
actions:
  - description: |-
      Remove Airflow-API-Version header

      Since the version is consistent across the entire client, we don't want it in the request objects
    target: $.paths.*.*.parameters[?(@.name == "Airflow-API-Version")]
    remove: true

  # Customize the operation IDs so it produced nice function names
  - target: $.paths['/dag-runs/count']
    update:
      operationId: get_dag_run_count
  - target: $.paths['/dag-runs/{dag_id}/{run_id}/state'].get
    update:
      operationId: get_dag_run_state
  - target: $.paths['/task-instances/{task_instance_id}/heartbeat'].put
    update:
      operationId: task_instance_heartbeat
  - target: $.paths['/task-instances/{task_instance_id}/rtif'].put
    update:
      operationId: task_instance_put_rendered_fields
  - target: $.paths['/task-instances/{task_instance_id}/run'].patch
    update:
      operationId: task_instance_run
  - target: $.paths['/task-instances/{task_instance_id}/state'].patch
    update:
      operationId: task_instance_update_state
  - target: $.paths['/task-instances/{task_instance_id}/skip-downstream'].patch
    update:
      operationId: task_instance_skip_downstream

  - target: $.paths['/task-instances/{task_instance_id}/state'].patch.requestBody.content['application/json'].schema
    description: Override UpdateState body to not be an embedded type
    update:
      x-go-type: TIUpdateStatePayload
  - target: $.components.schemas
    update:
      TIUpdateStatePayload:
        oneOf:
          - $ref: "#/components/schemas/TITerminalStatePayload"
          - $ref: "#/components/schemas/TISuccessStatePayload"
          - $ref: "#/components/schemas/TITargetStatePayload"
          - $ref: "#/components/schemas/TIDeferredStatePayload"
          - $ref: "#/components/schemas/TIRescheduleStatePayload"
          - $ref: "#/components/schemas/TIRetryStatePayload"
        title: "TI UpdateState Payload"
