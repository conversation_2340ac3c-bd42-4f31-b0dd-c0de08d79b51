diff --git a//dev/null b/dev/UI_PLUGIN_SUPPORT_PLAN.md
index **********..d6f27c7d97 100644
--- a//dev/null
+++ b/dev/UI_PLUGIN_SUPPORT_PLAN.md
@@ -0,0 +1,242 @@
+<!--
+ Licensed to the Apache Software Foundation (ASF) under one
+ or more contributor license agreements.  See the NOTICE file
+ distributed with this work for additional information
+ regarding copyright ownership.  The ASF licenses this file
+ to you under the Apache License, Version 2.0 (the
+ "License"); you may not use this file except in compliance
+ with the License.  You may obtain a copy of the License at
+
+   http://www.apache.org/licenses/LICENSE-2.0
+
+ Unless required by applicable law or agreed to in writing,
+ software distributed under the License is distributed on an
+ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
+ KIND, either express or implied.  See the License for the
+ specific language governing permissions and limitations
+ under the License.
+-->
+
+# Airflow 3 – Universal UI Plugin Support Plan
+
+> **Scope** Enable third-party plugins to add **menu items + pages** to the new React (Vite + Chakra) UI without any Flask / Flask-AppBuilder (FAB) dependency.
+
+<!-- START doctoc generated TOC please keep comment here to allow auto update -->
+<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->
+**Table of contents**
+
+- [1  Design Goals](#1--design-goals)
+- [2  Backend ( FastAPI ) Changes](#2--backend--fastapi--changes)
+  - [2.1  Data Model – extend `AirflowPlugin`](#21--data-model--extend-airflowplugin)
+  - [2.2  Discovery endpoint (core)](#22--discovery-endpoint-core)
+  - [2.3  Static asset serving helpers](#23--static-asset-serving-helpers)
+- [3  Front-end Changes (React + Vite)](#3--front-end-changes-react--vite)
+  - [3.1  Registry & Fetch](#31--registry--fetch)
+  - [3.2  Sidebar injection (`PluginMenus.tsx`)](#32--sidebar-injection-pluginmenustsx)
+  - [3.3  Router](#33--router)
+  - [3.4  RBAC filter](#34--rbac-filter)
+- [4  Plugin Author Guide](#4--plugin-author-guide)
+  - [4.1  Scaffold a plugin package](#41--scaffold-a-plugin-package)
+  - [4.2  Python side – register FastAPI + UI metadata](#42--python-side--register-fastapi--ui-metadata)
+  - [4.3  FastAPI app – serve built assets](#43--fastapi-app--serve-built-assets)
+  - [4.4  Frontend – reuse Airflow theme & nav stays intact](#44--frontend--reuse-airflow-theme--nav-stays-intact)
+  - [4.5  Build & install](#45--build--install)
+- [5  Phase-by-Phase Implementation Plan](#5--phase-by-phase-implementation-plan)
+- [6  Security & Auth](#6--security--auth)
+- [7  Migration Checklist](#7--migration-checklist)
+- [8  Open Questions](#8--open-questions)
+- [9  Future Enhancements](#9--future-enhancements)
+
+<!-- END doctoc generated TOC please keep comment here to allow auto update -->
+
+## 1  Design Goals
+1. Side-bar nav must show plugin-supplied entries.
+2. Clicking an entry renders the plugin page **inside** the existing `BaseLayout`; nav bar always visible.
+3. Plugin registration should be declarative and require **no core-code patch** when a new wheel/egg is dropped into `plugins/`.
+4. RBAC must apply – nav entry shown only when the current user is allowed.
+5. Zero Flask/FAB assumptions – **ignore `appbuilder_menu_items`**.
+
+## 2  Backend ( FastAPI ) Changes
+### 2.1  Data Model – extend `AirflowPlugin`
+```python
+class AirflowPlugin:
+    # existing fields …
+    ui = [
+        {
+            "slug": "my_plugin",        # url segment + primary key
+            "label": "My Plugin",       # text in sidebar
+            "icon": "FiZap",           # react-icons name (optional)
+            "entry": "/my_plugin/ui",  # absolute url served by plugin
+            "type": "iframe",          # "iframe" | "module" (module federation)
+            "permissions": ["my_plugin.view"],
+        },
+        # …more pages
+    ]
+```
+*Each* dict describes **one** page. A plugin may expose many pages if desired.
+
+### 2.2  Discovery endpoint (core)
+```
+GET /api/v1/ui_plugins  →  [ {…same fields as above…} ]
+```
+Implemented during plugin initialisation:
+1. Iterate all loaded plugins.
+2. Read `plugin.ui` list; validate required keys.
+3. Attach plugin name for traceability.
+4. Store in global registry → serve via new FastAPI router under `airflow.api.ui_plugins`.
+
+### 2.3  Static asset serving helpers
+Plugin author responsibilities:
+```python
+from fastapi.staticfiles import StaticFiles
+from pathlib import Path
+
+app = FastAPI(title="My Plugin")
+ui_dist = Path(__file__).parent / "ui" / "dist"
+app.mount("/ui", StaticFiles(directory=ui_dist, html=True), name="my_plugin_ui")
+```
+Only **fastapi_apps** (already supported) are needed; no Flask.
+
+## 3  Front-end Changes (React + Vite)
+### 3.1  Registry & Fetch
+```ts
+const { data: plugins } = useQuery("uiPlugins", () => api.get("/api/v1/ui_plugins").then(r=>r.data));
+```
+Create `PluginRegistryContext` to hold the list.
+
+### 3.2  Sidebar injection (`PluginMenus.tsx`)
+Replace hard-coded FAB menus:
+```tsx
+return (
+  <>
+    {plugins.map(p => (
+      <NavButton icon={iconMap[p.icon]} title={p.label} to={`/plugins/${p.slug}`} key={p.slug} />
+    ))}
+  </>
+);
+```
+### 3.3  Router
+```tsx
+<Route path="plugins/:slug/*" element={<PluginHost />} />
+```
+`PluginHost` resolves slug → registry entry → renders:
+```tsx
+if (p.type === "iframe") return <Iframe src={p.entry} />;
+const Remote = React.lazy(() => import(/* @vite-ignore */ p.entry));
+return <Suspense><Remote /></Suspense>;
+```
+### 3.4  RBAC filter
+Hide nav items when `!authorized_menu_items.includes(permission)`.
+
+## 4  Plugin Author Guide
+
+### 4.1  Scaffold a plugin package
+```bash
+cookiecutter gh:apache/cookiecutter-airflow-ui-plugin
+# → answer prompts, e.g. plugin_slug=my_plugin
+```
+This yields:
+```
+my_plugin/
+├─ plugins/my_plugin.py          # AirflowPlugin subclass
+├─ api/main.py                  # FastAPI app
+└─ ui/                           # Vite-React project (Chakra)
+```
+
+### 4.2  Python side – register FastAPI + UI metadata
+```python
+# plugins/my_plugin.py
+from airflow.plugins_manager import AirflowPlugin
+from ..api.main import app as fastapi_app
+
+class MyPlugin(AirflowPlugin):
+    name = "my_plugin"
+    fastapi_apps = [
+        {"app": fastapi_app, "url_prefix": "/my_plugin", "name": "my_plugin_api"},
+    ]
+    ui = [
+        {
+            "slug": "my_plugin",          # /plugins/my_plugin
+            "label": "My Plugin",
+            "icon": "FiZap",              # react-icons name
+            "entry": "/my_plugin/ui",    # served below
+            "type": "iframe",            # or "module" for MF
+            "permissions": ["my_plugin.view"],
+        }
+    ]
+```
+
+### 4.3  FastAPI app – serve built assets
+```python
+# api/main.py
+from fastapi import FastAPI
+from fastapi.staticfiles import StaticFiles
+from pathlib import Path
+
+app = FastAPI(title="My Plugin")
+ui_dist = Path(__file__).parents[1] / "ui" / "dist"
+app.mount("/ui", StaticFiles(directory=ui_dist, html=True), name="ui")
+```
+
+### 4.4  Frontend – reuse Airflow theme & nav stays intact
+Inside `ui/src/main.tsx`:
+```tsx
+import { ChakraProvider, extendTheme } from "@chakra-ui/react";
+import airflowTheme from "@apache/airflow-ui-theme"; // helper package the core ships
+import App from "./App";
+
+const theme = extendTheme(airflowTheme);
+root.render(
+  <ChakraProvider theme={theme}>
+    <App />
+  </ChakraProvider>
+);
+```
+Because the page is rendered inside `BaseLayout`, the left sidebar and header remain visible—you only build the main content.
+
+### 4.5  Build & install
+```bash
+cd ui && npm install && npm run build   # emits ui/dist
+cd .. && python -m build                # wheel includes ui/dist
+pip install dist/my_plugin-*.whl        # add to Airflow environment
+```
+Restart webserver → sidebar shows “My Plugin”; click it to see your page with native Airflow look-and-feel.
+
+## 5  Phase-by-Phase Implementation Plan
+
+| Phase | Scope | Key Outputs |
+|-------|-------|-------------|
+| 0 – Alignment | Finalise design, agree on `ui` schema & endpoint name. | Approved spec and this doc signed off. |
+| 1 – Backend Model | Add `ui` field to `AirflowPlugin` dataclass; validation helpers. | Patched `plugins_manager.py`. |
+| 2 – Discovery API | Implement `/api/v1/ui_plugins` (FastAPI router + Pydantic model). | JSON list available in Swagger. |
+| 3 – Static Mount Helper | Provide utility to mount `ui/dist` via `StaticFiles`. | Example plugin works in dev. |
+| 4 – Front-end Registry | React-Query fetch + `PluginRegistryContext`. | DevTools shows list in cache. |
+| 5 – Sidebar & RBAC | Replace `PluginMenus.tsx` contents, filter by permissions. | Sidebar shows/hides entries correctly. |
+| 6 – Router & Host | Add `/plugins/:slug/*` route, implement `PluginHost` (iframe first). | Plugin page renders inside layout. |
+| 7 – Tooling & Docs | Publish cookiecutter, update docs, sample plugin repo. | Developers can scaffold in <1 min. |
+| 8 – Security Pass | Add permission creation, tests, security review. | CI passes RBAC tests. |
+| 9 – Module Federation | Optional: enable `type="module"` with shared React. | POC micro-frontend plugin. |
+|10 – DX Extras | Hot-reload proxy, settings-menu hook, logo/icon guidelines. | Nice-to-have enhancements. |
+
+## 6  Security & Auth
+* Use standard Airflow session cookies.
+* Each `ui[].permissions` string is added at plugin-load time; admin assigns roles in usual way.
+
+## 7  Migration Checklist
+| Area | Task |
+|------|------|
+| Core API | add `UiPlugin` Pydantic model & `/api/v1/ui_plugins` route |
+| Plugin Manager | collect `plugin.ui` during `initialize_plugins()` |
+| React UI | create registry fetch, update `PluginMenus`, add `PluginHost` |
+| Docs | refresh old docs – remove FAB mentions |
+| Tooling | publish cookiecutter template |
+
+## 8  Open Questions
+* Icon naming convention – restrict to `react-icons/fi`? custom SVG?
+* Version collisions – share React via Module Federation or iFrame isolation.
+* Dark-mode CSS isolation for iframe strategy.
+
+## 9  Future Enhancements
+* Hot-reload during development (`/static/dev/<slug>` proxy).
+* "Settings" injection so plugins can add items under the user dropdown.
+* Embeddable widgets inside core pages (beyond full-page route).
