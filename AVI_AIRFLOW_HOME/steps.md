```
export AIRFLOW_HOME=/Users/<USER>/CODES/my_airflow/AVI_AIRFLOW_HOME
export AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@localhost:5432/airflow
export AIRFLOW__SCHEDULER__STANDALONE_DAG_PROCESSOR=True
export AIRFLOW__CORE__LOAD_EXAMPLES=False
export AIRFLOW__WEBSERVER__EXPOSE_CONFIG=True
export AIRFLOW__CORE__SIMPLE_AUTH_MANAGER_PASSWORDS_FILE=/Users/<USER>/CODES/my_airflow/AVI_AIRFLOW_HOME/passwds.json
export AIRFLOW__CORE__SIMPLE_AUTH_MANAGER_ALL_ADMINS=True

export AIRFLOW__COMMON_IO__XCOM_OBJECTSTORAGE_PATH=s3://my_aws_conn@xcom-backend-test/xcom	
export AIRFLOW__COMMON_IO__XCOM_OBJECTSTORAGE_THRESHOLD=0
export AIRFLOW__CORE__XCOM_BACKEND=airflow.providers.common.io.xcom.backend.XComObjectStorageBackend
export DEV_MODE=true
```

```
airflow db migrate
airflow users create --username admin --firstname admin --lastname admin --role Admin --email <EMAIL> --password admin
```

```bash
uv pip install --find-links \
https://dist.apache.org/repos/dist/dev/airflow/3.0.0b1/ \
apache-airflow==3.0.0b1 \
apache-airflow-task-sdk==1.0.0b1 \
apache-airflow-providers-standard==1.0.0b1 \
apache-airflow-providers-fab==2.0.0b1 \
apache-airflow-providers-celery==3.11.0b1 \
apache-airflow-providers-cncf-kubernetes==10.4.0b1 \
apache-airflow-providers-postgres==6.1.0
```


# Compile the frontend asssets
```
cd airflow/wwww
yarn install
yarn run build
```

# Build Docs

```
pip install pipx
pipx ensurepath
pipx install -e ./dev/breeze --force
breeze setup self-upgrade
breeze release-management prepare-provider-packages microsoft azure apache.hive --skip-tag-check
breeze build-docs
breeze docs -- serve
```

To view the built documentation, you have two options:
1. Start the webserver in breeze and access the built docs at http://localhost:28080/docs/
2. Alternatively, you can run ./docs/start_doc_server.sh for a lighter resource option and view the built docs at http://localhost:8000


# Grep clean logs
```
grep -v -e 'serde.py' -e 'providers_manager.py' -e 'plugins_manager.py' -e 'listener.py' -e 'settings.py' -e 'configuration.py'  
```