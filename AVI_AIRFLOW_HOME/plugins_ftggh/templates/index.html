<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>DAG Explorer — Galactic Mode</title>

  <!-- TailwindCSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- React & ReactDOM -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

  <!-- Babel for JSX transpilation -->
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body class="bg-gradient-to-r from-gray-900 via-indigo-900 to-purple-900 min-h-screen flex flex-col">
  <!-- Header -->
  <header class="flex justify-between items-center p-4 bg-black bg-opacity-50 backdrop-blur-md shadow-md">
    <h1 class="text-2xl font-bold text-indigo-300">🚀 DAG Explorer</h1>
    <input
      type="text"
      id="filterInput"
      placeholder="Search DAGs..."
      class="px-4 py-2 rounded-full bg-gray-800 text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition w-64"
    />
  </header>

  <!-- Main Grid -->
  <main class="flex-1 overflow-auto">
    <div id="root" class="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"></div>
  </main>

  <!-- React App -->
  <script type="text/babel" data-presets="react">
    const { useState, useEffect, useMemo } = React;

    function App() {
      const [dags, setDags] = useState([]);
      const [filter, setFilter] = useState("");
      const [loading, setLoading] = useState(true);
      const [token, setToken] = useState(null);
      const [aiLoading, setAiLoading] = useState(false);
      const [promptModal, setPromptModal] = useState({ visible: false, dagId: null });
      const [promptInput, setPromptInput] = useState("");
      const [aiSummary, setAiSummary] = useState("");
      const [modalDagId, setModalDagId] = useState(null);
      const [headersAuth, setHeadersAuth] = useState({});
      const [healthMetrics, setHealthMetrics] = useState({});
      const [triggeringId, setTriggeringId] = useState(null);
      const [runConfirmation, setRunConfirmation] = useState(null);

      // Fetch token
      useEffect(() => {
        fetch('/auth/token', { method: 'POST', headers: {'Content-Type':'application/json'}, body: JSON.stringify({username:'admin',password:'admin'}) })
          .then(r => r.json())
          .then(d => {
            setToken(d.access_token);
            setHeadersAuth({ Authorization: `Bearer ${d.access_token}` });
          });
      }, []);

      // Fetch DAGs
      useEffect(() => {
        if (!token) return;
        setLoading(true);
        fetch(`/api/v2/dags?limit=1000&order_by=dag_id`, { headers: {...headersAuth, Accept:'application/json'} })
          .then(r => r.json())
          .then(d => setDags(d.dags.map(x => x.dag_id)))
          .finally(() => setLoading(false));
      }, [token]);

      // Search filter
      useEffect(() => {
        const inp = document.getElementById('filterInput');
        const handler = e => setFilter(e.target.value.toLowerCase());
        inp.addEventListener('input', handler);
        return () => inp.removeEventListener('input', handler);
      }, []);

      const filtered = useMemo(() => dags.filter(id => id.toLowerCase().includes(filter)), [dags, filter]);

      // Fetch health metrics
      useEffect(() => {
        if (!token) return;
        filtered.forEach(dagId => {
          if (healthMetrics[dagId]) return;
          Promise.all([
            fetch(`/api/v2/dags/${dagId}/dagRuns?state=success&limit=1`, { headers: headersAuth }).then(r => r.json()).then(d => d.total_entries || 0),
            fetch(`/api/v2/dags/${dagId}/dagRuns?state=running&limit=1`, { headers: headersAuth }).then(r => r.json()).then(d => d.total_entries || 0),
            fetch(`/api/v2/dags/${dagId}/dagRuns?state=failed&limit=1`, { headers: headersAuth }).then(r => r.json()).then(d => d.total_entries || 0),
            fetch(`/api/v2/dags/${dagId}/dagRuns?limit=1`, { headers: headersAuth }).then(r => r.json()).then(d => d.dag_runs?.[0]?.dag_run_id)
          ])
          .then(([sRun, rRun, fRun, latest]) => {
            const other = 0;
            if (!latest) {
              setHealthMetrics(h => ({ ...h, [dagId]: { sRun, rRun, fRun, other, st:0, rt:0, ft:0, sk:0 } }));
              return;
            }
            fetch(`/api/v2/dags/${dagId}/dagRuns/${latest}/taskInstances`, { headers: headersAuth })
              .then(r => r.json()).then(d => {
                const t = d.task_instances || [];
                const st = t.filter(x => x.state === 'success').length;
                const rt = t.filter(x => x.state === 'running').length;
                const ft = t.filter(x => x.state === 'failed').length;
                const sk = t.filter(x => x.state === 'skipped').length;
                setHealthMetrics(h => ({ ...h, [dagId]: { sRun, rRun, fRun, other, st, rt, ft, sk } }));
              })
              .catch(console.error);
          })
          .catch(console.error);
        });
      }, [filtered, token, healthMetrics]);

      // Trigger DAG
      const triggerDag = async(dagId) => {
        setTriggeringId(dagId);
        try {
          const runId = `manual__${new Date().toISOString()}`;
          const logicalDate = new Date().toISOString();
          const res = await fetch(`/api/v2/dags/${dagId}/dagRuns`, {
            method: 'POST', headers: { ...headersAuth, 'Content-Type':'application/json' },
            body: JSON.stringify({ dag_run_id: runId, logical_date: logicalDate, conf: {} })
          });
          const data = await res.json();
          if (!res.ok) throw new Error(data.detail || JSON.stringify(data));
          setRunConfirmation({ dagId, runId: data.dag_run_id });
        } catch(e) {
          alert(`Trigger failed: ${e.message}`);
        } finally {
          setTriggeringId(null);
        }
      };

      // Ask AI
      const askAi = async(dagId, question) => {
        setAiLoading(true);
        try {
          const res = await fetch(`/dag-list/ai/describe/${dagId}?user_prompt=${encodeURIComponent(question)}`, { headers: headersAuth });
          const d = await res.json();
          if (!res.ok) throw new Error(JSON.stringify(d));
          setAiSummary(d.answer || d.summary);
          setModalDagId(dagId);
        } catch(e) {
          alert(`AI error: ${e.message}`);
        } finally {
          setAiLoading(false);
          setPromptModal({ visible: false, dagId: null });
          setPromptInput("");
        }
      };

      if (loading) {
        return <div className="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => <div key={i} className="animate-pulse bg-gray-700 h-40 rounded-2xl"></div>)}
        </div>;
      }

      return (
        <>
        {filtered.map(id => (
            <div key={id} className="bg-gray-800 p-6 rounded-2xl shadow-lg flex flex-col justify-between">
              <h3 className="text-lg text-indigo-200 truncate">{id}</h3>
              {/* Health Bars */}
              <div className="mt-4 space-y-3">
                <div>
                  <span className="text-xs text-white">
                    Runs: 
                    <span className="text-green-400 font-medium">{healthMetrics[id]?.sRun||0} Success</span> / 
                    <span className="text-green-200 font-medium">{healthMetrics[id]?.rRun||0} Running</span> / 
                    <span className="text-red-500 font-medium">{healthMetrics[id]?.fRun||0} Failed</span> / 
                    <span className="text-gray-500 font-medium">{healthMetrics[id]?.other||0} Queued</span>
                  </span>
                  <div className="w-full bg-gray-700 rounded-full h-2 flex overflow-hidden mt-1">
                    <div className="bg-green-800 h-full" style={{ width:`${((healthMetrics[id]?.sRun||0)/(healthMetrics[id]?.sRun+healthMetrics[id]?.rRun+healthMetrics[id]?.fRun+healthMetrics[id]?.other||1))*100}%` }}/>
                    <div className="bg-green-400 h-full" style={{ width:`${((healthMetrics[id]?.rRun||0)/(healthMetrics[id]?.sRun+healthMetrics[id]?.rRun+healthMetrics[id]?.fRun+healthMetrics[id]?.other||1))*100}%` }}/>
                    <div className="bg-red-500 h-full"   style={{ width:`${((healthMetrics[id]?.fRun||0)/(healthMetrics[id]?.sRun+healthMetrics[id]?.rRun+healthMetrics[id]?.fRun+healthMetrics[id]?.other||1))*100}%` }}/>
                    <div className="bg-gray-500 h-full" style={{ width:`${((healthMetrics[id]?.other||0)/(healthMetrics[id]?.sRun+healthMetrics[id]?.rRun+healthMetrics[id]?.fRun+healthMetrics[id]?.other||1))*100}%` }}/>
                  </div>
                </div>
                <div>
                  <span className="text-xs text-white">
                    Tasks: 
                    <span className="text-green-400 font-medium">{healthMetrics[id]?.st||0} Success</span> / 
                    <span className="text-green-200 font-medium">{healthMetrics[id]?.rt||0} Running</span> / 
                    <span className="text-red-500 font-medium">{healthMetrics[id]?.ft||0} Failed</span> / 
                    <span className="text-pink-500 font-medium">{healthMetrics[id]?.sk||0} Skipped</span>
                  </span>
                  <div className="w-full bg-gray-700 rounded-full h-2 flex overflow-hidden mt-1">
                    <div className="bg-green-800 h-full" style={{ width:`${((healthMetrics[id]?.st||0)/(healthMetrics[id]?.st+healthMetrics[id]?.rt+healthMetrics[id]?.ft+healthMetrics[id]?.sk||1))*100}%` }}/>
                    <div className="bg-green-400 h-full" style={{ width:`${((healthMetrics[id]?.rt||0)/(healthMetrics[id]?.st+healthMetrics[id]?.rt+healthMetrics[id]?.ft+healthMetrics[id]?.sk||1))*100}%` }}/>
                    <div className="bg-red-500 h-full"  style={{ width:`${((healthMetrics[id]?.ft||0)/(healthMetrics[id]?.st+healthMetrics[id]?.rt+healthMetrics[id]?.ft+healthMetrics[id]?.sk||1))*100}%` }}/>
                    <div className="bg-pink-500 h-full" style={{ width:`${((healthMetrics[id]?.sk||0)/(healthMetrics[id]?.st+healthMetrics[id]?.rt+healthMetrics[id]?.ft+healthMetrics[id]?.sk||1))*100}%` }}/>
                  </div>
                </div>
              </div>
              <div className="mt-4 flex space-x-2">
                <button onClick={() => triggerDag(id)} disabled={triggeringId===id} className="flex-1 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50">
                  {triggeringId===id ? 'Running…' : 'Run'}
                </button>
                <button onClick={() => window.location.href=`/dags/${id}`} className="flex-1 px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                  Open
                </button>
                <button onClick={() => setPromptModal({ visible: true, dagId: id })} className="flex-1 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                  Ask AI
                </button>
              </div>
            </div>
          ))}

          {/* Prompt Modal */}
          {promptModal.visible && (
            <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50">
              {aiLoading ? (
                <div className="bg-gray-800 p-8 rounded-xl shadow-lg flex flex-col items-center">
                  <svg className="animate-spin h-10 w-10 text-indigo-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                  </svg>
                  <p className="mt-4 text-white text-lg">Processing your question...</p>
                </div>
              ) : (
                <div className="bg-gray-800 p-6 rounded-xl shadow-lg w-full max-w-md">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl text-white">Ask AI about <span className="font-semibold">{promptModal.dagId}</span></h3>
                    <button onClick={() => setPromptModal({ visible: false, dagId: null })} className="text-gray-400 hover:text-gray-200 text-2xl">&times;</button>
                  </div>
                  <textarea
                    className="w-full p-3 mb-4 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 transition"
                    rows={4}
                    value={promptInput}
                    onChange={e => setPromptInput(e.target.value)}
                    placeholder="Type your question here"
                  />
                  <div className="flex justify-end space-x-2">
                    <button disabled={!promptInput} onClick={() => askAi(promptModal.dagId, promptInput)} className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 transition">
                      Submit
                    </button>
                    <button onClick={() => setPromptModal({ visible: false, dagId: null })} className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition">
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

                    {/* Run Confirmation Modal */}
          {runConfirmation && (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4">
              <div className="bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full relative">
                <h3 className="text-xl text-indigo-200 mb-4">DAG '{runConfirmation.dagId}' triggered</h3>
                <p className="text-gray-200 mb-6">Do you want to view the triggered DAG run (ID: {runConfirmation.runId})?</p>
                <div className="flex justify-end space-x-4">
                  <button
                    onClick={() => {
                      window.location.href = `/dags/${runConfirmation.dagId}/runs/${runConfirmation.runId}/`;
                      setRunConfirmation(null);
                    }}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg"
                  >
                    Yes
                  </button>
                  <button
                    onClick={() => setRunConfirmation(null)}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
                  >
                    No
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* AI Summary Modal */}
          {modalDagId && (
            <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4">
              <div className="bg-gray-800 p-6 rounded-lg max-w-lg w-full relative">
                <button onClick={() => setModalDagId(null)} className="absolute top-2 right-2 text-gray-400 hover:text-gray-200">&times;</button>
                <h3 className="text-xl text-indigo-200 mb-4">AI Response for {modalDagId}</h3>
                <div className="bg-gray-900 p-4 rounded-lg max-h-96 overflow-auto text-white whitespace-pre-wrap">
                  {aiSummary}
                </div>
                <div className="mt-4 flex justify-end">
                  <button onClick={() => setModalDagId(null)} className="px-4 py-2 bg-indigo-600 rounded text-white">Close</button>
                </div>
              </div>
            </div>
          )}
        </>
      );
    }

    ReactDOM.createRoot(document.getElementById("root")).render(<App />);
  </script>
</body>
</html>
