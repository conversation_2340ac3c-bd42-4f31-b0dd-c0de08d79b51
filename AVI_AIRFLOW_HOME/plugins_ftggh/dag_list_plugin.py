import os
import re
import json
import logging
import requests

from fastapi import FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse

from airflow.plugins_manager import AirflowPlugin

import openai
from openai import OpenAI
from datetime import datetime

# ——— Logger ———
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# ——— OpenAI Client Setup ———
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ai_client = OpenAI(api_key=OPENAI_API_KEY) if OPENAI_API_KEY else None
if not ai_client:
    logger.warning("OPENAI_API_KEY not set; AI endpoints unavailable")

# ——— FastAPI Sub-App ———
app = FastAPI(title="DAG Explorer Plugin")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True,
)

# ——— Load and dereference OpenAPI spec for dynamic mapping ———
import jsonref

BASE_URL = os.getenv("AIRFLOW__WEBSERVER__BASE_URL", "http://localhost:8080")
SPEC_PATHS = {}
OPENAPI_PATHS = "{}"

# ——— Serve UI ———
@app.get("/", response_class=FileResponse)
def serve_index():
    return FileResponse(
        os.path.join(os.path.dirname(__file__), "templates/index.html"),
        media_type="text/html"
    )

# ——— Helpers ———
def default_for_schema(schema: dict):
    t = schema.get('type')
    f = schema.get('format')
    if f == 'date-time':
        return datetime.utcnow().isoformat() + 'Z'
    if t == 'string':
        return ''
    if t == 'integer':
        return 0
    if t == 'boolean':
        return False
    if t == 'object':
        return {}
    if t == 'array':
        return []
    return None

# ——— Dynamic AI-driven REST mapping ———
@app.get("/ai/describe/{dag_id}")
async def dynamic_describe(
    dag_id: str,
    request: Request,
    user_prompt: str = Query(None, description="Natural language query")
):
    if not ai_client:
        raise HTTPException(503, "AI integration unavailable")
    if not user_prompt:
        raise HTTPException(400, "Please provide user_prompt parameter")
    # print(OPENAPI_PATHS)
    OPENAPI_PATHS="""{
    "openapi": "3.1.0",
    "info": {
        "title": "Airflow API",
        "description": "Airflow API. All endpoints located under ``/api/v2`` can be used safely, are stable and backward compatible. Endpoints located under ``/ui`` are dedicated to the UI and are subject to breaking change depending on the need of the frontend. Users should not rely on those but use the public ones instead.",
        "version": "2"
    },
    "paths": {
        "/api/v2/dags/{dag_id}/dagRuns/{dag_run_id}": {
        "get": {
            "tags": [
            "DagRun"
            ],
            "summary": "Get Dag Run",
            "operationId": "get_dag_run",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGRunResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "delete": {
            "tags": [
            "DagRun"
            ],
            "summary": "Delete Dag Run",
            "description": "Delete a DAG Run entry.",
            "operationId": "delete_dag_run",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            }
            ],
            "responses": {
            "204": {
                "description": "Successful Response"
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "patch": {
            "tags": [
            "DagRun"
            ],
            "summary": "Patch Dag Run",
            "description": "Modify a DAG Run.",
            "operationId": "patch_dag_run",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            },
            {
                "name": "update_mask",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Update Mask"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/DAGRunPatchBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGRunResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/dagRuns": {
        "get": {
            "tags": [
            "DagRun"
            ],
            "summary": "Get Dag Runs",
            "description": "Get all DAG Runs.\n\nThis endpoint allows specifying `~` as the dag_id to retrieve Dag Runs for all DAGs.",
            "operationId": "get_dag_runs",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "limit",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 50,
                "title": "Limit"
                }
            },
            {
                "name": "offset",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 0,
                "title": "Offset"
                }
            },
            {
                "name": "run_after_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Run After Gte"
                }
            },
            {
                "name": "run_after_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Run After Lte"
                }
            },
            {
                "name": "logical_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Logical Date Gte"
                }
            },
            {
                "name": "logical_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Logical Date Lte"
                }
            },
            {
                "name": "start_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Start Date Gte"
                }
            },
            {
                "name": "start_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Start Date Lte"
                }
            },
            {
                "name": "end_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "End Date Gte"
                }
            },
            {
                "name": "end_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "End Date Lte"
                }
            },
            {
                "name": "updated_at_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Updated At Gte"
                }
            },
            {
                "name": "updated_at_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Updated At Lte"
                }
            },
            {
                "name": "run_type",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Run Type"
                }
            },
            {
                "name": "state",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "State"
                }
            },
            {
                "name": "order_by",
                "in": "query",
                "required": False,
                "schema": {
                "type": "string",
                "default": "id",
                "title": "Order By"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGRunCollectionResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "post": {
            "tags": [
            "DagRun"
            ],
            "summary": "Trigger Dag Run",
            "description": "Trigger a DAG.",
            "operationId": "trigger_dag_run",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "title": "Dag Id"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/TriggerDAGRunPostBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGRunResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "409": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Conflict"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/dagRuns/list": {
        "post": {
            "tags": [
            "DagRun"
            ],
            "summary": "Get List Dag Runs Batch",
            "description": "Get a list of DAG Runs.",
            "operationId": "get_list_dag_runs_batch",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "const": "~",
                "type": "string",
                "title": "Dag Id"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/DAGRunsBatchBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGRunCollectionResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dagSources/{dag_id}": {
        "get": {
            "tags": [
            "DagSource"
            ],
            "summary": "Get Dag Source",
            "description": "Get source code using file token.",
            "operationId": "get_dag_source",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "version_number",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "integer"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Version Number"
                }
            },
            {
                "name": "accept",
                "in": "header",
                "required": False,
                "schema": {
                "type": "string",
                "enum": [
                    "application/json",
                    "text/plain",
                    "*/*"
                ],
                "default": "*/*",
                "title": "Accept"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGSourceResponse"
                    }
                },
                "text/plain": {
                    "schema": {
                    "type": "string",
                    "example": "dag code"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "406": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Acceptable"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dagStats": {
        "get": {
            "tags": [
            "DagStats"
            ],
            "summary": "Get Dag Stats",
            "description": "Get Dag statistics.",
            "operationId": "get_dag_stats",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_ids",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Dag Ids"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DagStatsCollectionResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dagReports": {
        "get": {
            "tags": [
            "DagReport"
            ],
            "summary": "Get Dag Reports",
            "description": "Get DAG report.",
            "operationId": "get_dag_reports",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "subdir",
                "in": "query",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Subdir"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {}
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags": {
        "get": {
            "tags": [
            "DAG"
            ],
            "summary": "Get Dags",
            "description": "Get all DAGs.",
            "operationId": "get_dags",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "limit",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 50,
                "title": "Limit"
                }
            },
            {
                "name": "offset",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 0,
                "title": "Offset"
                }
            },
            {
                "name": "tags",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Tags"
                }
            },
            {
                "name": "tags_match_mode",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "enum": [
                        "any",
                        "all"
                    ],
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Tags Match Mode"
                }
            },
            {
                "name": "owners",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Owners"
                }
            },
            {
                "name": "dag_id_pattern",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Id Pattern"
                }
            },
            {
                "name": "dag_display_name_pattern",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Display Name Pattern"
                }
            },
            {
                "name": "exclude_stale",
                "in": "query",
                "required": False,
                "schema": {
                "type": "boolean",
                "default": True,
                "title": "Exclude Stale"
                }
            },
            {
                "name": "paused",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "boolean"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Paused"
                }
            },
            {
                "name": "last_dag_run_state",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "$ref": "#/components/schemas/DagRunState"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Last Dag Run State"
                }
            },
            {
                "name": "dag_run_start_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Run Start Date Gte"
                }
            },
            {
                "name": "dag_run_start_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Run Start Date Lte"
                }
            },
            {
                "name": "dag_run_end_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Run End Date Gte"
                }
            },
            {
                "name": "dag_run_end_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Run End Date Lte"
                }
            },
            {
                "name": "dag_run_state",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Dag Run State"
                }
            },
            {
                "name": "order_by",
                "in": "query",
                "required": False,
                "schema": {
                "type": "string",
                "default": "dag_id",
                "title": "Order By"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGCollectionResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "patch": {
            "tags": [
            "DAG"
            ],
            "summary": "Patch Dags",
            "description": "Patch multiple DAGs.",
            "operationId": "patch_dags",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "update_mask",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Update Mask"
                }
            },
            {
                "name": "limit",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 50,
                "title": "Limit"
                }
            },
            {
                "name": "offset",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 0,
                "title": "Offset"
                }
            },
            {
                "name": "tags",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Tags"
                }
            },
            {
                "name": "tags_match_mode",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "enum": [
                        "any",
                        "all"
                    ],
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Tags Match Mode"
                }
            },
            {
                "name": "owners",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Owners"
                }
            },
            {
                "name": "dag_id_pattern",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Dag Id Pattern"
                }
            },
            {
                "name": "exclude_stale",
                "in": "query",
                "required": False,
                "schema": {
                "type": "boolean",
                "default": True,
                "title": "Exclude Stale"
                }
            },
            {
                "name": "paused",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "boolean"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Paused"
                }
            },
            {
                "name": "last_dag_run_state",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "$ref": "#/components/schemas/DagRunState"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Last Dag Run State"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/DAGPatchBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGCollectionResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}": {
        "get": {
            "tags": [
            "DAG"
            ],
            "summary": "Get Dag",
            "description": "Get basic information about a DAG.",
            "operationId": "get_dag",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unprocessable Entity"
            }
            }
        },
        "patch": {
            "tags": [
            "DAG"
            ],
            "summary": "Patch Dag",
            "description": "Patch the specific DAG.",
            "operationId": "patch_dag",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "update_mask",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Update Mask"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/DAGPatchBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "delete": {
            "tags": [
            "DAG"
            ],
            "summary": "Delete Dag",
            "description": "Delete the specific DAG.",
            "operationId": "delete_dag",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {}
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unprocessable Entity"
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/details": {
        "get": {
            "tags": [
            "DAG"
            ],
            "summary": "Get Dag Details",
            "description": "Get details of DAG.",
            "operationId": "get_dag_details",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/DAGDetailsResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/{task_id}": {
        "get": {
            "tags": [
            "Task Instance"
            ],
            "summary": "Get Task Instance",
            "description": "Get task instance.",
            "operationId": "get_task_instance",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            },
            {
                "name": "task_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Task Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskInstanceResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        },
        "patch": {
            "tags": [
            "Task Instance"
            ],
            "summary": "Patch Task Instance",
            "description": "Update a task instance.",
            "operationId": "patch_task_instance",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            },
            {
                "name": "task_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Task Id"
                }
            },
            {
                "name": "map_index",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "default": -1,
                "title": "Map Index"
                }
            },
            {
                "name": "update_mask",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Update Mask"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/PatchTaskInstanceBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskInstanceResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "409": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Conflict"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances": {
        "get": {
            "tags": [
            "Task Instance"
            ],
            "summary": "Get Task Instances",
            "description": "Get list of task instances.\n\nThis endpoint allows specifying `~` as the dag_id, dag_run_id to retrieve Task Instances for all DAGs\nand DAG runs.",
            "operationId": "get_task_instances",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Run Id"
                }
            },
            {
                "name": "task_id",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Task Id"
                }
            },
            {
                "name": "run_after_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Run After Gte"
                }
            },
            {
                "name": "run_after_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Run After Lte"
                }
            },
            {
                "name": "logical_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Logical Date Gte"
                }
            },
            {
                "name": "logical_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Logical Date Lte"
                }
            },
            {
                "name": "start_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Start Date Gte"
                }
            },
            {
                "name": "start_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Start Date Lte"
                }
            },
            {
                "name": "end_date_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "End Date Gte"
                }
            },
            {
                "name": "end_date_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "End Date Lte"
                }
            },
            {
                "name": "updated_at_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Updated At Gte"
                }
            },
            {
                "name": "updated_at_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string",
                    "format": "date-time"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Updated At Lte"
                }
            },
            {
                "name": "duration_gte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "number"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Duration Gte"
                }
            },
            {
                "name": "duration_lte",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "number"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Duration Lte"
                }
            },
            {
                "name": "task_display_name_pattern",
                "in": "query",
                "required": False,
                "schema": {
                "anyOf": [
                    {
                    "type": "string"
                    },
                    {
                    "type": "null"
                    }
                ],
                "title": "Task Display Name Pattern"
                }
            },
            {
                "name": "state",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "State"
                }
            },
            {
                "name": "pool",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Pool"
                }
            },
            {
                "name": "queue",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Queue"
                }
            },
            {
                "name": "executor",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "string"
                },
                "title": "Executor"
                }
            },
            {
                "name": "version_number",
                "in": "query",
                "required": False,
                "schema": {
                "type": "array",
                "items": {
                    "type": "integer"
                },
                "title": "Version Number"
                }
            },
            {
                "name": "limit",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 50,
                "title": "Limit"
                }
            },
            {
                "name": "offset",
                "in": "query",
                "required": False,
                "schema": {
                "type": "integer",
                "minimum": 0,
                "default": 0,
                "title": "Offset"
                }
            },
            {
                "name": "order_by",
                "in": "query",
                "required": False,
                "schema": {
                "type": "string",
                "default": "map_index",
                "title": "Order By"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskInstanceCollectionResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/dagRuns/{dag_run_id}/taskInstances/list": {
        "post": {
            "tags": [
            "Task Instance"
            ],
            "summary": "Get Task Instances Batch",
            "description": "Get list of task instances.",
            "operationId": "get_task_instances_batch",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "const": "~",
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "dag_run_id",
                "in": "path",
                "required": True,
                "schema": {
                "const": "~",
                "type": "string",
                "title": "Dag Run Id"
                }
            }
            ],
            "requestBody": {
            "required": True,
            "content": {
                "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/TaskInstancesBatchBody"
                }
                }
            }
            },
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskInstanceCollectionResponse"
                    }
                }
                }
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/tasks": {
        "get": {
            "tags": [
            "Task"
            ],
            "summary": "Get Tasks",
            "description": "Get tasks for DAG.",
            "operationId": "get_tasks",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "order_by",
                "in": "query",
                "required": False,
                "schema": {
                "type": "string",
                "default": "task_id",
                "title": "Order By"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskCollectionResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        },
        "/api/v2/dags/{dag_id}/tasks/{task_id}": {
        "get": {
            "tags": [
            "Task"
            ],
            "summary": "Get Task",
            "description": "Get simplified representation of a task.",
            "operationId": "get_task",
            "security": [
            {
                "OAuth2PasswordBearer": []
            }
            ],
            "parameters": [
            {
                "name": "dag_id",
                "in": "path",
                "required": True,
                "schema": {
                "type": "string",
                "title": "Dag Id"
                }
            },
            {
                "name": "task_id",
                "in": "path",
                "required": True,
                "schema": {
                "title": "Task Id"
                }
            }
            ],
            "responses": {
            "200": {
                "description": "Successful Response",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/TaskResponse"
                    }
                }
                }
            },
            "400": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Bad Request"
            },
            "401": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Unauthorized"
            },
            "403": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Forbidden"
            },
            "404": {
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPExceptionResponse"
                    }
                }
                },
                "description": "Not Found"
            },
            "422": {
                "description": "Validation Error",
                "content": {
                "application/json": {
                    "schema": {
                    "$ref": "#/components/schemas/HTTPValidationError"
                    }
                }
                }
            }
            }
        }
        }
    }
    }
    """
    # Build prompt with spec
    system_msg = (
        "You are an Airflow v2 REST API assistant. "
        "Here are available endpoints and their parameters:\n" + OPENAPI_PATHS +
        "\nGiven the user's question, output a JSON array of actions with keys 'method','path','params'. "
        f"User question: '{user_prompt}'"
    )
    try:
        # Ask AI
        ai_resp = ai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role":"system","content":system_msg}],
            max_tokens=500,
            temperature=0,
        )
        raw = ai_resp.choices[0].message.content
        m = re.search(r"(\[.*\])", raw, re.DOTALL)
        if not m:
            raise ValueError("AI response missing JSON actions")
        actions = json.loads(m.group(1))

        # Execute actions via HTTP
        auth = request.headers.get('authorization', '')
        headers = {'Accept': 'application/json'}
        if auth:
            headers['Authorization'] = auth
        results = []
        for act in actions:
            method = act.get('method', 'GET').upper()
            raw_path = act['path']
            path = raw_path if raw_path.startswith('/api/v2/') else '/api/v2' + raw_path
            params = act.get('params', {}) or {}
            # Fill missing required
            op = SPEC_PATHS.get(path, {}).get(method.lower(), {})
            # path & query params
            for p in op.get('parameters', []):
                name = p.get('name')
                if p.get('required') and name not in params:
                    params[name] = default_for_schema(p.get('schema', {}))
            # body
            rb = op.get('requestBody', {})
            if rb:
                sch = rb.get('content', {}).get('application/json', {}).get('schema', {})
                for name in sch.get('required', []):
                    if name not in params:
                        params[name] = default_for_schema(sch.get('properties', {}).get(name, {}))

            url = BASE_URL + path.format(dag_id=dag_id)
            try:
                if method in ('GET', 'DELETE'):
                    resp = requests.request(method, url, headers=headers, params=params, timeout=10)
                else:
                    hdr = headers.copy()
                    hdr['Content-Type'] = 'application/json'
                    resp = requests.request(method, url, headers=hdr, json=params, timeout=10)
                data = None
                try:
                    data = resp.json()
                except Exception:
                    data = resp.text
                results.append({'action': act, 'status_code': resp.status_code, 'data': data})
            except Exception as e:
                results.append({'action': act, 'error': str(e)})

        # Summarize
        synth = ai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {'role':'system','content':'Summarize the following API results.'},
                {'role':'user','content':json.dumps(results)}
            ],
            max_tokens=300,
            temperature=0.5,
        )
        return {'answer': synth.choices[0].message.content.strip(), 'results': results}

    except Exception as e:
        logger.exception("Dynamic AI mapping failed")
        raise HTTPException(500, f"AI mapping error: {e}")

# ——— Plugin Registration ———
fastapi_app = {'app': app, 'name': 'dag_list_ai', 'url_prefix': '/dag-list'}
class DagListPlugin(AirflowPlugin):
    name = 'dag_list_ai_plugin'
    fastapi_apps = [fastapi_app]
    menu_links = [{'name':'DAG Explorer','href':'/dag-list/','icon':'List'}]
    appbuilder_menu_items = [{'name':'DAG Explorer','href':'/dag-list/'}]
