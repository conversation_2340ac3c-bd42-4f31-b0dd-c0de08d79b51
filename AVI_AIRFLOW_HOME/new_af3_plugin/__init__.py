from __future__ import annotations

from pathlib import Path

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from airflow.plugins_manager import AirflowPlugin

# Create a FastAPI app for the plugin
app = FastAPI(title="Airflow 3 Demo Plugin")

# Get the path to the UI dist directory
ui_dist = Path(__file__).parent / "ui" / "dist"

# Mount the UI assets at /plugin-ui instead of /ui to avoid conflicts
app.mount("/plugin-ui", StaticFiles(directory=ui_dist, html=True), name="plugin_ui")

class Af3DemoPlugin(AirflowPlugin):
    """Demo plugin for Airflow 3 using the new iframe-based plugin system."""

    name = "af3_demo_plugin"

    # Register the FastAPI app
    fastapi_apps = [
        {
            "app": app,
            "url_prefix": "/af3-demo",
            "name": "af3_demo_api"
        },
    ]

    # Register the UI metadata
    ui = [
        {
            "slug": "af3-demo",          # /plugins/af3-demo
            "label": "AF3 Demo",
            "icon": "FiZap",             # react-icons/fi icon
            "entry": "/af3-demo/plugin-ui",  # Note: using plugin-ui instead of ui
            "type": "iframe",
            "permissions": ["plugins.can_read"],  # Use a standard Airflow permission
        }
    ]
