# Airflow 3 Demo Plugin

This is a sample plugin for Apache Airflow 3 using the new iframe-based plugin system.

## Features

- Integration with Airflow 3's React UI
- Iframe-based plugin rendering
- Dark mode support
- Custom UI components

## Installation

1. Copy this directory to your Airflow plugins directory or add it to your Python path.

2. Restart the Airflow webserver to load the plugin.

## Usage

After installation, you should see a new "AF3 Demo" menu item in the Plugins section of the Airflow UI sidebar. Click on it to view the plugin UI.

## Development

### UI Development

The UI is a simple HTML/CSS/JS application in the `ui/dist` directory. For a real plugin, you would typically:

1. Create a React application in the `ui` directory
2. Build it to the `ui/dist` directory
3. Serve the built assets via the FastAPI app

### Plugin Structure

- `__init__.py`: Main plugin file that defines the `AirflowPlugin` class
- `ui/dist/index.html`: Simple UI for the plugin

## Notes

- This plugin uses the `/plugin-ui` path instead of `/ui` to avoid conflicts with core Airflow routes
- The plugin uses the standard Airflow permission `plugins.can_read`
- The UI includes a dark mode toggle for demonstration purposes
