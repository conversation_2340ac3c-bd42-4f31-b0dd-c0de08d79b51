[project]
name = "airflow-af3-demo-plugin"
version = "0.1.0"
description = "Demo plugin for Airflow 3 using the new iframe-based plugin system"
readme = "README.md"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON><PERSON>hak<PERSON>", email = "<EMAIL>"}
]
license = {text = "Apache License 2.0"}
classifiers = [
    "Development Status :: 3 - Alpha",
    "Environment :: Web Environment",
    "Framework :: Apache Airflow",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
requires-python = ">=3.8"
dependencies = [
    "apache-airflow>=3.1.0",
]

[project.urls]
"Homepage" = "https://github.com/apache/airflow"
"Bug Tracker" = "https://github.com/apache/airflow/issues"

[project.optional-dependencies]
dev = [
    "build>=1.2.2",
    "pre-commit>=4.0.1",
    "ruff>=0.9.2"
]

[project.entry-points."airflow.plugins"]
af3_demo_plugin = "AVI_AIRFLOW_HOME.new_af3_plugin:Af3DemoPlugin"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.sdist]
exclude = [
    "*",
    "!AVI_AIRFLOW_HOME/**",
    "!pyproject.toml",
    "!ui/**"
]

[tool.hatch.build.targets.wheel]
packages = ["AVI_AIRFLOW_HOME/new_af3_plugin"]

[tool.hatch.build.targets.wheel.sources]
"AVI_AIRFLOW_HOME/new_af3_plugin" = "new_af3_plugin"

[tool.ruff]
line-length = 200
indent-width = 4
fix = true
preview = true

lint.select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "I",  # isort
    "W",  # pycodestyle warnings
    "C90",  # Complexity
    "C",  # flake8-comprehensions
    "ISC",  # flake8-implicit-str-concat
    "T10",  # flake8-debugger
    "A",  # flake8-builtins
    "UP",  # pyupgrade
]

lint.ignore = [
    "C416",  # Unnecessary list comprehension - rewrite as a generator expression
    "C408",  # Unnecessary `dict` call - rewrite as a literal
    "ISC001"  # Single line implicit string concatenation
]

lint.fixable = ["ALL"]
lint.unfixable = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false

[tool.ruff.lint.isort]
combine-as-imports = true

[tool.ruff.lint.mccabe]
max-complexity = 12


