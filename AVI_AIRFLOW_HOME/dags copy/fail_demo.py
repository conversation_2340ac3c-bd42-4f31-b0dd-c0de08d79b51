from airflow.decorators import dag, task
import pendulum
import time


@dag(
    dag_id="trigger_rule_demo",
    schedule=None,
    start_date=pendulum.datetime(2024, 1, 1),
    catchup=False,
    default_args={"retries": 0},
)
def trigger_rule_showcase():
    @task
    def task_1():
        time.sleep(30)
        return "Task 1 done"

    @task
    def task_2():
        time.sleep(45)
        return "Task 2 done"

    @task
    def task_3():
        time.sleep(60)
        return "Task 3 done"

    @task(trigger_rule="all_done")
    def final_task():
        return "Final task done"

    [task_1(), task_2(), task_3()] >> final_task()


dag = trigger_rule_showcase()
