<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

# Airflow 3 UI Plugins: A Documentation Plan

## 1. Introduction to Airflow 3 UI Plugins

*   **Paradigm Shift:** Explain the transition from Airflow 2's Flask-based UI extensions to Airflow 3's modern architecture utilizing FastAPI for the backend and React/Vite for the frontend Single Page Application (SPA).
*   **Flask AppBuilder Limitations:** Clarify that traditional Flask AppBuilder views and menu items from Airflow 2 plugins will have limited integration in Airflow 3. They are typically rendered within iframes or as simple external links, rather than being deeply embedded in the new React UI. (Evidence: `airflow-core/docs/howto/custom-view-plugin.rst` notes, "It is not possible to extend the core UI... nonetheless extra menu items of the auth managers are added to the core UI security tab and their `href` are rendered in iframes.")
*   **New Approach:** The recommended method for Airflow 3 UI plugins involves creating separate React applications (ideally using Vite for development and bundling) for the plugin's user interface. These are then integrated into the main Airflow UI via menu links.

## 2. Core Concepts for UI Plugin Development

*   **FastAPI Endpoints for Plugins:**
    *   Plugins must expose their own FastAPI endpoints. These endpoints are crucial for:
        *   Serving the static assets (HTML, JS, CSS) of the plugin's React frontend.
        *   Handling any backend logic, API calls, or data processing specific to the plugin.
    *   The `airflow-core/docs/howto/custom-view-plugin.rst` documentation states: "Using `fastapi_apps` in Airflow plugin, the core RestAPI can be extended to support extra endpoints to serve custom static file or any other json/application responses."
    *   Detail the process of defining and registering these `fastapi_apps` within an Airflow plugin's main class.
*   **Menu Item Integration:**
    *   Airflow's `plugins_manager.py` continues to process `appbuilder_menu_items` defined in plugins.
    *   The main Airflow UI (specifically, the component at `airflow-core/src/airflow/ui/src/layouts/Nav/PluginMenus.tsx`) fetches plugin metadata, including `appbuilder_menu_items`, through an API call (`usePluginServiceGetPlugins`). It then dynamically renders these as menu items.
    *   These menu items should link to the paths served by the plugin's dedicated FastAPI application.
    *   Provide clear instructions on how to define `appbuilder_menu_items` in the plugin class, ensuring the `href` attribute points to the correct route that serves the plugin's React application.
*   **Creating the Plugin Frontend (React/Vite):**
    *   **Recommendation:** Advise developers to create a new, standalone Vite + React project for each plugin's UI. This promotes modularity and keeps the plugin's frontend codebase self-contained and independent of the core Airflow UI's build process.
    *   **Setup:** Include a concise guide or link to official Vite documentation for initializing a new React (preferably TypeScript) project.
    *   **Building Assets:** Explain the process of building the Vite project into a set of static assets (HTML, JS, CSS bundles) that can be served by the plugin's FastAPI application.
    *   **Asset Path Adjustments:** Note that the core UI's `vite.config.ts` includes a `transformIndexHtml` plugin to modify asset paths. A similar configuration or careful base path setting in the plugin's Vite config might be necessary if its UI is served under a subpath, to ensure assets load correctly.

## 3. Step-by-Step Guide: Creating a Simple UI Plugin

*   **Step 1: Plugin Directory Structure:**
    *   Propose a conventional directory layout for an Airflow plugin that includes a frontend component.
        ```
        my_airflow_plugin/
        ├── __init__.py
        ├── plugins/
        │   └── my_ui_plugin.py  # Airflow plugin class
        ├── api/
        │   └── main.py          # Plugin's FastAPI app
        └── ui/
            ├── vite.config.ts
            ├── package.json
            ├── tsconfig.json
            ├── public/
            └── src/             # React components
                └── main.tsx
        ```
*   **Step 2: Define the Airflow Plugin Class:**
    *   Illustrate how to create the main plugin class, inheriting from `airflow.plugins_manager.AirflowPlugin`.
    *   Demonstrate defining `appbuilder_menu_items`. The `href` should be constructed to point to the base path where the plugin's UI will be served (e.g., `/my_plugin_api_prefix/ui_view`).
    *   Show how to register the plugin's FastAPI application using the `fastapi_apps` attribute.
        ```python
        # In my_airflow_plugin/plugins/my_ui_plugin.py
        from airflow.plugins_manager import AirflowPlugin
        from fastapi import FastAPI
        from starlette.staticfiles import StaticFiles
        import os

        # Define the path to the plugin's built UI assets
        # This assumes the 'ui/dist' directory is relative to this plugin file
        plugin_root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        ui_build_dir = os.path.join(plugin_root_dir, "ui", "dist")

        # Create a FastAPI app instance for the plugin (can be imported from api.main)
        # For simplicity, defined here, but better to import from api/main.py
        my_plugin_fastapi_app = FastAPI()

        # Mount static files for the plugin's UI
        # The path "/" here is relative to the `url_prefix` defined in `fastapi_apps`
        my_plugin_fastapi_app.mount(
            "/ui_view",  # This will be accessible at /my_plugin_api_prefix/ui_view
            StaticFiles(directory=ui_build_dir, html=True),
            name="my_plugin_static_ui",
        )


        # Example: A simple API endpoint within the plugin's FastAPI app
        @my_plugin_fastapi_app.get("/api/hello")
        async def read_hello():
            return {"message": "Hello from My Plugin API"}


        class MyAirflowUIPlugin(AirflowPlugin):
            name = "my_ui_plugin"

            # Menu item definition
            appbuilder_menu_items = [
                {
                    "name": "My Plugin View",
                    "category": "My Custom Plugins",  # This will group it in the "Plugins" menu
                    # This href should match the FastAPI app's url_prefix + the mount path for StaticFiles
                    "href": "/my_plugin_api_prefix/ui_view/index.html",
                }
            ]

            # Registering the FastAPI application
            fastapi_apps = [
                {
                    "app": my_plugin_fastapi_app,
                    "name": "my_plugin_endpoints",  # A unique name for the app
                    "url_prefix": "/my_plugin_api_prefix",  # Base path for this plugin's FastAPI app
                }
            ]
        ```
*   **Step 3: Create the FastAPI App for the Plugin:**
    *   (As shown above, or in a separate `api/main.py` file).
    *   This application is responsible for:
        *   Serving the static files of the plugin's React UI (using `StaticFiles`). The `html=True` option allows serving `index.html` for root requests to the mounted path.
        *   Optionally, defining any backend API endpoints that the plugin's UI might need to interact with.
*   **Step 4: Develop the React/Vite Frontend (in `ui/` directory):**
    *   Initialize a Vite + React project: `npm create vite@latest . -- --template react-ts` (run this inside the `ui/` directory).
    *   Develop the necessary React components for the plugin's interface.
    *   Configure the plugin's `ui/vite.config.ts`:
        *   Set the `base` option to match the path where it will be served. This is crucial for correct asset loading. For the example above, it would be `base: '/my_plugin_api_prefix/ui_view/'`.
        *   Ensure `build.outDir` is set to `dist` (or whatever directory the FastAPI `StaticFiles` is configured to serve from).
            ```typescript
            // In my_airflow_plugin/ui/vite.config.ts
            import { defineConfig } from 'vite'
            import react from '@vitejs/plugin-react-swc'

            export default defineConfig({
              plugins: [react()],
              base: '/my_plugin_api_prefix/ui_view/', // Critical for correct asset paths
              build: {
                outDir: 'dist',
              },
            })
            ```
    *   Build the React application: `npm run build` (from within the `ui/` directory).
*   **Step 5: Deployment and Testing:**
    *   Provide instructions on how to install the plugin into an Airflow environment (e.g., by adding it to the `plugins` folder or installing it as a package).
    *   Describe how the new menu item should appear under the "Plugins" section (or the specified category) in the Airflow UI.
    *   Verify that clicking the menu item correctly navigates to and renders the plugin's React view.

### Example: Simple Form UI Plugin

This section provides a complete, runnable example of an Airflow 3 UI plugin. It demonstrates a simple form with various input types, served by a FastAPI backend and built with a React/Vite frontend using Chakra UI.

**1. Plugin Directory Structure**

Create the following directory structure for your plugin (e.g., `airflow_simple_form_plugin`):

```plaintext
airflow_simple_form_plugin/
├── __init__.py                 # Can be empty
├── plugins/
│   └── simple_form_plugin.py   # Airflow plugin class definition
├── api/
│   └── main.py                 # FastAPI application for the plugin
└── ui/
    ├── package.json
    ├── vite.config.ts
    ├── tsconfig.json
    ├── index.html
    ├── public/
    │   └── vite.svg            # Default Vite public asset
    └── src/
        ├── main.tsx            # React app entry point
        ├── App.tsx             # Main form component
        ├── theme.ts            # Chakra UI theme configuration
        └── vite-env.d.ts       # Vite TypeScript environment types
```

**2. Python Backend Files**

**(a) `airflow_simple_form_plugin/plugins/simple_form_plugin.py`**

```python
from airflow.plugins_manager import AirflowPlugin
import os

# Import the FastAPI app instance from the api submodule
# Ensure api/main.py defines an 'app' variable that is the FastAPI instance
from ..api.main import app as simple_form_fastapi_app


class AirflowSimpleFormPlugin(AirflowPlugin):
    name = "simple_form_plugin"

    # Menu item definition for Airflow UI
    appbuilder_menu_items = [
        {
            "name": "Simple Form",
            "category": "Plugin Examples",  # Appears under this category in Plugins menu
            # This href must match the FastAPI app's url_prefix + the mount path for StaticFiles + index.html
            "href": "/simple_form_api/ui/index.html",
        }
    ]

    # Registering the FastAPI application
    fastapi_apps = [
        {
            "app": simple_form_fastapi_app,
            "name": "simple_form_api_endpoints",  # A unique name for these endpoints
            "url_prefix": "/simple_form_api",  # Base path for this plugin's FastAPI app
        }
    ]
```

**(b) `airflow_simple_form_plugin/api/main.py`**

```python
from fastapi import FastAPI, Request, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates  # Optional, if you need server-side templates for other things
from fastapi.responses import HTMLResponse, JSONResponse
import os
import logging

logger = logging.getLogger(__name__)

app = FastAPI(title="Simple Form Plugin API")

# Determine the absolute path to the plugin directory and then to the ui/dist directory
plugin_root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ui_build_dir = os.path.join(plugin_root_dir, "ui", "dist")

if not os.path.exists(ui_build_dir):
    logger.error(f"UI build directory not found: {ui_build_dir}")
    logger.error("Please ensure you have run 'npm run build' in the 'ui' directory of the plugin.")

# Mount static files for the plugin's UI (React app)
# The path "/ui" here is relative to the `url_prefix` defined in `fastapi_apps` in simple_form_plugin.py
app.mount("/ui", StaticFiles(directory=ui_build_dir, html=True), name="plugin_simple_form_ui_static")


@app.get("/", include_in_schema=False)
async def root_redirect():
    # Redirect root of this FastAPI app to the UI's index.html
    return HTMLResponse(
        '<html><head><meta http-equiv="refresh" content="0; url=/simple_form_api/ui/index.html" /></head></html>'
    )


@app.post("/submit-form")
async def handle_form_submission(
    text_field: str = Form(...),
    datetime_field: str = Form(...),
    file_upload: UploadFile = File(None),  # Optional file
):
    logger.info(f"Form submitted!")
    logger.info(f"Text Field: {text_field}")
    logger.info(f"Datetime Field: {datetime_field}")

    file_info = "No file uploaded"
    if file_upload and file_upload.filename:
        file_contents = await file_upload.read()
        logger.info(f"File Name: {file_upload.filename}")
        logger.info(f"File Content Type: {file_upload.content_type}")
        logger.info(f"File Size: {len(file_contents)} bytes")
        file_info = f"Uploaded: {file_upload.filename} ({len(file_contents)} bytes)"
        # Here you would typically save the file or process its contents
        # For example: with open(f"uploaded_{file_upload.filename}", "wb") as f:
        #     f.write(file_contents)

    return JSONResponse(
        content={
            "message": "Form submitted successfully!",
            "text_field": text_field,
            "datetime_field": datetime_field,
            "file_info": file_info,
        }
    )


# Example API endpoint (optional)
@app.get("/hello")
def read_hello():
    return {"message": "Hello from Simple Form Plugin API"}
```

**3. React/Vite Frontend Files (`ui/` directory)**

**(a) `airflow_simple_form_plugin/ui/package.json`**

```json
{
  "name": "simple-form-plugin-ui",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "@chakra-ui/react": "^2.8.2",
    "@emotion/react": "^11.11.4",
    "@emotion/styled": "^11.11.5",
    "framer-motion": "^11.2.10",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-hook-form": "^7.51.5"
  },
  "devDependencies": {
    "@types/react": "^18.2.66",
    "@types/react-dom": "^18.2.22",
    "@typescript-eslint/eslint-plugin": "^7.2.0",
    "@typescript-eslint/parser": "^7.2.0",
    "@vitejs/plugin-react-swc": "^3.5.0",
    "eslint": "^8.57.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.6",
    "typescript": "^5.2.2",
    "vite": "^5.2.0"
  }
}
```

**(b) `airflow_simple_form_plugin/ui/vite.config.ts`**

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  // This base path MUST match the href in appbuilder_menu_items (excluding index.html)
  // and how FastAPI serves the static files.
  base: '/simple_form_api/ui/',
  build: {
    outDir: 'dist',
    chunkSizeWarningLimit: 1600, // Optional: Adjust as needed
  },
  server: {
    // Optional: For local development, proxy API requests to your Airflow backend
    // proxy: {
    //   '/simple_form_api': 'http://localhost:8080', // Adjust if your Airflow runs elsewhere
    // },
  },
})
```

**(c) `airflow_simple_form_plugin/ui/tsconfig.json`**

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```
(You will also need a `tsconfig.node.json` if it's referenced, typically for Vite config processing. Vite usually generates this.)

**`airflow_simple_form_plugin/ui/tsconfig.node.json` (if needed by Vite setup):**
```json
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true
  },
  "include": ["vite.config.ts"]
}
```

**(d) `airflow_simple_form_plugin/ui/index.html`** (Standard Vite entry point)

```html
<!doctype html>
<html lang="en" style="height: 100%;">
  <head>
    <meta charset="UTF-8" />
    <!-- The base href will be injected by Vite based on vite.config.ts -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Simple Form Plugin</title>
  </head>
  <body style="height: 100%; margin: 0;">
    <div id="root" style="height: 100%;"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
```

**(e) `airflow_simple_form_plugin/ui/src/theme.ts`** (Simplified Chakra UI Theme)

```typescript
import { extendTheme, type ThemeConfig } from '@chakra-ui/react';

// Basic color mode config
const config: ThemeConfig = {
  initialColorMode: 'system',
  useSystemColorMode: true,
};

// Define some basic colors similar to Airflow's scheme
// This is a very simplified version. For full consistency, you'd replicate more from Airflow's core theme.ts
const colors = {
  airflow: {
    // Example: using blue shades similar to Airflow
    50: '#EBF8FF',  // Lightest blue
    100: '#BEE3F8',
    200: '#90CDF4',
    300: '#63B3ED',
    400: '#4299E1', // Primary blue
    500: '#3182CE',
    600: '#2B6CB0',
    700: '#2C5282',
    800: '#2A4365',  // Darkest blue
    900: '#1A365D',
  },
  // You can add more specific colors for states like success, error, warning
  success: {
    500: '#38A169', // Green
  },
  error: {
    500: '#E53E3E', // Red
  },
};

const theme = extendTheme({
  config,
  colors,
  styles: {
    global: (props: any) => ({
      body: {
        bg: props.colorMode === 'dark' ? 'gray.800' : 'gray.50',
        color: props.colorMode === 'dark' ? 'whiteAlpha.900' : 'gray.800',
      },
    }),
  },
  components: {
    // You can add component-specific style overrides here if needed
    Button: {
      baseStyle: {
        // Example: fontWeight: 'bold',
      },
      variants: {
        solid: (props: any) => ({
          bg: props.colorMode === 'dark' ? 'airflow.300' : 'airflow.500',
          color: props.colorMode === 'dark' ? 'gray.800' : 'white',
          _hover: {
            bg: props.colorMode === 'dark' ? 'airflow.400' : 'airflow.600',
          }
        }),
      },
    },
  },
});

export default theme;
```

**(f) `airflow_simple_form_plugin/ui/src/App.tsx`** (Main Form Component)

```tsx
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  useToast,
  Textarea, // Added for multiline text
  Select,   // Added for dropdown
  Checkbox, // Added for checkbox
  Radio, RadioGroup, Stack, // Added for radio buttons
  useColorMode,
  IconButton,
  Flex
} from '@chakra-ui/react';
import { SunIcon, MoonIcon } from '@chakra-ui/icons';
import { useForm, SubmitHandler } from 'react-hook-form';
import React from 'react';

interface IFormInput {
  textField: string;
  multilineTextField: string;
  datetimeField: string;
  fileUpload?: FileList;
  selectField: string;
  checkboxField: boolean;
  radioField: string;
}

function App() {
  const { register, handleSubmit, formState: { errors, isSubmitting }, reset } = useForm<IFormInput>();
  const toast = useToast();
  const { colorMode, toggleColorMode } = useColorMode();

  const onSubmit: SubmitHandler<IFormInput> = async (data) => {
    const formData = new FormData();
    formData.append('text_field', data.textField);
    formData.append('multiline_text_field', data.multilineTextField);
    formData.append('datetime_field', data.datetimeField);
    formData.append('select_field', data.selectField);
    formData.append('checkbox_field', String(data.checkboxField));
    formData.append('radio_field', data.radioField);

    if (data.fileUpload && data.fileUpload.length > 0) {
      formData.append('file_upload', data.fileUpload[0]);
    }

    try {
      // The URL for submission should match the FastAPI endpoint
      // It's relative to the current page's origin if served under the same domain
      // or an absolute URL if different.
      // Since the FastAPI app is prefixed with /simple_form_api, the path is /simple_form_api/submit-form
      const response = await fetch('/simple_form_api/submit-form', {
        method: 'POST',
        body: formData, // FormData handles multipart/form-data for file uploads
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.detail || 'Form submission failed');
      }

      toast({
        title: 'Form Submitted.',
        description: `Server response: ${result.message || JSON.stringify(result)}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      reset(); // Reset form fields after successful submission
    } catch (error: any) {
      toast({
        title: 'Submission Error.',
        description: error.message || 'An unexpected error occurred.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Container maxW="container.md" py={10}>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading as="h1" size="lg">Simple Plugin Form</Heading>
        <IconButton
          aria-label="Toggle theme"
          icon={colorMode === 'light' ? <MoonIcon /> : <SunIcon />}
          onClick={toggleColorMode}
        />
      </Flex>
      <Box p={6} borderWidth="1px" borderRadius="lg" boxShadow="md">
        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={4}>
            <FormControl isInvalid={!!errors.textField}>
              <FormLabel htmlFor="textField">Text Input</FormLabel>
              <Input
                id="textField"
                placeholder="Enter some text"
                {...register('textField', { required: 'Text input is required' })}
              />
              {errors.textField && <Box color="red.500" fontSize="sm">{errors.textField.message}</Box>}
            </FormControl>

            <FormControl isInvalid={!!errors.multilineTextField}>
              <FormLabel htmlFor="multilineTextField">Multiline Text Input</FormLabel>
              <Textarea
                id="multilineTextField"
                placeholder="Enter longer text here..."
                {...register('multilineTextField')}
              />
            </FormControl>

            <FormControl isInvalid={!!errors.datetimeField}>
              <FormLabel htmlFor="datetimeField">Date & Time Input</FormLabel>
              <Input
                id="datetimeField"
                type="datetime-local"
                {...register('datetimeField', { required: 'Date and time is required' })}
              />
              {errors.datetimeField && <Box color="red.500" fontSize="sm">{errors.datetimeField.message}</Box>}
            </FormControl>

            <FormControl isInvalid={!!errors.selectField}>
              <FormLabel htmlFor="selectField">Select Dropdown</FormLabel>
              <Select
                id="selectField"
                placeholder="Choose an option"
                {...register('selectField', { required: 'Please select an option' })}
              >
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
              </Select>
              {errors.selectField && <Box color="red.500" fontSize="sm">{errors.selectField.message}</Box>}
            </FormControl>

            <FormControl>
              <FormLabel htmlFor="fileUpload">File Upload (Optional)</FormLabel>
              <Input
                id="fileUpload"
                type="file"
                {...register('fileUpload')}
                p={1} // Add some padding for better appearance
              />
            </FormControl>

            <FormControl isInvalid={!!errors.checkboxField}>
              <Checkbox
                id="checkboxField"
                {...register('checkboxField')}
              >
                I agree to the terms
              </Checkbox>
              {/* You can add validation for checkbox if needed, e.g., must be checked */}
            </FormControl>

            <FormControl as="fieldset" isInvalid={!!errors.radioField}>
              <FormLabel as="legend">Radio Options</FormLabel>
              <RadioGroup defaultValue="radio1">
                <Stack spacing={5} direction="row">
                  <Radio {...register('radioField', { required: "Please select a radio option"})} value="radio1">Radio 1</Radio>
                  <Radio {...register('radioField')} value="radio2">Radio 2</Radio>
                  <Radio {...register('radioField')} value="radio3">Radio 3</Radio>
                </Stack>
              </RadioGroup>
              {errors.radioField && <Box color="red.500" fontSize="sm">{errors.radioField.message}</Box>}
            </FormControl>

            <Button
              mt={4}
              colorScheme="airflow" // Using a color defined in our theme
              isLoading={isSubmitting}
              type="submit"
              w="full"
            >
              Submit Form
            </Button>
          </VStack>
        </form>
      </Box>
    </Container>
  );
}

export default App;

```

**(g) `airflow_simple_form_plugin/ui/src/main.tsx`** (React App Entry Point)

```tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import { ChakraProvider, ColorModeScript } from '@chakra-ui/react'
import theme from './theme.ts' // Your custom theme

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ChakraProvider theme={theme}>
      <ColorModeScript initialColorMode={theme.config.initialColorMode} />
      <App />
    </ChakraProvider>
  </React.StrictMode>,
)
```

**(h) `airflow_simple_form_plugin/ui/src/vite-env.d.ts`** (Default Vite TS env types)

```typescript
/// <reference types="vite/client" />
```

**4. Build and Run**

**(a) Build the Frontend:**

Navigate to your plugin's `ui` directory:

```bash
cd path/to/your/airflow_simple_form_plugin/ui
npm install
npm run build
```

This will create a `dist` folder inside `ui` with the static assets.

**(b) Install the Plugin in Airflow:**

Place the entire `airflow_simple_form_plugin` directory into your Airflow `plugins` folder.

**(c) Restart Airflow Webserver:**

After adding the plugin and building the UI, restart the Airflow webserver.

**(d) Access the Plugin:**

In the Airflow UI, navigate to the "Plugins" menu. You should see a category "Plugin Examples" (or whatever you named it) with a "Simple Form" link. Clicking this link will take you to `/simple_form_api/ui/index.html`, which serves your React application.

This example provides a solid foundation. Users can expand upon it by adding more complex form handling, API interactions, and UI components as needed.

## 4. Advanced Topics (Considerations for Robust Plugins)

*   **Authentication and Authorization:**
    *   Discuss strategies for securing the plugin\'s UI and its API endpoints. The main Airflow UI\'s authentication context does not automatically extend to separately served React applications.
    *   If the plugin\'s UI needs to call core Airflow APIs, explain how to handle authentication for those calls (e.g., ensuring session cookies are forwarded, handling CSRF tokens if necessary).
*   **Styling and Theming Consistency:**
    *   **Goal:** Aim for visual consistency with the main Airflow UI.
    *   **Chakra UI:** The Airflow 3 UI uses Chakra UI. Your plugin should also use Chakra UI for its components.
    *   **Replicating the Theme:**
        *   Airflow\'s core theme is defined in `airflow-core/src/airflow/ui/src/theme.ts`. It customizes default Chakra colors and defines semantic tokens for various UI states (e.g., `success.solid`, `failed.fg`).
        *   **Process:**
            1.  Install Chakra UI in your plugin\'s `ui` project (`npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion`).
            2.  Create a `theme.ts` file in your plugin\'s `ui/src` directory.
            3.  Copy the relevant color palette definitions (from `theme.tokens.colors`) and semantic token generation logic (including the `generateSemanticTokens` function and `theme.semanticTokens.colors` definitions) from Airflow\'s core `theme.ts` into your plugin\'s `theme.ts`.
            4.  In your plugin\'s main React application file (e.g., `ui/src/main.tsx`), import `extendTheme` from `@chakra-ui/react` and your theme configuration. Create a theme instance using `extendTheme(yourPluginThemeConfig)` and pass it to the `<ChakraProvider theme={pluginTheme}>`.
        *   **Benefits:** This approach allows your plugin to use the same color schemes and support light/dark mode consistently with the main Airflow UI.
        *   **Maintenance:** Be aware that if the core Airflow theme is updated, you may need to manually update your plugin\'s theme file to maintain consistency.
    *   Offer guidance on how to make the plugin\'s UI visually consistent with the main Airflow UI. This might involve:
        *   Using the same UI component library (Chakra UI).
        *   Adopting similar theming principles or even importing/referencing Airflow\'s theme variables if feasible.
*   **State Management and Context Sharing:**
    *   Acknowledge that direct state sharing or context propagation between the main Airflow React app and a plugin's separate React app is complex and generally not supported out-of-the-box. Communication would typically happen via API calls.
*   **API Communication:**
    *   Detail best practices for the plugin's frontend to communicate with its own backend API (served by its FastAPI app) and, if necessary, with the core Airflow REST API.

## Evidence from Code:

*   **React/Vite Usage:** Confirmed by `airflow-core/src/airflow/ui/package.json` (scripts like `"dev": "vite"`, `"build": "vite build"`) and `airflow-core/src/airflow/ui/vite.config.ts`.
*   **FastAPI Serving Main UI:** Evident in `airflow-core/src/airflow/api_fastapi/core_api/app.py`, which serves static assets from `airflow/ui/dist` (production) or `airflow/ui/dev` (development).
*   **Plugin Integration Points:** `airflow-core/src/airflow/plugins_manager.py` handles the registration of `appbuilder_menu_items` and `fastapi_apps` from plugins.
*   **Dynamic Menu Rendering:** The React component `airflow-core/src/airflow/ui/src/layouts/Nav/PluginMenus.tsx` demonstrates how plugin menu items are fetched via an API and rendered in the navigation.
*   **Plugin Documentation (Core):** `airflow-core/docs/howto/custom-view-plugin.rst` is a key resource, providing insights into `fastapi_apps` and the limitations of older Flask-based UI extensions in Airflow 3.

This plan emphasizes leveraging Airflow 3's FastAPI backend to allow plugins to serve their own modern React-based UIs, integrating them primarily through menu links. It recognizes that deep, seamless integration into the main React application's component hierarchy is likely not the standard or straightforward path for plugin UIs.
