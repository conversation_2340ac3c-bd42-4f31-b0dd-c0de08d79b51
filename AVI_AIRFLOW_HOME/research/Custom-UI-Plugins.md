<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

# Custom UI Plugin Integration in Airflow 3 (Vite/React/Chakra)

## Goal

Enable rendering a plugin's custom UI (served from a FastAPI app) inside the Airflow 3 React UI, while keeping the Chakra-based navigation bar (sidebar) visible.

---

## 1. How the Airflow 3 UI Layout Works

- The main layout is defined in `BaseLayout.tsx`.
  - The sidebar navigation (`Nav`) is always rendered on the left.
  - The main content area is rendered to the right of the sidebar, using React Router's `<Outlet />` for routing.
- The sidebar (`Nav`) is built with Chakra UI components and includes plugin menus via `PluginMenus.tsx`.
- Plugin menus currently only support FAB-style menu items (not FastAPI plugin UIs).

---

## 2. What You Want

- You want to render a custom UI (from a FastAPI plugin) inside the main content area, with the Chakra sidebar still visible.
- Ideally, you want to add a menu item to the sidebar that, when clicked, loads your plugin's UI in the main content area (not in a new tab or iframe).

---

## 3. What the Code Supports (as of May 2025)

- The sidebar (`Nav`) and layout (`BaseLayout`) are Chakra-based and always present.
- Routing is handled by React Router v6, with `<Outlet />` used for nested routes.
- There is no built-in mechanism for dynamically discovering FastAPI plugin UIs and adding them as routes/pages in the React app.
- The current `PluginMenus` only supports FAB-style menu items, which are deprecated.

---

## 4. What You Would Need to Do

### A. Expose Your Plugin UI as a Standalone Frontend
- Your FastAPI plugin can serve a React (or other) SPA at a custom URL (e.g., `/my_plugin`).
- This UI is not automatically integrated into the Airflow React app.

### B. Integrate Plugin UI into Airflow React App
1. **Add a Route in the React App:**
   - Edit the Airflow UI's router configuration to add a new route (e.g., `/plugins/my_plugin`).
   - The route's component can use an `<iframe src="/my_plugin" />` to embed the plugin UI, or you can build a React wrapper that fetches and renders content from the plugin.
2. **Add a Menu Item:**
   - Add a new button or link in the `Nav` sidebar that navigates to `/plugins/my_plugin`.
3. **(Optional) Dynamic Discovery:**
   - For full dynamic support, you would need to extend the backend to expose registered FastAPI plugin UIs and have the frontend fetch and render them as routes/pages.

---

## 5. Example: Embedding a Plugin UI with an Iframe

- Add a new page in `src/pages/PluginMyPlugin.tsx`:
  ```tsx
  import { Box } from "@chakra-ui/react";

  export default function PluginMyPlugin() {
    return (
      <Box w="100%" h="100%">
        <iframe src="/my_plugin" style={{ width: '100%', height: '100%', border: 'none' }} />
      </Box>
    );
  }
  ```
- Add a route in your router config:
  ```tsx
  <Route path="/plugins/my_plugin" element={<PluginMyPlugin />} />
  ```
- Add a menu item in `Nav.tsx`:
  ```tsx
  <NavButton icon={<SomeIcon />} title="My Plugin" to="/plugins/my_plugin" />
  ```

---

## 6. Limitations & Future Directions
- This approach requires modifying the Airflow UI source and rebuilding it.
- There is no out-of-the-box support for auto-discovering and integrating FastAPI plugin UIs.
- For a more dynamic solution, both backend and frontend would need to be extended to support plugin UI registration and routing.

---

## References
- `src/layouts/BaseLayout.tsx`, `src/layouts/Nav/Nav.tsx`, `src/layouts/Nav/PluginMenus.tsx`
- [Airflow UI GitHub](https://github.com/apache/airflow/tree/main/airflow/ui)

---

This document will be updated as more dynamic plugin UI integration features become available in Airflow.
