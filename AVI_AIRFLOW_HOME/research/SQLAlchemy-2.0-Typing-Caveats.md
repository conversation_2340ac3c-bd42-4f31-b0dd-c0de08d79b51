<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

# SQLAlchemy 2.0+ Typing: Caveats and Static Analysis Challenges

## Problem Statement

With the introduction of SQLAlchemy 2.0+ typing (`Mapped[]` + `mapped_column()`), large Python codebases such as Airflow face a unique challenge: mixing new-style ORM typing with legacy `Column()` declarations can lead to confusing static analysis results and false-positive type errors from tools like mypy.

---

## The Core Issue

- **Legacy Style:**
  ```python
  my_date = Column(DateTime)
  ```
  - mypy interprets this as `Column[Any]`, not as a Python attribute of type `datetime | None`.
  - This means any assignment or operation expecting a `datetime` will trigger a type error, even if the code works at runtime.

- **New Style (SQLAlchemy 2.0+):**
  ```python
  my_date: Mapped[datetime | None] = mapped_column(DateTime)
  ```
  - mypy correctly understands the type, enabling static type checking and catching real errors.

- **Mixing Styles:**
  - In large codebases, it is common for some models to use the new style and others to remain on the legacy style, especially when refactoring is incremental.
  - When accessing attributes of legacy models (e.g., `dr.start_date` where `dr` is a `DagRun`), mypy will treat the attribute as a `Column[Any]`, not as a `datetime`.
  - This can result in false-positive errors like:
    ```
    error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[Any]")
    ```

---

## Technical Debt and Migration Complexity

- **Cascading Refactor:**
  - Migrating one model to new typing may require updating all related models to avoid type errors, which can be impractical in large, interdependent codebases.
  - This creates a situation where partial migration increases static analysis noise, rather than reducing it.

- **Type Safety vs. Practicality:**
  - Full type safety is only achieved when all models and attributes use the new `Mapped[]` style.
  - Until then, static analysis tools may flag correct code as erroneous solely due to legacy typing.

---

## Summary Table

| Model Style                  | mypy Type Safety | SQLAlchemy Support | Migration Needed?           |
|------------------------------|------------------|-------------------|-----------------------------|
| `Mapped[]` + `mapped_column()` | ✅ Yes           | ✅ Yes            | Yes, for full static safety |
| `Column()` (legacy)          | ❌ No            | ✅ Yes            | None (but no type safety)   |

---

## References
- [SQLAlchemy 2.0 ORM Typing Docs](https://docs.sqlalchemy.org/en/20/orm/declarative_tables.html#typing-orm-mapped-attributes)
- [Airflow SQLAlchemy Model Migration Discussions](https://github.com/apache/airflow/pull/29931)

---

## Conclusion

Mixing SQLAlchemy 2.0+ and legacy ORM typing is a source of static analysis friction in large Python projects. The core problem is not runtime correctness, but the mismatch between what static type checkers expect and what legacy model declarations provide. This should be considered technical debt, and teams should be aware that partial migration may temporarily increase the number of false-positive type errors until a full transition to the new style is complete.
