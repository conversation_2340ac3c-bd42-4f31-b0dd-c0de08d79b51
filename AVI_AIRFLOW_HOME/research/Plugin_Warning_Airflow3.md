<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

## Airflow 3 UI Bug: Non-existent URLs Return 200

### Issue Description

In Airflow 3, if you visit a non-existent URL such as `http://0.0.0.0:8080/ui/asdsaf` or `http://0.0.0.0:8080/asdasfgf`, the server responds with a 200 status code and renders the main UI shell, even though the page does not exist. This is because the new Vite-based React frontend is a single-page application (SPA) that handles routing client-side, and the backend is configured to serve the UI shell for any unknown path under the UI root.

### Why is this a problem?

- **False Positives:** You cannot distinguish between a real, registered plugin route and a typo or missing route, since both return 200 and show the UI shell.
- **Debugging Difficulty:** When developing FastAPI plugin apps, it is hard to know if your app is not registered, or if your code has a bug, or if you are simply hitting a wrong URL. The UI always loads, masking 404 errors.
- **User Confusion:** Users may think a page exists when it does not, because there is no 404 error or clear feedback.

### Impact on Plugin Development

If your FastAPI app is not registered correctly, or if you mistype the `url_prefix`, you will not get a 404 or error page. Instead, Airflow will serve the main UI shell, making it hard to debug plugin registration issues.

### Workarounds

- **Check the Network Tab:** Use browser developer tools to inspect the network response. If the response is the main UI HTML, your route is not registered.
- **Log Plugin Registration:** Add logging in your plugin to confirm that your FastAPI app is being registered and mounted at the expected prefix.
- **Test API Endpoints Directly:** For API routes, use `curl` or Postman to check for expected responses and status codes.

### Recommendation

Be aware of this behavior when developing plugins for Airflow 3. Always verify plugin registration and route availability using logs and direct API calls, not just by visiting URLs in the browser.

# Airflow 3 Plugin Compatibility Warning: What It Means and What To Do

## Why Do You See This Warning?


When you see a warning like:

```
Plugin 'your_plugin' may not be compatible with the current Airflow version. Please contact the author of the plugin.
```

it means your plugin is using legacy attributes (`admin_views`, `menu_links`, `appbuilder_views`, `appbuilder_menu_items`) or is missing required FastAPI plugin metadata. Airflow 3+ has moved away from Flask and Flask AppBuilder (FAB) for new plugins. The system now expects plugins to use FastAPI-based integration via the `fastapi_apps` attribute, not any Flask or FAB attributes. Using FAB attributes in new plugins will cause warnings or errors, and may require the FAB provider to be installed (which is not recommended for new development).


## Why Is This Change Needed?

- **Modernization:** Airflow 3 has moved away from Flask-Admin and Flask AppBuilder for new plugin development, using a React/TypeScript UI and FastAPI backend instead.
- **Consistency:** FastAPI-based plugins are more consistent, future-proof, and integrate better with the new Airflow 3 architecture.
- **Deprecation:** Old attributes (`admin_views`, `menu_links`, `appbuilder_views`, `appbuilder_menu_items`) are deprecated and may be removed in future versions. Relying on them will cause warnings or errors.


## What Should Plugin Authors and Users Do?

- **For new plugins (Airflow 3+):**
  - Only use the `fastapi_apps` attribute in your plugin class.
  - Do NOT use any Flask or FAB attributes (`admin_views`, `menu_links`, `appbuilder_views`, `appbuilder_menu_items`).
  - Each entry in `fastapi_apps` must be a dict with at least `app`, `url_prefix`, and `name` keys.
  - Example:
    ```python
    from airflow.plugins_manager import AirflowPlugin
    from fastapi import FastAPI

    my_app = FastAPI()


    class MyPlugin(AirflowPlugin):
        name = "my_fastapi_plugin"
        fastapi_apps = [{"app": my_app, "url_prefix": "/my-plugin", "name": "My FastAPI Plugin"}]
    ```
- **Test on Airflow 3:**
  - Make sure your plugin works as expected in Airflow 3.
  - Check for any other deprecation warnings or errors.
- **Contact the Author:**
  - If you are not the author, contact the maintainer and ask them to update the plugin for Airflow 3 compatibility.


## Example: FastAPI Plugin Structure (Airflow 3+)

```python
from airflow.plugins_manager import AirflowPlugin
from fastapi import FastAPI

my_app = FastAPI()


class MyPlugin(AirflowPlugin):
    name = "my_fastapi_plugin"
    fastapi_apps = [{"app": my_app, "url_prefix": "/my-plugin", "name": "My FastAPI Plugin"}]
```


## Summary

- The warning means your plugin is using deprecated or unsupported features for Airflow 3.
- For new plugins, use only FastAPI-based integration with the `fastapi_apps` attribute.
- Do not use any Flask or FAB attributes in new plugins. If you use them, you may see errors or be required to install the FAB provider, which is not recommended for new development.
- This ensures your plugin will work and be supported in future Airflow releases.
