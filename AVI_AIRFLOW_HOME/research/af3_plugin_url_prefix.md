<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

# Understanding `url_prefix` in Airflow 3 FastAPI Plugin Apps

## What is `url_prefix`?

In Airflow 3, plugins can extend the core REST API by registering their own FastAPI applications. When doing so, each FastAPI app must be provided with a `url_prefix`—a string that defines the base URL path under which the app will be accessible in the Airflow webserver.

For example, if you register a FastAPI app with `url_prefix: "/my_plugin"`, all endpoints in that app will be available under `/my_plugin/*` in the Airflow webserver.

## Why is `url_prefix` Needed?

- **Namespace Isolation:** It prevents route conflicts between the core Airflow API and plugin APIs by ensuring each plugin's endpoints are grouped under a unique path.
- **Routing:** Airflow uses the `url_prefix` to mount the FastAPI app at the correct location in the main application.
- **Required by Airflow:** If the `url_prefix` is missing, Airflow cannot mount the plugin app, resulting in errors like `'url_prefix' key is missing for the fastapi app: <plugin_name>`.

## How to Use `url_prefix` in a Plugin

When defining a plugin, you must provide a dictionary for each FastAPI app in the `fastapi_apps` list. This dictionary must include:
- `app`: The FastAPI app instance
- `url_prefix`: The base path (string, must start with `/`)
- `name`: A name for the app

### Example
```python
from airflow.plugins_manager import AirflowPlugin
from fastapi import FastAPI

app = FastAPI()


@app.get("/")
async def root():
    return {"message": "Hello from plugin!"}


app_with_metadata = {"app": app, "url_prefix": "/my_plugin", "name": "MyPluginApp"}  # Required!


class MyPlugin(AirflowPlugin):
    name = "my_plugin"
    fastapi_apps = [app_with_metadata]
```

## What Happens if `url_prefix` is Missing?
If you omit `url_prefix`, Airflow will log an error and your FastAPI app will not be registered:
```
ERROR - 'url_prefix' key is missing for the fastapi app: <plugin_name>
```

## Best Practices
- Always use a unique `url_prefix` for each plugin app to avoid conflicts.
- The `url_prefix` should start with a `/` and be descriptive of your plugin's purpose.
- Document the endpoints your plugin exposes under its prefix.

## References
- Airflow documentation: [Plugins](airflow-core/docs/administration-and-deployment/plugins.rst)
- Example from docs:
  ```python
  app_with_metadata = {"app": app, "url_prefix": "/some_prefix", "name": "Name of the App"}
  ```

---
This document explains the importance and usage of `url_prefix` for FastAPI plugin apps in Airflow 3. Always include it to ensure your plugin is loaded and accessible.
