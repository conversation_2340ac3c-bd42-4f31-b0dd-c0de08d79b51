<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

<chatName="SQLAlchemy 2.x, PEP 604, Python 3.9, and mypy/ruff compatibility: No Optional, No type aliases, No UP007 violation"/>

## Problem Recap

- You want to use SQLAlchemy 2.x ORM typing (`Mapped[...] = mapped_column(...)`).
- You want to use PEP 604 union types (`int | None`) in model annotations.
- You must support Python 3.9 (which does **not** support `int | None` at runtime).
- You **do not** want to use `Optional[...]` (ruff UP007 is enforced).
- You **do not** want to use version-dependent type aliases.
- You have `from __future__ import annotations` everywhere.
- You are getting runtime errors from SQLAlchemy and mypy/ruff errors, e.g.:
  ```
  sqlalchemy.orm.exc.MappedAnnotationError: Could not resolve all types within mapped annotation: "Mapped[int | None]"
  TypeError: unsupported operand type(s) for |: 'type' and 'NoneType'
  ```
- mypy/ruff are not happy with `Mapped[Optional[int]]` (ruff UP007), and SQLAlchemy 2.x cannot parse `Mapped[int | None]` on Python 3.9.

---

## Why This Is a Deadlock

- **PEP 604 (`int | None`)** is not valid in Python 3.9 at runtime, even with `from __future__ import annotations` (because SQLAlchemy tries to `eval()` the string).
- **`Optional[int]`** is not allowed by ruff UP007 (which enforces PEP 604).
- **Type aliases** (e.g., `IntOrNone`) are not a solution, because SQLAlchemy's annotation parser cannot resolve them reliably, and they are also not ruff/mypy friendly.
- **String annotations** (e.g., `Mapped["int | None"]`) are not supported by SQLAlchemy's annotation parser.
- **You cannot disable ruff UP007** (by your instruction).

---

## What Does SQLAlchemy 2.x Actually Do?

- With `from __future__ import annotations`, SQLAlchemy stores the annotation as a string.
- At runtime, it tries to `eval()` the string in the module's namespace.
- On Python 3.9, `eval("int | None")` fails with `TypeError: unsupported operand type(s) for |: 'type' and 'NoneType'`.
- This is a **hard runtime error** and cannot be worked around by imports, type aliases, or stringification.

---

## What About mypy/ruff?

- mypy and ruff (with UP007) want you to use PEP 604 everywhere.
- But SQLAlchemy 2.x cannot support PEP 604 on Python 3.9 for mapped columns.

---

## **There Is No Solution That Satisfies All Constraints**

### You cannot have:
- PEP 604 (`int | None`) in mapped column annotations
- Python 3.9 support
- ruff UP007 enabled (no `Optional[...]`)
- No type aliases

**at the same time.**

---

## **What Are Your Options?**

### 1. **Drop Python 3.9 Support**
- If you drop Python 3.9 and require Python 3.10+, you can use PEP 604 everywhere, and ruff/mypy/SQLAlchemy will all be happy.
- This is the only way to have `Mapped[int | None]` everywhere and pass all static/runtime checks.

### 2. **Disable ruff UP007 for model files**
- If you keep Python 3.9, you must use `Optional[...]` in mapped column annotations.
- You can use PEP 604 everywhere else.
- You can disable ruff UP007 for model files only (via `# noqa: UP007` or per-file config).
- This is the only way to have Python 3.9 support and working SQLAlchemy 2.x models.

### 3. **Accept Partial Lint/Type-Check Failures**
- If you insist on both Python 3.9 and ruff UP007, you must accept that either:
  - SQLAlchemy will fail at runtime (with PEP 604 in mapped columns), or
  - ruff will fail at lint time (with `Optional[...]` in mapped columns).

### 4. **Custom SQLAlchemy Patch (Not Recommended)**
- You could patch SQLAlchemy's annotation parser to handle PEP 604 as a string on Python 3.9, but this is a deep hack and not maintainable.

---

## **Implementation Plan (If You Must Support Python 3.9 and ruff UP007)**

### **A. Use PEP 604 (`int | None`) in all non-ORM code.**
- This is fine for function signatures, dataclasses, etc.

### **B. For SQLAlchemy ORM mapped columns:**
- You must use `Optional[...]` for Python 3.9 support.
- Add `# noqa: UP007` to each mapped column annotation line, or disable UP007 for model files in your ruff config.

**Example:**
```python
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional


class MyModel(Base):
    my_field: Mapped[Optional[int]] = mapped_column(Integer)  # noqa: UP007
```

### **C. Document This in Your Codebase**
- Add a comment at the top of each model file:
  ```
  # NOTE: We use Optional[...] in mapped column annotations for SQLAlchemy 2.x compatibility with Python 3.9.
  # ruff UP007 is disabled for this file. Do not change to PEP 604 until Python 3.10+ is required.
  ```

### **D. When You Drop Python 3.9 Support**
- Remove the `# noqa: UP007` comments or per-file disables.
- Change all `Optional[...]` to PEP 604 (`int | None`).

---

## **Summary Table**

| Constraint                | PEP 604 in mapped columns | Optional[...] in mapped columns | ruff UP007 | Python 3.9 | SQLAlchemy 2.x |
|---------------------------|:------------------------:|:------------------------------:|:----------:|:----------:|:--------------:|
| All enabled               | ❌ (runtime error)        | ❌ (ruff error)                | ✅         | ✅         | ✅             |
| UP007 disabled in models  | ✅ (in non-ORM code)      | ✅ (in ORM code)               | 🚫         | ✅         | ✅             |
| Python 3.10+ only         | ✅                       | ✅                             | ✅         | 🚫         | ✅             |

---

## **Architectural Decision**

- **You must choose between Python 3.9 support and ruff UP007 enforcement in model files.**
- There is no technical solution that allows both PEP 604 in mapped columns and Python 3.9 support with ruff UP007 enabled.

---

## **References**

- [SQLAlchemy 2.0 ORM Typing Docs](https://docs.sqlalchemy.org/en/20/orm/declarative_tables.html#typing-orm-mapped-attributes)
- [SQLAlchemy Issue: Could not de-stringify annotation 'int | None'](https://github.com/sqlalchemy/sqlalchemy/issues/10041)
- [ruff UP007 docs](https://docs.astral.sh/ruff/rules/pyupgrade/)

---

## **Conclusion**

**You must either:**
- Use `Optional[...]` in mapped columns and disable ruff UP007 for model files, or
- Drop Python 3.9 support and use PEP 604 everywhere.

**There is no other way.**

---

**If you want a concrete ruff config snippet or code comment template for this, let me know.**
