<!--
 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.
 -->

# Common SQLAlchemy 2.0 + mypy Type Issues and Explanations

Here's a breakdown of typical mypy errors seen when migrating to SQLAlchemy 2.0+ with type annotations, especially in Airflow models:

---

## 1. Missing Type Annotations for ORM Columns
```
airflow-core/src/airflow/models/taskinstancehistory.py:95: error: Need type annotation for "executor_config"  [var-annotated]
airflow-core/src/airflow/models/taskinstancehistory.py:108: error: Need type annotation for "dag_version_id"  [var-annotated]
```
**Explanation:**
- mypy requires explicit type annotations for class variables, especially when using SQLAlchemy 2.0+.
- **Solution:** Use the new style:
  ```python
  executor_config: Mapped[Any] = mapped_column(ExecutorConfigType(pickler=dill))
  dag_version_id: Mapped[str] = mapped_column(UUIDType(binary=False))
  ```
- The old `executor_config = Column(...)` style is not type-annotated, causing this error.

---

## 2. Type Mismatch in Assignments
```
airflow-core/src/airflow/models/taskinstancehistory.py:139: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")
                self.state = state
```
**Explanation:**
- `self.state` is a SQLAlchemy column, not a regular instance variable.
- Assigning a plain value to a column attribute is fine for SQLAlchemy, but mypy expects a `Column[str]` type, not a `str`.
- **Solution:** Use SQLAlchemy 2.0+ typing (`Mapped[str] = mapped_column(...)`) for columns, and mypy will understand instance assignments.

---

## 3. TypeVar and Optional Issues
```
airflow-core/src/airflow/models/taskinstance.py:301: error: Value of type variable "SupportsRichComparisonT" of "max" cannot be "Optional[int]" [type-var]
                    ti.max_tries = max(ti.max_tries, ti.try_number)
```
**Explanation:**
- `max()` expects non-optional arguments, but either `ti.max_tries` or `ti.try_number` could be `None`.
- **Solution:** Ensure both are not `None` before calling `max`, or provide a default value.

---

## 4. SQLAlchemy Expression Type Mismatches
```
airflow-core/src/airflow/models/taskinstance.py:317: error: Argument 1 to "or_" has incompatible type "Generator[ColumnElement[bool], None, None]"; expected ...
```
**Explanation:**
- SQLAlchemy expects a sequence of boolean expressions, but a generator is being passed.
- **Solution:** Convert the generator to a list or tuple: `or_(*list(generator))`.

---

## 5. Assignment to Column Objects
```
airflow-core/src/airflow/models/taskinstance.py:328: error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[Any]")
                    dr.start_date = timezone.utcnow()
```
**Explanation:**
- `dr.start_date` is a SQLAlchemy column, but mypy thinks it's a `Column[Any]` (old style).
- **Solution:** Use `Mapped[datetime | None] = mapped_column(...)` for columns, so mypy knows instance assignments are valid.

---

## 6. List Append Type Mismatches
```
airflow-core/src/airflow/models/taskinstance.py:529: error: Argument 1 to "append" of "list" has incompatible type "int"; expected "str"  [arg-type]
            params.append(task_instance.map_index)
```
**Explanation:**
- The list is expected to hold `str`, but an `int` is being appended.
- **Solution:** Convert or ensure types match, e.g., `params.append(str(task_instance.map_index))`.

---

## 7. Relationship and Assignment Type Issues
```
airflow-core/src/airflow/models/taskinstance.py:657: error: Incompatible types in assignment (expression has type "_RelationshipDeclared[Any]", variable has type "DagModel")  [assignment]
        dag_model: DagModel = relationship(
                              ^
```
**Explanation:**
- `relationship()` returns a special descriptor, not a `DagModel` instance.
- **Solution:** Remove the type annotation or use `Mapped[DagModel] = relationship(...)` if using SQLAlchemy 2.0+.

---

## 8. Optional Type Operations
```
airflow-core/src/airflow/models/taskinstance.py:1536: error: Unsupported operand types for + ("None" and "int")  [operator]
                        ti.max_tries + 1,
                        ^
```
**Explanation:**
- `ti.max_tries` might be `None`, so `None + 1` is invalid.
- **Solution:** Ensure `max_tries` is not `None` before using it, or provide a default.

---

## 9. Argument Type Errors in Method Calls
```
airflow-core/src/airflow/models/taskinstance.py:1243: error: Argument "session" to "get_dagrun" of "TaskInstance" has incompatible type "Optional[Session]"; expected "Session"  [arg-type]
```
**Explanation:**
- The function expects a non-optional `Session`, but an `Optional[Session]` is being passed.
- **Solution:** Make sure to pass a guaranteed `Session` object.

---

## 10. General SQLAlchemy/Mypy Compatibility
- Many errors are due to mixing old-style `Column` assignments with new-style typing, or not using the right types for SQLAlchemy expressions (e.g., `ColumnElement`, `BooleanClauseList`, etc.).
- **Solution:** Fully migrate to SQLAlchemy 2.0+ typing (`Mapped[]`, `mapped_column()`) and use correct types in expressions and assignments.

---

## Summary Table

| Error Type                    | Example/Error | Cause | Solution |
|-------------------------------|--------------|-------|----------|
| Missing type annotation       | `Need type annotation for ...` | Old-style `Column()` | Use `Mapped[] = mapped_column()` |
| Assignment type mismatch      | `Incompatible types in assignment` | mypy thinks attribute is a `Column`, not a value | Use SQLA 2.0+ style |
| Optional type issues          | `Operand types for + ("None" and "int")` | Possible `None` value | Check for `None` or provide default |
| SQLAlchemy expression types   | `Argument to "or_" has incompatible type ...` | Wrong iterable type | Use `*list(generator)` or correct types |
| Relationship typing           | `relationship(...)` returns a descriptor | Use `Mapped[DagModel] = relationship(...)` or no annotation |

---

If you want to fix a specific error, let me know which one and I'll walk you through the fix!
