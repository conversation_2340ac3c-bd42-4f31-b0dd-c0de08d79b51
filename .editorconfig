#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
root = true

[*]
end_of_line = lf
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

[*.py]
indent_size = 4
max_line_length = 110

[*.sh]
indent_size = 4

[*.sql]
indent_size = 4

[*.js]
indent_size = 2

[*.ts]
indent_size = 2

[*.tsx]
indent_size = 2

[*.css]
indent_size = 2

[*.{md,rst}]
indent_size = 2

[*.{yml,yaml}]
indent_size = 2
max_line_length = 110

[*.{htm,html}]
indent_size = 2

[*.json]
indent_size = 4

[*.go]
indent_style = tab
max_line_length = 110
