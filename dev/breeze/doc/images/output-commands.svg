<svg class="rich-terminal" viewBox="0 0 1482 2148.4" xmlns="http://www.w3.org/2000/svg">
    <!-- Generated with <PERSON> https://www.textualize.io -->
    <style>

    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Regular"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Regular.woff") format("woff");
        font-style: normal;
        font-weight: 400;
    }
    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Bold"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Bold.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Bold.woff") format("woff");
        font-style: bold;
        font-weight: 700;
    }

    .breeze-help-matrix {
        font-family: Fira Code, monospace;
        font-size: 20px;
        line-height: 24.4px;
        font-variant-east-asian: full-width;
    }

    .breeze-help-title {
        font-size: 18px;
        font-weight: bold;
        font-family: arial;
    }

    .breeze-help-r1 { fill: #c5c8c6 }
.breeze-help-r2 { fill: #d0b344 }
.breeze-help-r3 { fill: #c5c8c6;font-weight: bold }
.breeze-help-r4 { fill: #68a0b3;font-weight: bold }
.breeze-help-r5 { fill: #868887 }
.breeze-help-r6 { fill: #98a84b;font-weight: bold }
.breeze-help-r7 { fill: #8d7b39 }
    </style>

    <defs>
    <clipPath id="breeze-help-clip-terminal">
      <rect x="0" y="0" width="1463.0" height="2097.4" />
    </clipPath>
    <clipPath id="breeze-help-line-0">
    <rect x="0" y="1.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-1">
    <rect x="0" y="25.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-2">
    <rect x="0" y="50.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-3">
    <rect x="0" y="74.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-4">
    <rect x="0" y="99.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-5">
    <rect x="0" y="123.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-6">
    <rect x="0" y="147.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-7">
    <rect x="0" y="172.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-8">
    <rect x="0" y="196.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-9">
    <rect x="0" y="221.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-10">
    <rect x="0" y="245.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-11">
    <rect x="0" y="269.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-12">
    <rect x="0" y="294.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-13">
    <rect x="0" y="318.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-14">
    <rect x="0" y="343.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-15">
    <rect x="0" y="367.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-16">
    <rect x="0" y="391.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-17">
    <rect x="0" y="416.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-18">
    <rect x="0" y="440.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-19">
    <rect x="0" y="465.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-20">
    <rect x="0" y="489.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-21">
    <rect x="0" y="513.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-22">
    <rect x="0" y="538.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-23">
    <rect x="0" y="562.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-24">
    <rect x="0" y="587.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-25">
    <rect x="0" y="611.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-26">
    <rect x="0" y="635.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-27">
    <rect x="0" y="660.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-28">
    <rect x="0" y="684.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-29">
    <rect x="0" y="709.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-30">
    <rect x="0" y="733.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-31">
    <rect x="0" y="757.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-32">
    <rect x="0" y="782.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-33">
    <rect x="0" y="806.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-34">
    <rect x="0" y="831.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-35">
    <rect x="0" y="855.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-36">
    <rect x="0" y="879.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-37">
    <rect x="0" y="904.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-38">
    <rect x="0" y="928.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-39">
    <rect x="0" y="953.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-40">
    <rect x="0" y="977.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-41">
    <rect x="0" y="1001.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-42">
    <rect x="0" y="1026.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-43">
    <rect x="0" y="1050.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-44">
    <rect x="0" y="1075.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-45">
    <rect x="0" y="1099.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-46">
    <rect x="0" y="1123.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-47">
    <rect x="0" y="1148.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-48">
    <rect x="0" y="1172.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-49">
    <rect x="0" y="1197.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-50">
    <rect x="0" y="1221.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-51">
    <rect x="0" y="1245.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-52">
    <rect x="0" y="1270.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-53">
    <rect x="0" y="1294.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-54">
    <rect x="0" y="1319.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-55">
    <rect x="0" y="1343.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-56">
    <rect x="0" y="1367.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-57">
    <rect x="0" y="1392.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-58">
    <rect x="0" y="1416.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-59">
    <rect x="0" y="1441.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-60">
    <rect x="0" y="1465.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-61">
    <rect x="0" y="1489.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-62">
    <rect x="0" y="1514.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-63">
    <rect x="0" y="1538.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-64">
    <rect x="0" y="1563.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-65">
    <rect x="0" y="1587.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-66">
    <rect x="0" y="1611.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-67">
    <rect x="0" y="1636.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-68">
    <rect x="0" y="1660.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-69">
    <rect x="0" y="1685.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-70">
    <rect x="0" y="1709.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-71">
    <rect x="0" y="1733.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-72">
    <rect x="0" y="1758.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-73">
    <rect x="0" y="1782.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-74">
    <rect x="0" y="1807.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-75">
    <rect x="0" y="1831.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-76">
    <rect x="0" y="1855.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-77">
    <rect x="0" y="1880.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-78">
    <rect x="0" y="1904.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-79">
    <rect x="0" y="1929.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-80">
    <rect x="0" y="1953.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-81">
    <rect x="0" y="1977.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-82">
    <rect x="0" y="2002.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-83">
    <rect x="0" y="2026.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-help-line-84">
    <rect x="0" y="2051.1" width="1464" height="24.65"/>
            </clipPath>
    </defs>

    <rect fill="#292929" stroke="rgba(255,255,255,0.35)" stroke-width="1" x="1" y="1" width="1480" height="2146.4" rx="8"/><text class="breeze-help-title" fill="#c5c8c6" text-anchor="middle" x="740" y="27">Breeze&#160;commands</text>
            <g transform="translate(26,22)">
            <circle cx="0" cy="0" r="7" fill="#ff5f57"/>
            <circle cx="22" cy="0" r="7" fill="#febc2e"/>
            <circle cx="44" cy="0" r="7" fill="#28c840"/>
            </g>
        
    <g transform="translate(9, 41)" clip-path="url(#breeze-help-clip-terminal)">
    
    <g class="breeze-help-matrix">
    <text class="breeze-help-r1" x="1464" y="20" textLength="12.2" clip-path="url(#breeze-help-line-0)">
</text><text class="breeze-help-r2" x="12.2" y="44.4" textLength="73.2" clip-path="url(#breeze-help-line-1)">Usage:</text><text class="breeze-help-r3" x="97.6" y="44.4" textLength="73.2" clip-path="url(#breeze-help-line-1)">breeze</text><text class="breeze-help-r1" x="183" y="44.4" textLength="12.2" clip-path="url(#breeze-help-line-1)">[</text><text class="breeze-help-r4" x="195.2" y="44.4" textLength="85.4" clip-path="url(#breeze-help-line-1)">OPTIONS</text><text class="breeze-help-r1" x="280.6" y="44.4" textLength="24.4" clip-path="url(#breeze-help-line-1)">]&#160;</text><text class="breeze-help-r4" x="305" y="44.4" textLength="85.4" clip-path="url(#breeze-help-line-1)">COMMAND</text><text class="breeze-help-r1" x="390.4" y="44.4" textLength="24.4" clip-path="url(#breeze-help-line-1)">&#160;[</text><text class="breeze-help-r4" x="414.8" y="44.4" textLength="48.8" clip-path="url(#breeze-help-line-1)">ARGS</text><text class="breeze-help-r1" x="463.6" y="44.4" textLength="48.8" clip-path="url(#breeze-help-line-1)">]...</text><text class="breeze-help-r1" x="1464" y="44.4" textLength="12.2" clip-path="url(#breeze-help-line-1)">
</text><text class="breeze-help-r1" x="1464" y="68.8" textLength="12.2" clip-path="url(#breeze-help-line-2)">
</text><text class="breeze-help-r5" x="0" y="93.2" textLength="24.4" clip-path="url(#breeze-help-line-3)">╭─</text><text class="breeze-help-r5" x="24.4" y="93.2" textLength="195.2" clip-path="url(#breeze-help-line-3)">&#160;Execution&#160;mode&#160;</text><text class="breeze-help-r5" x="219.6" y="93.2" textLength="1220" clip-path="url(#breeze-help-line-3)">────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="93.2" textLength="24.4" clip-path="url(#breeze-help-line-3)">─╮</text><text class="breeze-help-r1" x="1464" y="93.2" textLength="12.2" clip-path="url(#breeze-help-line-3)">
</text><text class="breeze-help-r5" x="0" y="117.6" textLength="12.2" clip-path="url(#breeze-help-line-4)">│</text><text class="breeze-help-r4" x="24.4" y="117.6" textLength="97.6" clip-path="url(#breeze-help-line-4)">--python</text><text class="breeze-help-r6" x="719.8" y="117.6" textLength="24.4" clip-path="url(#breeze-help-line-4)">-p</text><text class="breeze-help-r1" x="768.6" y="117.6" textLength="671" clip-path="url(#breeze-help-line-4)">Python&#160;major/minor&#160;version&#160;used&#160;in&#160;Airflow&#160;image&#160;for&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="117.6" textLength="12.2" clip-path="url(#breeze-help-line-4)">│</text><text class="breeze-help-r1" x="1464" y="117.6" textLength="12.2" clip-path="url(#breeze-help-line-4)">
</text><text class="breeze-help-r5" x="0" y="142" textLength="12.2" clip-path="url(#breeze-help-line-5)">│</text><text class="breeze-help-r1" x="768.6" y="142" textLength="671" clip-path="url(#breeze-help-line-5)">images.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="142" textLength="12.2" clip-path="url(#breeze-help-line-5)">│</text><text class="breeze-help-r1" x="1464" y="142" textLength="12.2" clip-path="url(#breeze-help-line-5)">
</text><text class="breeze-help-r5" x="0" y="166.4" textLength="12.2" clip-path="url(#breeze-help-line-6)">│</text><text class="breeze-help-r7" x="768.6" y="166.4" textLength="671" clip-path="url(#breeze-help-line-6)">(&gt;3.9&lt;&#160;|&#160;3.10&#160;|&#160;3.11&#160;|&#160;3.12)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="166.4" textLength="12.2" clip-path="url(#breeze-help-line-6)">│</text><text class="breeze-help-r1" x="1464" y="166.4" textLength="12.2" clip-path="url(#breeze-help-line-6)">
</text><text class="breeze-help-r5" x="0" y="190.8" textLength="12.2" clip-path="url(#breeze-help-line-7)">│</text><text class="breeze-help-r5" x="768.6" y="190.8" textLength="671" clip-path="url(#breeze-help-line-7)">[default:&#160;3.9]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="190.8" textLength="12.2" clip-path="url(#breeze-help-line-7)">│</text><text class="breeze-help-r1" x="1464" y="190.8" textLength="12.2" clip-path="url(#breeze-help-line-7)">
</text><text class="breeze-help-r5" x="0" y="215.2" textLength="12.2" clip-path="url(#breeze-help-line-8)">│</text><text class="breeze-help-r4" x="24.4" y="215.2" textLength="158.6" clip-path="url(#breeze-help-line-8)">--integration</text><text class="breeze-help-r1" x="768.6" y="215.2" textLength="671" clip-path="url(#breeze-help-line-8)">Core&#160;Integrations&#160;to&#160;enable&#160;when&#160;running&#160;(can&#160;be&#160;more&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="215.2" textLength="12.2" clip-path="url(#breeze-help-line-8)">│</text><text class="breeze-help-r1" x="1464" y="215.2" textLength="12.2" clip-path="url(#breeze-help-line-8)">
</text><text class="breeze-help-r5" x="0" y="239.6" textLength="12.2" clip-path="url(#breeze-help-line-9)">│</text><text class="breeze-help-r1" x="768.6" y="239.6" textLength="671" clip-path="url(#breeze-help-line-9)">than&#160;one).&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="239.6" textLength="12.2" clip-path="url(#breeze-help-line-9)">│</text><text class="breeze-help-r1" x="1464" y="239.6" textLength="12.2" clip-path="url(#breeze-help-line-9)">
</text><text class="breeze-help-r5" x="0" y="264" textLength="12.2" clip-path="url(#breeze-help-line-10)">│</text><text class="breeze-help-r7" x="768.6" y="264" textLength="671" clip-path="url(#breeze-help-line-10)">(all&#160;|&#160;all-testable&#160;|&#160;cassandra&#160;|&#160;celery&#160;|&#160;drill&#160;|&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="264" textLength="12.2" clip-path="url(#breeze-help-line-10)">│</text><text class="breeze-help-r1" x="1464" y="264" textLength="12.2" clip-path="url(#breeze-help-line-10)">
</text><text class="breeze-help-r5" x="0" y="288.4" textLength="12.2" clip-path="url(#breeze-help-line-11)">│</text><text class="breeze-help-r7" x="768.6" y="288.4" textLength="671" clip-path="url(#breeze-help-line-11)">gremlin&#160;|&#160;kafka&#160;|&#160;kerberos&#160;|&#160;keycloak&#160;|&#160;mongo&#160;|&#160;mssql&#160;|</text><text class="breeze-help-r5" x="1451.8" y="288.4" textLength="12.2" clip-path="url(#breeze-help-line-11)">│</text><text class="breeze-help-r1" x="1464" y="288.4" textLength="12.2" clip-path="url(#breeze-help-line-11)">
</text><text class="breeze-help-r5" x="0" y="312.8" textLength="12.2" clip-path="url(#breeze-help-line-12)">│</text><text class="breeze-help-r7" x="768.6" y="312.8" textLength="671" clip-path="url(#breeze-help-line-12)">openlineage&#160;|&#160;otel&#160;|&#160;pinot&#160;|&#160;qdrant&#160;|&#160;redis&#160;|&#160;statsd&#160;|&#160;</text><text class="breeze-help-r5" x="1451.8" y="312.8" textLength="12.2" clip-path="url(#breeze-help-line-12)">│</text><text class="breeze-help-r1" x="1464" y="312.8" textLength="12.2" clip-path="url(#breeze-help-line-12)">
</text><text class="breeze-help-r5" x="0" y="337.2" textLength="12.2" clip-path="url(#breeze-help-line-13)">│</text><text class="breeze-help-r7" x="768.6" y="337.2" textLength="671" clip-path="url(#breeze-help-line-13)">trino&#160;|&#160;ydb)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="337.2" textLength="12.2" clip-path="url(#breeze-help-line-13)">│</text><text class="breeze-help-r1" x="1464" y="337.2" textLength="12.2" clip-path="url(#breeze-help-line-13)">
</text><text class="breeze-help-r5" x="0" y="361.6" textLength="12.2" clip-path="url(#breeze-help-line-14)">│</text><text class="breeze-help-r4" x="24.4" y="361.6" textLength="317.2" clip-path="url(#breeze-help-line-14)">--standalone-dag-processor</text><text class="breeze-help-r1" x="341.6" y="361.6" textLength="12.2" clip-path="url(#breeze-help-line-14)">/</text><text class="breeze-help-r4" x="353.8" y="361.6" textLength="341.6" clip-path="url(#breeze-help-line-14)">--no-standalone-dag-processo</text><text class="breeze-help-r1" x="768.6" y="361.6" textLength="671" clip-path="url(#breeze-help-line-14)">Run&#160;standalone&#160;dag&#160;processor&#160;for&#160;start-airflow&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="361.6" textLength="12.2" clip-path="url(#breeze-help-line-14)">│</text><text class="breeze-help-r1" x="1464" y="361.6" textLength="12.2" clip-path="url(#breeze-help-line-14)">
</text><text class="breeze-help-r5" x="0" y="386" textLength="12.2" clip-path="url(#breeze-help-line-15)">│</text><text class="breeze-help-r4" x="24.4" y="386" textLength="12.2" clip-path="url(#breeze-help-line-15)">r</text><text class="breeze-help-r1" x="768.6" y="386" textLength="671" clip-path="url(#breeze-help-line-15)">(required&#160;for&#160;Airflow&#160;3).&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="386" textLength="12.2" clip-path="url(#breeze-help-line-15)">│</text><text class="breeze-help-r1" x="1464" y="386" textLength="12.2" clip-path="url(#breeze-help-line-15)">
</text><text class="breeze-help-r5" x="0" y="410.4" textLength="12.2" clip-path="url(#breeze-help-line-16)">│</text><text class="breeze-help-r5" x="768.6" y="410.4" textLength="671" clip-path="url(#breeze-help-line-16)">[default:&#160;standalone-dag-processor]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="410.4" textLength="12.2" clip-path="url(#breeze-help-line-16)">│</text><text class="breeze-help-r1" x="1464" y="410.4" textLength="12.2" clip-path="url(#breeze-help-line-16)">
</text><text class="breeze-help-r5" x="0" y="434.8" textLength="1464" clip-path="url(#breeze-help-line-17)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="434.8" textLength="12.2" clip-path="url(#breeze-help-line-17)">
</text><text class="breeze-help-r5" x="0" y="459.2" textLength="24.4" clip-path="url(#breeze-help-line-18)">╭─</text><text class="breeze-help-r5" x="24.4" y="459.2" textLength="463.6" clip-path="url(#breeze-help-line-18)">&#160;Docker&#160;Compose&#160;selection&#160;and&#160;cleanup&#160;</text><text class="breeze-help-r5" x="488" y="459.2" textLength="951.6" clip-path="url(#breeze-help-line-18)">──────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="459.2" textLength="24.4" clip-path="url(#breeze-help-line-18)">─╮</text><text class="breeze-help-r1" x="1464" y="459.2" textLength="12.2" clip-path="url(#breeze-help-line-18)">
</text><text class="breeze-help-r5" x="0" y="483.6" textLength="12.2" clip-path="url(#breeze-help-line-19)">│</text><text class="breeze-help-r4" x="24.4" y="483.6" textLength="170.8" clip-path="url(#breeze-help-line-19)">--project-name</text><text class="breeze-help-r1" x="244" y="483.6" textLength="1195.6" clip-path="url(#breeze-help-line-19)">Name&#160;of&#160;the&#160;docker-compose&#160;project&#160;to&#160;bring&#160;down.&#160;The&#160;`docker-compose`&#160;is&#160;for&#160;legacy&#160;breeze&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="483.6" textLength="12.2" clip-path="url(#breeze-help-line-19)">│</text><text class="breeze-help-r1" x="1464" y="483.6" textLength="12.2" clip-path="url(#breeze-help-line-19)">
</text><text class="breeze-help-r5" x="0" y="508" textLength="12.2" clip-path="url(#breeze-help-line-20)">│</text><text class="breeze-help-r1" x="244" y="508" textLength="512.4" clip-path="url(#breeze-help-line-20)">project&#160;name&#160;and&#160;you&#160;can&#160;use&#160;`breeze&#160;down&#160;</text><text class="breeze-help-r4" x="756.4" y="508" textLength="170.8" clip-path="url(#breeze-help-line-20)">--project-name</text><text class="breeze-help-r1" x="927.2" y="508" textLength="512.4" clip-path="url(#breeze-help-line-20)">&#160;docker-compose`&#160;to&#160;stop&#160;all&#160;containers&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="508" textLength="12.2" clip-path="url(#breeze-help-line-20)">│</text><text class="breeze-help-r1" x="1464" y="508" textLength="12.2" clip-path="url(#breeze-help-line-20)">
</text><text class="breeze-help-r5" x="0" y="532.4" textLength="12.2" clip-path="url(#breeze-help-line-21)">│</text><text class="breeze-help-r1" x="244" y="532.4" textLength="1195.6" clip-path="url(#breeze-help-line-21)">belonging&#160;to&#160;it.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="532.4" textLength="12.2" clip-path="url(#breeze-help-line-21)">│</text><text class="breeze-help-r1" x="1464" y="532.4" textLength="12.2" clip-path="url(#breeze-help-line-21)">
</text><text class="breeze-help-r5" x="0" y="556.8" textLength="12.2" clip-path="url(#breeze-help-line-22)">│</text><text class="breeze-help-r7" x="244" y="556.8" textLength="1195.6" clip-path="url(#breeze-help-line-22)">(breeze&#160;|&#160;pre-commit&#160;|&#160;docker-compose)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="556.8" textLength="12.2" clip-path="url(#breeze-help-line-22)">│</text><text class="breeze-help-r1" x="1464" y="556.8" textLength="12.2" clip-path="url(#breeze-help-line-22)">
</text><text class="breeze-help-r5" x="0" y="581.2" textLength="12.2" clip-path="url(#breeze-help-line-23)">│</text><text class="breeze-help-r5" x="244" y="581.2" textLength="1195.6" clip-path="url(#breeze-help-line-23)">[default:&#160;breeze]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="581.2" textLength="12.2" clip-path="url(#breeze-help-line-23)">│</text><text class="breeze-help-r1" x="1464" y="581.2" textLength="12.2" clip-path="url(#breeze-help-line-23)">
</text><text class="breeze-help-r5" x="0" y="605.6" textLength="12.2" clip-path="url(#breeze-help-line-24)">│</text><text class="breeze-help-r4" x="24.4" y="605.6" textLength="158.6" clip-path="url(#breeze-help-line-24)">--docker-host</text><text class="breeze-help-r1" x="244" y="605.6" textLength="915" clip-path="url(#breeze-help-line-24)">Optional&#160;-&#160;docker&#160;host&#160;to&#160;use&#160;when&#160;running&#160;docker&#160;commands.&#160;When&#160;set,&#160;the&#160;`</text><text class="breeze-help-r4" x="1159" y="605.6" textLength="109.8" clip-path="url(#breeze-help-line-24)">--builder</text><text class="breeze-help-r1" x="1268.8" y="605.6" textLength="170.8" clip-path="url(#breeze-help-line-24)">`&#160;option&#160;is&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="605.6" textLength="12.2" clip-path="url(#breeze-help-line-24)">│</text><text class="breeze-help-r1" x="1464" y="605.6" textLength="12.2" clip-path="url(#breeze-help-line-24)">
</text><text class="breeze-help-r5" x="0" y="630" textLength="12.2" clip-path="url(#breeze-help-line-25)">│</text><text class="breeze-help-r1" x="244" y="630" textLength="1195.6" clip-path="url(#breeze-help-line-25)">ignored&#160;when&#160;building&#160;images.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="630" textLength="12.2" clip-path="url(#breeze-help-line-25)">│</text><text class="breeze-help-r1" x="1464" y="630" textLength="12.2" clip-path="url(#breeze-help-line-25)">
</text><text class="breeze-help-r5" x="0" y="654.4" textLength="12.2" clip-path="url(#breeze-help-line-26)">│</text><text class="breeze-help-r7" x="244" y="654.4" textLength="1195.6" clip-path="url(#breeze-help-line-26)">(TEXT)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="654.4" textLength="12.2" clip-path="url(#breeze-help-line-26)">│</text><text class="breeze-help-r1" x="1464" y="654.4" textLength="12.2" clip-path="url(#breeze-help-line-26)">
</text><text class="breeze-help-r5" x="0" y="678.8" textLength="1464" clip-path="url(#breeze-help-line-27)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="678.8" textLength="12.2" clip-path="url(#breeze-help-line-27)">
</text><text class="breeze-help-r5" x="0" y="703.2" textLength="24.4" clip-path="url(#breeze-help-line-28)">╭─</text><text class="breeze-help-r5" x="24.4" y="703.2" textLength="122" clip-path="url(#breeze-help-line-28)">&#160;Database&#160;</text><text class="breeze-help-r5" x="146.4" y="703.2" textLength="1293.2" clip-path="url(#breeze-help-line-28)">──────────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="703.2" textLength="24.4" clip-path="url(#breeze-help-line-28)">─╮</text><text class="breeze-help-r1" x="1464" y="703.2" textLength="12.2" clip-path="url(#breeze-help-line-28)">
</text><text class="breeze-help-r5" x="0" y="727.6" textLength="12.2" clip-path="url(#breeze-help-line-29)">│</text><text class="breeze-help-r4" x="24.4" y="727.6" textLength="109.8" clip-path="url(#breeze-help-line-29)">--backend</text><text class="breeze-help-r6" x="268.4" y="727.6" textLength="24.4" clip-path="url(#breeze-help-line-29)">-b</text><text class="breeze-help-r1" x="488" y="727.6" textLength="951.6" clip-path="url(#breeze-help-line-29)">Database&#160;backend&#160;to&#160;use.&#160;If&#160;&#x27;none&#x27;&#160;is&#160;chosen,&#160;Breeze&#160;will&#160;start&#160;with&#160;an&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="727.6" textLength="12.2" clip-path="url(#breeze-help-line-29)">│</text><text class="breeze-help-r1" x="1464" y="727.6" textLength="12.2" clip-path="url(#breeze-help-line-29)">
</text><text class="breeze-help-r5" x="0" y="752" textLength="12.2" clip-path="url(#breeze-help-line-30)">│</text><text class="breeze-help-r1" x="488" y="752" textLength="951.6" clip-path="url(#breeze-help-line-30)">invalid&#160;database&#160;configuration,&#160;meaning&#160;there&#160;will&#160;be&#160;no&#160;database&#160;available,&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="752" textLength="12.2" clip-path="url(#breeze-help-line-30)">│</text><text class="breeze-help-r1" x="1464" y="752" textLength="12.2" clip-path="url(#breeze-help-line-30)">
</text><text class="breeze-help-r5" x="0" y="776.4" textLength="12.2" clip-path="url(#breeze-help-line-31)">│</text><text class="breeze-help-r1" x="488" y="776.4" textLength="951.6" clip-path="url(#breeze-help-line-31)">and&#160;any&#160;attempts&#160;to&#160;connect&#160;to&#160;the&#160;Airflow&#160;database&#160;will&#160;fail.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="776.4" textLength="12.2" clip-path="url(#breeze-help-line-31)">│</text><text class="breeze-help-r1" x="1464" y="776.4" textLength="12.2" clip-path="url(#breeze-help-line-31)">
</text><text class="breeze-help-r5" x="0" y="800.8" textLength="12.2" clip-path="url(#breeze-help-line-32)">│</text><text class="breeze-help-r7" x="488" y="800.8" textLength="951.6" clip-path="url(#breeze-help-line-32)">(&gt;sqlite&lt;&#160;|&#160;mysql&#160;|&#160;postgres&#160;|&#160;none)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="800.8" textLength="12.2" clip-path="url(#breeze-help-line-32)">│</text><text class="breeze-help-r1" x="1464" y="800.8" textLength="12.2" clip-path="url(#breeze-help-line-32)">
</text><text class="breeze-help-r5" x="0" y="825.2" textLength="12.2" clip-path="url(#breeze-help-line-33)">│</text><text class="breeze-help-r5" x="488" y="825.2" textLength="951.6" clip-path="url(#breeze-help-line-33)">[default:&#160;sqlite]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="825.2" textLength="12.2" clip-path="url(#breeze-help-line-33)">│</text><text class="breeze-help-r1" x="1464" y="825.2" textLength="12.2" clip-path="url(#breeze-help-line-33)">
</text><text class="breeze-help-r5" x="0" y="849.6" textLength="12.2" clip-path="url(#breeze-help-line-34)">│</text><text class="breeze-help-r4" x="24.4" y="849.6" textLength="219.6" clip-path="url(#breeze-help-line-34)">--postgres-version</text><text class="breeze-help-r6" x="268.4" y="849.6" textLength="24.4" clip-path="url(#breeze-help-line-34)">-P</text><text class="breeze-help-r1" x="488" y="849.6" textLength="305" clip-path="url(#breeze-help-line-34)">Version&#160;of&#160;Postgres&#160;used.</text><text class="breeze-help-r7" x="805.2" y="849.6" textLength="317.2" clip-path="url(#breeze-help-line-34)">(&gt;13&lt;&#160;|&#160;14&#160;|&#160;15&#160;|&#160;16&#160;|&#160;17)</text><text class="breeze-help-r5" x="1134.6" y="849.6" textLength="158.6" clip-path="url(#breeze-help-line-34)">[default:&#160;13]</text><text class="breeze-help-r5" x="1451.8" y="849.6" textLength="12.2" clip-path="url(#breeze-help-line-34)">│</text><text class="breeze-help-r1" x="1464" y="849.6" textLength="12.2" clip-path="url(#breeze-help-line-34)">
</text><text class="breeze-help-r5" x="0" y="874" textLength="12.2" clip-path="url(#breeze-help-line-35)">│</text><text class="breeze-help-r4" x="24.4" y="874" textLength="183" clip-path="url(#breeze-help-line-35)">--mysql-version</text><text class="breeze-help-r6" x="268.4" y="874" textLength="24.4" clip-path="url(#breeze-help-line-35)">-M</text><text class="breeze-help-r1" x="488" y="874" textLength="268.4" clip-path="url(#breeze-help-line-35)">Version&#160;of&#160;MySQL&#160;used.</text><text class="breeze-help-r7" x="768.6" y="874" textLength="158.6" clip-path="url(#breeze-help-line-35)">(&gt;8.0&lt;&#160;|&#160;8.4)</text><text class="breeze-help-r5" x="939.4" y="874" textLength="170.8" clip-path="url(#breeze-help-line-35)">[default:&#160;8.0]</text><text class="breeze-help-r5" x="1451.8" y="874" textLength="12.2" clip-path="url(#breeze-help-line-35)">│</text><text class="breeze-help-r1" x="1464" y="874" textLength="12.2" clip-path="url(#breeze-help-line-35)">
</text><text class="breeze-help-r5" x="0" y="898.4" textLength="12.2" clip-path="url(#breeze-help-line-36)">│</text><text class="breeze-help-r4" x="24.4" y="898.4" textLength="122" clip-path="url(#breeze-help-line-36)">--db-reset</text><text class="breeze-help-r6" x="268.4" y="898.4" textLength="24.4" clip-path="url(#breeze-help-line-36)">-d</text><text class="breeze-help-r1" x="292.8" y="898.4" textLength="12.2" clip-path="url(#breeze-help-line-36)">/</text><text class="breeze-help-r4" x="305" y="898.4" textLength="158.6" clip-path="url(#breeze-help-line-36)">--no-db-reset</text><text class="breeze-help-r1" x="488" y="898.4" textLength="451.4" clip-path="url(#breeze-help-line-36)">Reset&#160;DB&#160;when&#160;entering&#160;the&#160;container.</text><text class="breeze-help-r5" x="951.6" y="898.4" textLength="268.4" clip-path="url(#breeze-help-line-36)">[default:&#160;no-db-reset]</text><text class="breeze-help-r5" x="1451.8" y="898.4" textLength="12.2" clip-path="url(#breeze-help-line-36)">│</text><text class="breeze-help-r1" x="1464" y="898.4" textLength="12.2" clip-path="url(#breeze-help-line-36)">
</text><text class="breeze-help-r5" x="0" y="922.8" textLength="1464" clip-path="url(#breeze-help-line-37)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="922.8" textLength="12.2" clip-path="url(#breeze-help-line-37)">
</text><text class="breeze-help-r5" x="0" y="947.2" textLength="24.4" clip-path="url(#breeze-help-line-38)">╭─</text><text class="breeze-help-r5" x="24.4" y="947.2" textLength="488" clip-path="url(#breeze-help-line-38)">&#160;Build&#160;CI&#160;image&#160;(before&#160;entering&#160;shell)&#160;</text><text class="breeze-help-r5" x="512.4" y="947.2" textLength="927.2" clip-path="url(#breeze-help-line-38)">────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="947.2" textLength="24.4" clip-path="url(#breeze-help-line-38)">─╮</text><text class="breeze-help-r1" x="1464" y="947.2" textLength="12.2" clip-path="url(#breeze-help-line-38)">
</text><text class="breeze-help-r5" x="0" y="971.6" textLength="12.2" clip-path="url(#breeze-help-line-39)">│</text><text class="breeze-help-r4" x="24.4" y="971.6" textLength="231.8" clip-path="url(#breeze-help-line-39)">--github-repository</text><text class="breeze-help-r6" x="292.8" y="971.6" textLength="24.4" clip-path="url(#breeze-help-line-39)">-g</text><text class="breeze-help-r1" x="341.6" y="971.6" textLength="585.6" clip-path="url(#breeze-help-line-39)">GitHub&#160;repository&#160;used&#160;to&#160;pull,&#160;push&#160;run&#160;images.</text><text class="breeze-help-r7" x="939.4" y="971.6" textLength="73.2" clip-path="url(#breeze-help-line-39)">(TEXT)</text><text class="breeze-help-r5" x="1024.8" y="971.6" textLength="305" clip-path="url(#breeze-help-line-39)">[default:&#160;apache/airflow]</text><text class="breeze-help-r5" x="1451.8" y="971.6" textLength="12.2" clip-path="url(#breeze-help-line-39)">│</text><text class="breeze-help-r1" x="1464" y="971.6" textLength="12.2" clip-path="url(#breeze-help-line-39)">
</text><text class="breeze-help-r5" x="0" y="996" textLength="12.2" clip-path="url(#breeze-help-line-40)">│</text><text class="breeze-help-r4" x="24.4" y="996" textLength="109.8" clip-path="url(#breeze-help-line-40)">--builder</text><text class="breeze-help-r1" x="341.6" y="996" textLength="756.4" clip-path="url(#breeze-help-line-40)">Buildx&#160;builder&#160;used&#160;to&#160;perform&#160;`docker&#160;buildx&#160;build`&#160;commands.</text><text class="breeze-help-r7" x="1110.2" y="996" textLength="73.2" clip-path="url(#breeze-help-line-40)">(TEXT)</text><text class="breeze-help-r5" x="1451.8" y="996" textLength="12.2" clip-path="url(#breeze-help-line-40)">│</text><text class="breeze-help-r1" x="1464" y="996" textLength="12.2" clip-path="url(#breeze-help-line-40)">
</text><text class="breeze-help-r5" x="0" y="1020.4" textLength="12.2" clip-path="url(#breeze-help-line-41)">│</text><text class="breeze-help-r5" x="341.6" y="1020.4" textLength="756.4" clip-path="url(#breeze-help-line-41)">[default:&#160;autodetect]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1020.4" textLength="12.2" clip-path="url(#breeze-help-line-41)">│</text><text class="breeze-help-r1" x="1464" y="1020.4" textLength="12.2" clip-path="url(#breeze-help-line-41)">
</text><text class="breeze-help-r5" x="0" y="1044.8" textLength="12.2" clip-path="url(#breeze-help-line-42)">│</text><text class="breeze-help-r4" x="24.4" y="1044.8" textLength="97.6" clip-path="url(#breeze-help-line-42)">--use-uv</text><text class="breeze-help-r1" x="122" y="1044.8" textLength="12.2" clip-path="url(#breeze-help-line-42)">/</text><text class="breeze-help-r4" x="134.2" y="1044.8" textLength="134.2" clip-path="url(#breeze-help-line-42)">--no-use-uv</text><text class="breeze-help-r1" x="341.6" y="1044.8" textLength="719.8" clip-path="url(#breeze-help-line-42)">Use&#160;uv&#160;instead&#160;of&#160;pip&#160;as&#160;packaging&#160;tool&#160;to&#160;build&#160;the&#160;image.</text><text class="breeze-help-r5" x="1073.6" y="1044.8" textLength="207.4" clip-path="url(#breeze-help-line-42)">[default:&#160;use-uv]</text><text class="breeze-help-r5" x="1451.8" y="1044.8" textLength="12.2" clip-path="url(#breeze-help-line-42)">│</text><text class="breeze-help-r1" x="1464" y="1044.8" textLength="12.2" clip-path="url(#breeze-help-line-42)">
</text><text class="breeze-help-r5" x="0" y="1069.2" textLength="12.2" clip-path="url(#breeze-help-line-43)">│</text><text class="breeze-help-r4" x="24.4" y="1069.2" textLength="207.4" clip-path="url(#breeze-help-line-43)">--uv-http-timeout</text><text class="breeze-help-r1" x="341.6" y="1069.2" textLength="829.6" clip-path="url(#breeze-help-line-43)">Timeout&#160;for&#160;requests&#160;that&#160;UV&#160;makes&#160;(only&#160;used&#160;in&#160;case&#160;of&#160;UV&#160;builds).</text><text class="breeze-help-r7" x="1183.4" y="1069.2" textLength="183" clip-path="url(#breeze-help-line-43)">(INTEGER&#160;RANGE)</text><text class="breeze-help-r5" x="1451.8" y="1069.2" textLength="12.2" clip-path="url(#breeze-help-line-43)">│</text><text class="breeze-help-r1" x="1464" y="1069.2" textLength="12.2" clip-path="url(#breeze-help-line-43)">
</text><text class="breeze-help-r5" x="0" y="1093.6" textLength="12.2" clip-path="url(#breeze-help-line-44)">│</text><text class="breeze-help-r5" x="341.6" y="1093.6" textLength="829.6" clip-path="url(#breeze-help-line-44)">[default:&#160;300;&#160;x&gt;=1]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1093.6" textLength="12.2" clip-path="url(#breeze-help-line-44)">│</text><text class="breeze-help-r1" x="1464" y="1093.6" textLength="12.2" clip-path="url(#breeze-help-line-44)">
</text><text class="breeze-help-r5" x="0" y="1118" textLength="1464" clip-path="url(#breeze-help-line-45)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1118" textLength="12.2" clip-path="url(#breeze-help-line-45)">
</text><text class="breeze-help-r5" x="0" y="1142.4" textLength="24.4" clip-path="url(#breeze-help-line-46)">╭─</text><text class="breeze-help-r5" x="24.4" y="1142.4" textLength="183" clip-path="url(#breeze-help-line-46)">&#160;Other&#160;options&#160;</text><text class="breeze-help-r5" x="207.4" y="1142.4" textLength="1232.2" clip-path="url(#breeze-help-line-46)">─────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1142.4" textLength="24.4" clip-path="url(#breeze-help-line-46)">─╮</text><text class="breeze-help-r1" x="1464" y="1142.4" textLength="12.2" clip-path="url(#breeze-help-line-46)">
</text><text class="breeze-help-r5" x="0" y="1166.8" textLength="12.2" clip-path="url(#breeze-help-line-47)">│</text><text class="breeze-help-r4" x="24.4" y="1166.8" textLength="256.2" clip-path="url(#breeze-help-line-47)">--forward-credentials</text><text class="breeze-help-r6" x="305" y="1166.8" textLength="24.4" clip-path="url(#breeze-help-line-47)">-f</text><text class="breeze-help-r1" x="353.8" y="1166.8" textLength="634.4" clip-path="url(#breeze-help-line-47)">Forward&#160;local&#160;credentials&#160;to&#160;container&#160;when&#160;running.</text><text class="breeze-help-r5" x="1451.8" y="1166.8" textLength="12.2" clip-path="url(#breeze-help-line-47)">│</text><text class="breeze-help-r1" x="1464" y="1166.8" textLength="12.2" clip-path="url(#breeze-help-line-47)">
</text><text class="breeze-help-r5" x="0" y="1191.2" textLength="12.2" clip-path="url(#breeze-help-line-48)">│</text><text class="breeze-help-r4" x="24.4" y="1191.2" textLength="122" clip-path="url(#breeze-help-line-48)">--max-time</text><text class="breeze-help-r1" x="353.8" y="1191.2" textLength="1049.2" clip-path="url(#breeze-help-line-48)">Maximum&#160;time&#160;that&#160;the&#160;command&#160;should&#160;take&#160;-&#160;if&#160;it&#160;takes&#160;longer,&#160;the&#160;command&#160;will&#160;fail.</text><text class="breeze-help-r5" x="1451.8" y="1191.2" textLength="12.2" clip-path="url(#breeze-help-line-48)">│</text><text class="breeze-help-r1" x="1464" y="1191.2" textLength="12.2" clip-path="url(#breeze-help-line-48)">
</text><text class="breeze-help-r5" x="0" y="1215.6" textLength="12.2" clip-path="url(#breeze-help-line-49)">│</text><text class="breeze-help-r7" x="353.8" y="1215.6" textLength="1049.2" clip-path="url(#breeze-help-line-49)">(INTEGER&#160;RANGE)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1215.6" textLength="12.2" clip-path="url(#breeze-help-line-49)">│</text><text class="breeze-help-r1" x="1464" y="1215.6" textLength="12.2" clip-path="url(#breeze-help-line-49)">
</text><text class="breeze-help-r5" x="0" y="1240" textLength="1464" clip-path="url(#breeze-help-line-50)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1240" textLength="12.2" clip-path="url(#breeze-help-line-50)">
</text><text class="breeze-help-r5" x="0" y="1264.4" textLength="24.4" clip-path="url(#breeze-help-line-51)">╭─</text><text class="breeze-help-r5" x="24.4" y="1264.4" textLength="195.2" clip-path="url(#breeze-help-line-51)">&#160;Common&#160;options&#160;</text><text class="breeze-help-r5" x="219.6" y="1264.4" textLength="1220" clip-path="url(#breeze-help-line-51)">────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1264.4" textLength="24.4" clip-path="url(#breeze-help-line-51)">─╮</text><text class="breeze-help-r1" x="1464" y="1264.4" textLength="12.2" clip-path="url(#breeze-help-line-51)">
</text><text class="breeze-help-r5" x="0" y="1288.8" textLength="12.2" clip-path="url(#breeze-help-line-52)">│</text><text class="breeze-help-r4" x="24.4" y="1288.8" textLength="97.6" clip-path="url(#breeze-help-line-52)">--answer</text><text class="breeze-help-r6" x="158.6" y="1288.8" textLength="24.4" clip-path="url(#breeze-help-line-52)">-a</text><text class="breeze-help-r1" x="207.4" y="1288.8" textLength="317.2" clip-path="url(#breeze-help-line-52)">Force&#160;answer&#160;to&#160;questions.</text><text class="breeze-help-r7" x="536.8" y="1288.8" textLength="353.8" clip-path="url(#breeze-help-line-52)">(y&#160;|&#160;n&#160;|&#160;q&#160;|&#160;yes&#160;|&#160;no&#160;|&#160;quit)</text><text class="breeze-help-r5" x="1451.8" y="1288.8" textLength="12.2" clip-path="url(#breeze-help-line-52)">│</text><text class="breeze-help-r1" x="1464" y="1288.8" textLength="12.2" clip-path="url(#breeze-help-line-52)">
</text><text class="breeze-help-r5" x="0" y="1313.2" textLength="12.2" clip-path="url(#breeze-help-line-53)">│</text><text class="breeze-help-r4" x="24.4" y="1313.2" textLength="109.8" clip-path="url(#breeze-help-line-53)">--dry-run</text><text class="breeze-help-r6" x="158.6" y="1313.2" textLength="24.4" clip-path="url(#breeze-help-line-53)">-D</text><text class="breeze-help-r1" x="207.4" y="1313.2" textLength="719.8" clip-path="url(#breeze-help-line-53)">If&#160;dry-run&#160;is&#160;set,&#160;commands&#160;are&#160;only&#160;printed,&#160;not&#160;executed.</text><text class="breeze-help-r5" x="1451.8" y="1313.2" textLength="12.2" clip-path="url(#breeze-help-line-53)">│</text><text class="breeze-help-r1" x="1464" y="1313.2" textLength="12.2" clip-path="url(#breeze-help-line-53)">
</text><text class="breeze-help-r5" x="0" y="1337.6" textLength="12.2" clip-path="url(#breeze-help-line-54)">│</text><text class="breeze-help-r4" x="24.4" y="1337.6" textLength="109.8" clip-path="url(#breeze-help-line-54)">--verbose</text><text class="breeze-help-r6" x="158.6" y="1337.6" textLength="24.4" clip-path="url(#breeze-help-line-54)">-v</text><text class="breeze-help-r1" x="207.4" y="1337.6" textLength="585.6" clip-path="url(#breeze-help-line-54)">Print&#160;verbose&#160;information&#160;about&#160;performed&#160;steps.</text><text class="breeze-help-r5" x="1451.8" y="1337.6" textLength="12.2" clip-path="url(#breeze-help-line-54)">│</text><text class="breeze-help-r1" x="1464" y="1337.6" textLength="12.2" clip-path="url(#breeze-help-line-54)">
</text><text class="breeze-help-r5" x="0" y="1362" textLength="12.2" clip-path="url(#breeze-help-line-55)">│</text><text class="breeze-help-r4" x="24.4" y="1362" textLength="73.2" clip-path="url(#breeze-help-line-55)">--help</text><text class="breeze-help-r6" x="158.6" y="1362" textLength="24.4" clip-path="url(#breeze-help-line-55)">-h</text><text class="breeze-help-r1" x="207.4" y="1362" textLength="329.4" clip-path="url(#breeze-help-line-55)">Show&#160;this&#160;message&#160;and&#160;exit.</text><text class="breeze-help-r5" x="1451.8" y="1362" textLength="12.2" clip-path="url(#breeze-help-line-55)">│</text><text class="breeze-help-r1" x="1464" y="1362" textLength="12.2" clip-path="url(#breeze-help-line-55)">
</text><text class="breeze-help-r5" x="0" y="1386.4" textLength="1464" clip-path="url(#breeze-help-line-56)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1386.4" textLength="12.2" clip-path="url(#breeze-help-line-56)">
</text><text class="breeze-help-r5" x="0" y="1410.8" textLength="24.4" clip-path="url(#breeze-help-line-57)">╭─</text><text class="breeze-help-r5" x="24.4" y="1410.8" textLength="244" clip-path="url(#breeze-help-line-57)">&#160;Developer&#160;commands&#160;</text><text class="breeze-help-r5" x="268.4" y="1410.8" textLength="1171.2" clip-path="url(#breeze-help-line-57)">────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1410.8" textLength="24.4" clip-path="url(#breeze-help-line-57)">─╮</text><text class="breeze-help-r1" x="1464" y="1410.8" textLength="12.2" clip-path="url(#breeze-help-line-57)">
</text><text class="breeze-help-r5" x="0" y="1435.2" textLength="12.2" clip-path="url(#breeze-help-line-58)">│</text><text class="breeze-help-r4" x="24.4" y="1435.2" textLength="280.6" clip-path="url(#breeze-help-line-58)">start-airflow&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1435.2" textLength="1110.2" clip-path="url(#breeze-help-line-58)">Enter&#160;breeze&#160;environment&#160;and&#160;starts&#160;all&#160;Airflow&#160;components&#160;in&#160;the&#160;tmux&#160;session.&#160;Compile&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1435.2" textLength="12.2" clip-path="url(#breeze-help-line-58)">│</text><text class="breeze-help-r1" x="1464" y="1435.2" textLength="12.2" clip-path="url(#breeze-help-line-58)">
</text><text class="breeze-help-r5" x="0" y="1459.6" textLength="12.2" clip-path="url(#breeze-help-line-59)">│</text><text class="breeze-help-r1" x="329.4" y="1459.6" textLength="1110.2" clip-path="url(#breeze-help-line-59)">assets&#160;if&#160;contents&#160;of&#160;www&#160;directory&#160;changed.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1459.6" textLength="12.2" clip-path="url(#breeze-help-line-59)">│</text><text class="breeze-help-r1" x="1464" y="1459.6" textLength="12.2" clip-path="url(#breeze-help-line-59)">
</text><text class="breeze-help-r5" x="0" y="1484" textLength="12.2" clip-path="url(#breeze-help-line-60)">│</text><text class="breeze-help-r4" x="24.4" y="1484" textLength="280.6" clip-path="url(#breeze-help-line-60)">static-checks&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1484" textLength="1110.2" clip-path="url(#breeze-help-line-60)">Run&#160;static&#160;checks.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1484" textLength="12.2" clip-path="url(#breeze-help-line-60)">│</text><text class="breeze-help-r1" x="1464" y="1484" textLength="12.2" clip-path="url(#breeze-help-line-60)">
</text><text class="breeze-help-r5" x="0" y="1508.4" textLength="12.2" clip-path="url(#breeze-help-line-61)">│</text><text class="breeze-help-r4" x="24.4" y="1508.4" textLength="280.6" clip-path="url(#breeze-help-line-61)">build-docs&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1508.4" textLength="1110.2" clip-path="url(#breeze-help-line-61)">Build&#160;documents.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1508.4" textLength="12.2" clip-path="url(#breeze-help-line-61)">│</text><text class="breeze-help-r1" x="1464" y="1508.4" textLength="12.2" clip-path="url(#breeze-help-line-61)">
</text><text class="breeze-help-r5" x="0" y="1532.8" textLength="12.2" clip-path="url(#breeze-help-line-62)">│</text><text class="breeze-help-r4" x="24.4" y="1532.8" textLength="280.6" clip-path="url(#breeze-help-line-62)">down&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1532.8" textLength="1110.2" clip-path="url(#breeze-help-line-62)">Stop&#160;running&#160;breeze&#160;environment.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1532.8" textLength="12.2" clip-path="url(#breeze-help-line-62)">│</text><text class="breeze-help-r1" x="1464" y="1532.8" textLength="12.2" clip-path="url(#breeze-help-line-62)">
</text><text class="breeze-help-r5" x="0" y="1557.2" textLength="12.2" clip-path="url(#breeze-help-line-63)">│</text><text class="breeze-help-r4" x="24.4" y="1557.2" textLength="280.6" clip-path="url(#breeze-help-line-63)">shell&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1557.2" textLength="1110.2" clip-path="url(#breeze-help-line-63)">Enter&#160;breeze&#160;environment.&#160;this&#160;is&#160;the&#160;default&#160;command&#160;use&#160;when&#160;no&#160;other&#160;is&#160;selected.&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1557.2" textLength="12.2" clip-path="url(#breeze-help-line-63)">│</text><text class="breeze-help-r1" x="1464" y="1557.2" textLength="12.2" clip-path="url(#breeze-help-line-63)">
</text><text class="breeze-help-r5" x="0" y="1581.6" textLength="12.2" clip-path="url(#breeze-help-line-64)">│</text><text class="breeze-help-r4" x="24.4" y="1581.6" textLength="280.6" clip-path="url(#breeze-help-line-64)">exec&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1581.6" textLength="1110.2" clip-path="url(#breeze-help-line-64)">Joins&#160;the&#160;interactive&#160;shell&#160;of&#160;running&#160;airflow&#160;container.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1581.6" textLength="12.2" clip-path="url(#breeze-help-line-64)">│</text><text class="breeze-help-r1" x="1464" y="1581.6" textLength="12.2" clip-path="url(#breeze-help-line-64)">
</text><text class="breeze-help-r5" x="0" y="1606" textLength="12.2" clip-path="url(#breeze-help-line-65)">│</text><text class="breeze-help-r4" x="24.4" y="1606" textLength="280.6" clip-path="url(#breeze-help-line-65)">compile-ui-assets&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1606" textLength="1110.2" clip-path="url(#breeze-help-line-65)">Compiles&#160;ui&#160;assets.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1606" textLength="12.2" clip-path="url(#breeze-help-line-65)">│</text><text class="breeze-help-r1" x="1464" y="1606" textLength="12.2" clip-path="url(#breeze-help-line-65)">
</text><text class="breeze-help-r5" x="0" y="1630.4" textLength="12.2" clip-path="url(#breeze-help-line-66)">│</text><text class="breeze-help-r4" x="24.4" y="1630.4" textLength="280.6" clip-path="url(#breeze-help-line-66)">cleanup&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1630.4" textLength="1110.2" clip-path="url(#breeze-help-line-66)">Cleans&#160;the&#160;cache&#160;of&#160;parameters,&#160;docker&#160;cache&#160;and&#160;optionally&#160;built&#160;CI/PROD&#160;images.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1630.4" textLength="12.2" clip-path="url(#breeze-help-line-66)">│</text><text class="breeze-help-r1" x="1464" y="1630.4" textLength="12.2" clip-path="url(#breeze-help-line-66)">
</text><text class="breeze-help-r5" x="0" y="1654.8" textLength="12.2" clip-path="url(#breeze-help-line-67)">│</text><text class="breeze-help-r4" x="24.4" y="1654.8" textLength="280.6" clip-path="url(#breeze-help-line-67)">generate-migration-file</text><text class="breeze-help-r1" x="329.4" y="1654.8" textLength="1110.2" clip-path="url(#breeze-help-line-67)">Autogenerate&#160;the&#160;alembic&#160;migration&#160;file&#160;for&#160;the&#160;ORM&#160;changes.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1654.8" textLength="12.2" clip-path="url(#breeze-help-line-67)">│</text><text class="breeze-help-r1" x="1464" y="1654.8" textLength="12.2" clip-path="url(#breeze-help-line-67)">
</text><text class="breeze-help-r5" x="0" y="1679.2" textLength="12.2" clip-path="url(#breeze-help-line-68)">│</text><text class="breeze-help-r4" x="24.4" y="1679.2" textLength="280.6" clip-path="url(#breeze-help-line-68)">doctor&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1679.2" textLength="1110.2" clip-path="url(#breeze-help-line-68)">Auto-healing&#160;of&#160;breeze&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1679.2" textLength="12.2" clip-path="url(#breeze-help-line-68)">│</text><text class="breeze-help-r1" x="1464" y="1679.2" textLength="12.2" clip-path="url(#breeze-help-line-68)">
</text><text class="breeze-help-r5" x="0" y="1703.6" textLength="1464" clip-path="url(#breeze-help-line-69)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1703.6" textLength="12.2" clip-path="url(#breeze-help-line-69)">
</text><text class="breeze-help-r5" x="0" y="1728" textLength="24.4" clip-path="url(#breeze-help-line-70)">╭─</text><text class="breeze-help-r5" x="24.4" y="1728" textLength="219.6" clip-path="url(#breeze-help-line-70)">&#160;Testing&#160;commands&#160;</text><text class="breeze-help-r5" x="244" y="1728" textLength="1195.6" clip-path="url(#breeze-help-line-70)">──────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1728" textLength="24.4" clip-path="url(#breeze-help-line-70)">─╮</text><text class="breeze-help-r1" x="1464" y="1728" textLength="12.2" clip-path="url(#breeze-help-line-70)">
</text><text class="breeze-help-r5" x="0" y="1752.4" textLength="12.2" clip-path="url(#breeze-help-line-71)">│</text><text class="breeze-help-r4" x="24.4" y="1752.4" textLength="183" clip-path="url(#breeze-help-line-71)">testing&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="231.8" y="1752.4" textLength="1207.8" clip-path="url(#breeze-help-line-71)">Tools&#160;that&#160;developers&#160;can&#160;use&#160;to&#160;run&#160;tests&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1752.4" textLength="12.2" clip-path="url(#breeze-help-line-71)">│</text><text class="breeze-help-r1" x="1464" y="1752.4" textLength="12.2" clip-path="url(#breeze-help-line-71)">
</text><text class="breeze-help-r5" x="0" y="1776.8" textLength="12.2" clip-path="url(#breeze-help-line-72)">│</text><text class="breeze-help-r4" x="24.4" y="1776.8" textLength="183" clip-path="url(#breeze-help-line-72)">k8s&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="231.8" y="1776.8" textLength="1207.8" clip-path="url(#breeze-help-line-72)">Tools&#160;that&#160;developers&#160;use&#160;to&#160;run&#160;Kubernetes&#160;tests&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1776.8" textLength="12.2" clip-path="url(#breeze-help-line-72)">│</text><text class="breeze-help-r1" x="1464" y="1776.8" textLength="12.2" clip-path="url(#breeze-help-line-72)">
</text><text class="breeze-help-r5" x="0" y="1801.2" textLength="1464" clip-path="url(#breeze-help-line-73)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1801.2" textLength="12.2" clip-path="url(#breeze-help-line-73)">
</text><text class="breeze-help-r5" x="0" y="1825.6" textLength="24.4" clip-path="url(#breeze-help-line-74)">╭─</text><text class="breeze-help-r5" x="24.4" y="1825.6" textLength="195.2" clip-path="url(#breeze-help-line-74)">&#160;Image&#160;commands&#160;</text><text class="breeze-help-r5" x="219.6" y="1825.6" textLength="1220" clip-path="url(#breeze-help-line-74)">────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1825.6" textLength="24.4" clip-path="url(#breeze-help-line-74)">─╮</text><text class="breeze-help-r1" x="1464" y="1825.6" textLength="12.2" clip-path="url(#breeze-help-line-74)">
</text><text class="breeze-help-r5" x="0" y="1850" textLength="12.2" clip-path="url(#breeze-help-line-75)">│</text><text class="breeze-help-r4" x="24.4" y="1850" textLength="207.4" clip-path="url(#breeze-help-line-75)">ci-image&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="256.2" y="1850" textLength="1183.4" clip-path="url(#breeze-help-line-75)">Tools&#160;that&#160;developers&#160;can&#160;use&#160;to&#160;manually&#160;manage&#160;CI&#160;images&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1850" textLength="12.2" clip-path="url(#breeze-help-line-75)">│</text><text class="breeze-help-r1" x="1464" y="1850" textLength="12.2" clip-path="url(#breeze-help-line-75)">
</text><text class="breeze-help-r5" x="0" y="1874.4" textLength="12.2" clip-path="url(#breeze-help-line-76)">│</text><text class="breeze-help-r4" x="24.4" y="1874.4" textLength="207.4" clip-path="url(#breeze-help-line-76)">prod-image&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="256.2" y="1874.4" textLength="1183.4" clip-path="url(#breeze-help-line-76)">Tools&#160;that&#160;developers&#160;can&#160;use&#160;to&#160;manually&#160;manage&#160;PROD&#160;images&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1874.4" textLength="12.2" clip-path="url(#breeze-help-line-76)">│</text><text class="breeze-help-r1" x="1464" y="1874.4" textLength="12.2" clip-path="url(#breeze-help-line-76)">
</text><text class="breeze-help-r5" x="0" y="1898.8" textLength="1464" clip-path="url(#breeze-help-line-77)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1898.8" textLength="12.2" clip-path="url(#breeze-help-line-77)">
</text><text class="breeze-help-r5" x="0" y="1923.2" textLength="24.4" clip-path="url(#breeze-help-line-78)">╭─</text><text class="breeze-help-r5" x="24.4" y="1923.2" textLength="353.8" clip-path="url(#breeze-help-line-78)">&#160;Release&#160;management&#160;commands&#160;</text><text class="breeze-help-r5" x="378.2" y="1923.2" textLength="1061.4" clip-path="url(#breeze-help-line-78)">───────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="1923.2" textLength="24.4" clip-path="url(#breeze-help-line-78)">─╮</text><text class="breeze-help-r1" x="1464" y="1923.2" textLength="12.2" clip-path="url(#breeze-help-line-78)">
</text><text class="breeze-help-r5" x="0" y="1947.6" textLength="12.2" clip-path="url(#breeze-help-line-79)">│</text><text class="breeze-help-r4" x="24.4" y="1947.6" textLength="280.6" clip-path="url(#breeze-help-line-79)">release-management&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1947.6" textLength="1110.2" clip-path="url(#breeze-help-line-79)">Tools&#160;that&#160;release&#160;managers&#160;can&#160;use&#160;to&#160;prepare&#160;and&#160;manage&#160;Airflow&#160;releases&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1947.6" textLength="12.2" clip-path="url(#breeze-help-line-79)">│</text><text class="breeze-help-r1" x="1464" y="1947.6" textLength="12.2" clip-path="url(#breeze-help-line-79)">
</text><text class="breeze-help-r5" x="0" y="1972" textLength="12.2" clip-path="url(#breeze-help-line-80)">│</text><text class="breeze-help-r4" x="24.4" y="1972" textLength="280.6" clip-path="url(#breeze-help-line-80)">sbom&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="329.4" y="1972" textLength="1110.2" clip-path="url(#breeze-help-line-80)">Tools&#160;that&#160;release&#160;managers&#160;can&#160;use&#160;to&#160;prepare&#160;sbom&#160;information&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="1972" textLength="12.2" clip-path="url(#breeze-help-line-80)">│</text><text class="breeze-help-r1" x="1464" y="1972" textLength="12.2" clip-path="url(#breeze-help-line-80)">
</text><text class="breeze-help-r5" x="0" y="1996.4" textLength="1464" clip-path="url(#breeze-help-line-81)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="1996.4" textLength="12.2" clip-path="url(#breeze-help-line-81)">
</text><text class="breeze-help-r5" x="0" y="2020.8" textLength="24.4" clip-path="url(#breeze-help-line-82)">╭─</text><text class="breeze-help-r5" x="24.4" y="2020.8" textLength="195.2" clip-path="url(#breeze-help-line-82)">&#160;Other&#160;commands&#160;</text><text class="breeze-help-r5" x="219.6" y="2020.8" textLength="1220" clip-path="url(#breeze-help-line-82)">────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-help-r5" x="1439.6" y="2020.8" textLength="24.4" clip-path="url(#breeze-help-line-82)">─╮</text><text class="breeze-help-r1" x="1464" y="2020.8" textLength="12.2" clip-path="url(#breeze-help-line-82)">
</text><text class="breeze-help-r5" x="0" y="2045.2" textLength="12.2" clip-path="url(#breeze-help-line-83)">│</text><text class="breeze-help-r4" x="24.4" y="2045.2" textLength="122" clip-path="url(#breeze-help-line-83)">setup&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="170.8" y="2045.2" textLength="1268.8" clip-path="url(#breeze-help-line-83)">Tools&#160;that&#160;developers&#160;can&#160;use&#160;to&#160;configure&#160;Breeze&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="2045.2" textLength="12.2" clip-path="url(#breeze-help-line-83)">│</text><text class="breeze-help-r1" x="1464" y="2045.2" textLength="12.2" clip-path="url(#breeze-help-line-83)">
</text><text class="breeze-help-r5" x="0" y="2069.6" textLength="12.2" clip-path="url(#breeze-help-line-84)">│</text><text class="breeze-help-r4" x="24.4" y="2069.6" textLength="122" clip-path="url(#breeze-help-line-84)">ci&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r1" x="170.8" y="2069.6" textLength="1268.8" clip-path="url(#breeze-help-line-84)">Tools&#160;that&#160;CI&#160;workflows&#160;use&#160;to&#160;cleanup/manage&#160;CI&#160;environment&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-help-r5" x="1451.8" y="2069.6" textLength="12.2" clip-path="url(#breeze-help-line-84)">│</text><text class="breeze-help-r1" x="1464" y="2069.6" textLength="12.2" clip-path="url(#breeze-help-line-84)">
</text><text class="breeze-help-r5" x="0" y="2094" textLength="1464" clip-path="url(#breeze-help-line-85)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-help-r1" x="1464" y="2094" textLength="12.2" clip-path="url(#breeze-help-line-85)">
</text>
    </g>
    </g>
</svg>
