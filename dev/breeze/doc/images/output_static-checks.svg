<svg class="rich-terminal" viewBox="0 0 1482 2221.6" xmlns="http://www.w3.org/2000/svg">
    <!-- Generated with <PERSON> https://www.textualize.io -->
    <style>

    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Regular"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Regular.woff") format("woff");
        font-style: normal;
        font-weight: 400;
    }
    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Bold"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Bold.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Bold.woff") format("woff");
        font-style: bold;
        font-weight: 700;
    }

    .breeze-static-checks-matrix {
        font-family: Fira Code, monospace;
        font-size: 20px;
        line-height: 24.4px;
        font-variant-east-asian: full-width;
    }

    .breeze-static-checks-title {
        font-size: 18px;
        font-weight: bold;
        font-family: arial;
    }

    .breeze-static-checks-r1 { fill: #c5c8c6 }
.breeze-static-checks-r2 { fill: #d0b344 }
.breeze-static-checks-r3 { fill: #c5c8c6;font-weight: bold }
.breeze-static-checks-r4 { fill: #68a0b3;font-weight: bold }
.breeze-static-checks-r5 { fill: #868887 }
.breeze-static-checks-r6 { fill: #98a84b;font-weight: bold }
.breeze-static-checks-r7 { fill: #8d7b39 }
    </style>

    <defs>
    <clipPath id="breeze-static-checks-clip-terminal">
      <rect x="0" y="0" width="1463.0" height="2170.6" />
    </clipPath>
    <clipPath id="breeze-static-checks-line-0">
    <rect x="0" y="1.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-1">
    <rect x="0" y="25.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-2">
    <rect x="0" y="50.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-3">
    <rect x="0" y="74.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-4">
    <rect x="0" y="99.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-5">
    <rect x="0" y="123.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-6">
    <rect x="0" y="147.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-7">
    <rect x="0" y="172.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-8">
    <rect x="0" y="196.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-9">
    <rect x="0" y="221.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-10">
    <rect x="0" y="245.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-11">
    <rect x="0" y="269.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-12">
    <rect x="0" y="294.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-13">
    <rect x="0" y="318.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-14">
    <rect x="0" y="343.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-15">
    <rect x="0" y="367.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-16">
    <rect x="0" y="391.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-17">
    <rect x="0" y="416.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-18">
    <rect x="0" y="440.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-19">
    <rect x="0" y="465.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-20">
    <rect x="0" y="489.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-21">
    <rect x="0" y="513.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-22">
    <rect x="0" y="538.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-23">
    <rect x="0" y="562.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-24">
    <rect x="0" y="587.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-25">
    <rect x="0" y="611.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-26">
    <rect x="0" y="635.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-27">
    <rect x="0" y="660.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-28">
    <rect x="0" y="684.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-29">
    <rect x="0" y="709.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-30">
    <rect x="0" y="733.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-31">
    <rect x="0" y="757.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-32">
    <rect x="0" y="782.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-33">
    <rect x="0" y="806.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-34">
    <rect x="0" y="831.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-35">
    <rect x="0" y="855.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-36">
    <rect x="0" y="879.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-37">
    <rect x="0" y="904.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-38">
    <rect x="0" y="928.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-39">
    <rect x="0" y="953.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-40">
    <rect x="0" y="977.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-41">
    <rect x="0" y="1001.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-42">
    <rect x="0" y="1026.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-43">
    <rect x="0" y="1050.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-44">
    <rect x="0" y="1075.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-45">
    <rect x="0" y="1099.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-46">
    <rect x="0" y="1123.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-47">
    <rect x="0" y="1148.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-48">
    <rect x="0" y="1172.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-49">
    <rect x="0" y="1197.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-50">
    <rect x="0" y="1221.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-51">
    <rect x="0" y="1245.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-52">
    <rect x="0" y="1270.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-53">
    <rect x="0" y="1294.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-54">
    <rect x="0" y="1319.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-55">
    <rect x="0" y="1343.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-56">
    <rect x="0" y="1367.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-57">
    <rect x="0" y="1392.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-58">
    <rect x="0" y="1416.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-59">
    <rect x="0" y="1441.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-60">
    <rect x="0" y="1465.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-61">
    <rect x="0" y="1489.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-62">
    <rect x="0" y="1514.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-63">
    <rect x="0" y="1538.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-64">
    <rect x="0" y="1563.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-65">
    <rect x="0" y="1587.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-66">
    <rect x="0" y="1611.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-67">
    <rect x="0" y="1636.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-68">
    <rect x="0" y="1660.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-69">
    <rect x="0" y="1685.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-70">
    <rect x="0" y="1709.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-71">
    <rect x="0" y="1733.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-72">
    <rect x="0" y="1758.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-73">
    <rect x="0" y="1782.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-74">
    <rect x="0" y="1807.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-75">
    <rect x="0" y="1831.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-76">
    <rect x="0" y="1855.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-77">
    <rect x="0" y="1880.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-78">
    <rect x="0" y="1904.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-79">
    <rect x="0" y="1929.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-80">
    <rect x="0" y="1953.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-81">
    <rect x="0" y="1977.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-82">
    <rect x="0" y="2002.3" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-83">
    <rect x="0" y="2026.7" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-84">
    <rect x="0" y="2051.1" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-85">
    <rect x="0" y="2075.5" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-86">
    <rect x="0" y="2099.9" width="1464" height="24.65"/>
            </clipPath>
<clipPath id="breeze-static-checks-line-87">
    <rect x="0" y="2124.3" width="1464" height="24.65"/>
            </clipPath>
    </defs>

    <rect fill="#292929" stroke="rgba(255,255,255,0.35)" stroke-width="1" x="1" y="1" width="1480" height="2219.6" rx="8"/><text class="breeze-static-checks-title" fill="#c5c8c6" text-anchor="middle" x="740" y="27">Command:&#160;static-checks</text>
            <g transform="translate(26,22)">
            <circle cx="0" cy="0" r="7" fill="#ff5f57"/>
            <circle cx="22" cy="0" r="7" fill="#febc2e"/>
            <circle cx="44" cy="0" r="7" fill="#28c840"/>
            </g>
        
    <g transform="translate(9, 41)" clip-path="url(#breeze-static-checks-clip-terminal)">
    
    <g class="breeze-static-checks-matrix">
    <text class="breeze-static-checks-r1" x="1464" y="20" textLength="12.2" clip-path="url(#breeze-static-checks-line-0)">
</text><text class="breeze-static-checks-r2" x="12.2" y="44.4" textLength="73.2" clip-path="url(#breeze-static-checks-line-1)">Usage:</text><text class="breeze-static-checks-r3" x="97.6" y="44.4" textLength="244" clip-path="url(#breeze-static-checks-line-1)">breeze&#160;static-checks</text><text class="breeze-static-checks-r1" x="353.8" y="44.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-1)">[</text><text class="breeze-static-checks-r4" x="366" y="44.4" textLength="85.4" clip-path="url(#breeze-static-checks-line-1)">OPTIONS</text><text class="breeze-static-checks-r1" x="451.4" y="44.4" textLength="36.6" clip-path="url(#breeze-static-checks-line-1)">]&#160;[</text><text class="breeze-static-checks-r4" x="488" y="44.4" textLength="170.8" clip-path="url(#breeze-static-checks-line-1)">PRECOMMIT_ARGS</text><text class="breeze-static-checks-r1" x="658.8" y="44.4" textLength="48.8" clip-path="url(#breeze-static-checks-line-1)">]...</text><text class="breeze-static-checks-r1" x="1464" y="44.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-1)">
</text><text class="breeze-static-checks-r1" x="1464" y="68.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-2)">
</text><text class="breeze-static-checks-r1" x="12.2" y="93.2" textLength="219.6" clip-path="url(#breeze-static-checks-line-3)">Run&#160;static&#160;checks.</text><text class="breeze-static-checks-r1" x="1464" y="93.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-3)">
</text><text class="breeze-static-checks-r1" x="1464" y="117.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-4)">
</text><text class="breeze-static-checks-r5" x="0" y="142" textLength="24.4" clip-path="url(#breeze-static-checks-line-5)">╭─</text><text class="breeze-static-checks-r5" x="24.4" y="142" textLength="219.6" clip-path="url(#breeze-static-checks-line-5)">&#160;Pre-commit&#160;flags&#160;</text><text class="breeze-static-checks-r5" x="244" y="142" textLength="1195.6" clip-path="url(#breeze-static-checks-line-5)">──────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-static-checks-r5" x="1439.6" y="142" textLength="24.4" clip-path="url(#breeze-static-checks-line-5)">─╮</text><text class="breeze-static-checks-r1" x="1464" y="142" textLength="12.2" clip-path="url(#breeze-static-checks-line-5)">
</text><text class="breeze-static-checks-r5" x="0" y="166.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-6)">│</text><text class="breeze-static-checks-r4" x="24.4" y="166.4" textLength="73.2" clip-path="url(#breeze-static-checks-line-6)">--type</text><text class="breeze-static-checks-r6" x="402.6" y="166.4" textLength="24.4" clip-path="url(#breeze-static-checks-line-6)">-t</text><text class="breeze-static-checks-r1" x="451.4" y="166.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-6)">Type(s)&#160;of&#160;the&#160;static&#160;checks&#160;to&#160;run.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="166.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-6)">│</text><text class="breeze-static-checks-r1" x="1464" y="166.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-6)">
</text><text class="breeze-static-checks-r5" x="0" y="190.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-7)">│</text><text class="breeze-static-checks-r7" x="451.4" y="190.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-7)">(all&#160;|&#160;bandit&#160;|&#160;blacken-docs&#160;|&#160;check-aiobotocore-optional&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="190.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-7)">│</text><text class="breeze-static-checks-r1" x="1464" y="190.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-7)">
</text><text class="breeze-static-checks-r5" x="0" y="215.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-8)">│</text><text class="breeze-static-checks-r7" x="451.4" y="215.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-8)">check-airflow-k8s-not-used&#160;|&#160;check-airflow-providers-bug-report-template&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="215.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-8)">│</text><text class="breeze-static-checks-r1" x="1464" y="215.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-8)">
</text><text class="breeze-static-checks-r5" x="0" y="239.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-9)">│</text><text class="breeze-static-checks-r7" x="451.4" y="239.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-9)">check-apache-license-rat&#160;|&#160;check-base-operator-partial-arguments&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="239.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-9)">│</text><text class="breeze-static-checks-r1" x="1464" y="239.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-9)">
</text><text class="breeze-static-checks-r5" x="0" y="264" textLength="12.2" clip-path="url(#breeze-static-checks-line-10)">│</text><text class="breeze-static-checks-r7" x="451.4" y="264" textLength="988.2" clip-path="url(#breeze-static-checks-line-10)">check-base-operator-usage&#160;|&#160;check-boring-cyborg-configuration&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="264" textLength="12.2" clip-path="url(#breeze-static-checks-line-10)">│</text><text class="breeze-static-checks-r1" x="1464" y="264" textLength="12.2" clip-path="url(#breeze-static-checks-line-10)">
</text><text class="breeze-static-checks-r5" x="0" y="288.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-11)">│</text><text class="breeze-static-checks-r7" x="451.4" y="288.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-11)">check-breeze-top-dependencies-limited&#160;|&#160;check-builtin-literals&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="288.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-11)">│</text><text class="breeze-static-checks-r1" x="1464" y="288.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-11)">
</text><text class="breeze-static-checks-r5" x="0" y="312.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-12)">│</text><text class="breeze-static-checks-r7" x="451.4" y="312.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-12)">check-changelog-format&#160;|&#160;check-changelog-has-no-duplicates&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="312.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-12)">│</text><text class="breeze-static-checks-r1" x="1464" y="312.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-12)">
</text><text class="breeze-static-checks-r5" x="0" y="337.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-13)">│</text><text class="breeze-static-checks-r7" x="451.4" y="337.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-13)">check-cncf-k8s-only-for-executors&#160;|&#160;check-code-deprecations&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="337.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-13)">│</text><text class="breeze-static-checks-r1" x="1464" y="337.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-13)">
</text><text class="breeze-static-checks-r5" x="0" y="361.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-14)">│</text><text class="breeze-static-checks-r7" x="451.4" y="361.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-14)">check-common-compat-used-for-openlineage&#160;|&#160;check-core-deprecation-classes&#160;|&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="361.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-14)">│</text><text class="breeze-static-checks-r1" x="1464" y="361.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-14)">
</text><text class="breeze-static-checks-r5" x="0" y="386" textLength="12.2" clip-path="url(#breeze-static-checks-line-15)">│</text><text class="breeze-static-checks-r7" x="451.4" y="386" textLength="988.2" clip-path="url(#breeze-static-checks-line-15)">check-daysago-import-from-utils&#160;|&#160;check-decorated-operator-implements-custom-name</text><text class="breeze-static-checks-r5" x="1451.8" y="386" textLength="12.2" clip-path="url(#breeze-static-checks-line-15)">│</text><text class="breeze-static-checks-r1" x="1464" y="386" textLength="12.2" clip-path="url(#breeze-static-checks-line-15)">
</text><text class="breeze-static-checks-r5" x="0" y="410.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-16)">│</text><text class="breeze-static-checks-r7" x="451.4" y="410.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-16)">|&#160;check-default-configuration&#160;|&#160;check-deferrable-default&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="410.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-16)">│</text><text class="breeze-static-checks-r1" x="1464" y="410.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-16)">
</text><text class="breeze-static-checks-r5" x="0" y="434.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-17)">│</text><text class="breeze-static-checks-r7" x="451.4" y="434.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-17)">check-docstring-param-types&#160;|&#160;check-example-dags-urls&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="434.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-17)">│</text><text class="breeze-static-checks-r1" x="1464" y="434.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-17)">
</text><text class="breeze-static-checks-r5" x="0" y="459.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-18)">│</text><text class="breeze-static-checks-r7" x="451.4" y="459.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-18)">check-executables-have-shebangs&#160;|&#160;check-extra-packages-references&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="459.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-18)">│</text><text class="breeze-static-checks-r1" x="1464" y="459.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-18)">
</text><text class="breeze-static-checks-r5" x="0" y="483.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-19)">│</text><text class="breeze-static-checks-r7" x="451.4" y="483.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-19)">check-extras-order&#160;|&#160;check-fab-migrations&#160;|&#160;check-for-inclusive-language&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="483.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-19)">│</text><text class="breeze-static-checks-r1" x="1464" y="483.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-19)">
</text><text class="breeze-static-checks-r5" x="0" y="508" textLength="12.2" clip-path="url(#breeze-static-checks-line-20)">│</text><text class="breeze-static-checks-r7" x="451.4" y="508" textLength="988.2" clip-path="url(#breeze-static-checks-line-20)">check-get-lineage-collector-providers&#160;|&#160;check-hooks-apply&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="508" textLength="12.2" clip-path="url(#breeze-static-checks-line-20)">│</text><text class="breeze-static-checks-r1" x="1464" y="508" textLength="12.2" clip-path="url(#breeze-static-checks-line-20)">
</text><text class="breeze-static-checks-r5" x="0" y="532.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-21)">│</text><text class="breeze-static-checks-r7" x="451.4" y="532.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-21)">check-imports-in-providers&#160;|&#160;check-incorrect-use-of-LoggingMixin&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="532.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-21)">│</text><text class="breeze-static-checks-r1" x="1464" y="532.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-21)">
</text><text class="breeze-static-checks-r5" x="0" y="556.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-22)">│</text><text class="breeze-static-checks-r7" x="451.4" y="556.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-22)">check-init-decorator-arguments&#160;|&#160;check-integrations-list-consistent&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="556.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-22)">│</text><text class="breeze-static-checks-r1" x="1464" y="556.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-22)">
</text><text class="breeze-static-checks-r5" x="0" y="581.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-23)">│</text><text class="breeze-static-checks-r7" x="451.4" y="581.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-23)">check-lazy-logging&#160;|&#160;check-links-to-example-dags-do-not-use-hardcoded-versions&#160;|&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="581.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-23)">│</text><text class="breeze-static-checks-r1" x="1464" y="581.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-23)">
</text><text class="breeze-static-checks-r5" x="0" y="605.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-24)">│</text><text class="breeze-static-checks-r7" x="451.4" y="605.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-24)">check-merge-conflict&#160;|&#160;check-min-python-version&#160;|&#160;check-newsfragments-are-valid&#160;|</text><text class="breeze-static-checks-r5" x="1451.8" y="605.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-24)">│</text><text class="breeze-static-checks-r1" x="1464" y="605.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-24)">
</text><text class="breeze-static-checks-r5" x="0" y="630" textLength="12.2" clip-path="url(#breeze-static-checks-line-25)">│</text><text class="breeze-static-checks-r7" x="451.4" y="630" textLength="988.2" clip-path="url(#breeze-static-checks-line-25)">check-no-airflow-deprecation-in-providers&#160;|&#160;check-no-providers-in-core-examples&#160;|</text><text class="breeze-static-checks-r5" x="1451.8" y="630" textLength="12.2" clip-path="url(#breeze-static-checks-line-25)">│</text><text class="breeze-static-checks-r1" x="1464" y="630" textLength="12.2" clip-path="url(#breeze-static-checks-line-25)">
</text><text class="breeze-static-checks-r5" x="0" y="654.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-26)">│</text><text class="breeze-static-checks-r7" x="451.4" y="654.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-26)">check-only-new-session-with-provide-session&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="654.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-26)">│</text><text class="breeze-static-checks-r1" x="1464" y="654.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-26)">
</text><text class="breeze-static-checks-r5" x="0" y="678.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-27)">│</text><text class="breeze-static-checks-r7" x="451.4" y="678.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-27)">check-persist-credentials-disabled-in-github-workflows&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="678.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-27)">│</text><text class="breeze-static-checks-r1" x="1464" y="678.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-27)">
</text><text class="breeze-static-checks-r5" x="0" y="703.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-28)">│</text><text class="breeze-static-checks-r7" x="451.4" y="703.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-28)">check-pre-commit-information-consistent&#160;|&#160;check-provide-create-sessions-imports&#160;|</text><text class="breeze-static-checks-r5" x="1451.8" y="703.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-28)">│</text><text class="breeze-static-checks-r1" x="1464" y="703.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-28)">
</text><text class="breeze-static-checks-r5" x="0" y="727.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-29)">│</text><text class="breeze-static-checks-r7" x="451.4" y="727.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-29)">check-provider-docs-valid&#160;|&#160;check-provider-yaml-valid&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="727.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-29)">│</text><text class="breeze-static-checks-r1" x="1464" y="727.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-29)">
</text><text class="breeze-static-checks-r5" x="0" y="752" textLength="12.2" clip-path="url(#breeze-static-checks-line-30)">│</text><text class="breeze-static-checks-r7" x="451.4" y="752" textLength="988.2" clip-path="url(#breeze-static-checks-line-30)">check-providers-subpackages-init-file-exist&#160;|&#160;check-pydevd-left-in-code&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="752" textLength="12.2" clip-path="url(#breeze-static-checks-line-30)">│</text><text class="breeze-static-checks-r1" x="1464" y="752" textLength="12.2" clip-path="url(#breeze-static-checks-line-30)">
</text><text class="breeze-static-checks-r5" x="0" y="776.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-31)">│</text><text class="breeze-static-checks-r7" x="451.4" y="776.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-31)">check-revision-heads-map&#160;|&#160;check-safe-filter-usage-in-html&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="776.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-31)">│</text><text class="breeze-static-checks-r1" x="1464" y="776.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-31)">
</text><text class="breeze-static-checks-r5" x="0" y="800.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-32)">│</text><text class="breeze-static-checks-r7" x="451.4" y="800.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-32)">check-significant-newsfragments-are-valid&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="800.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-32)">│</text><text class="breeze-static-checks-r1" x="1464" y="800.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-32)">
</text><text class="breeze-static-checks-r5" x="0" y="825.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-33)">│</text><text class="breeze-static-checks-r7" x="451.4" y="825.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-33)">check-sql-dependency-common-data-structure&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="825.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-33)">│</text><text class="breeze-static-checks-r1" x="1464" y="825.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-33)">
</text><text class="breeze-static-checks-r5" x="0" y="849.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-34)">│</text><text class="breeze-static-checks-r7" x="451.4" y="849.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-34)">check-start-date-not-used-in-defaults&#160;|&#160;check-system-tests-present&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="849.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-34)">│</text><text class="breeze-static-checks-r1" x="1464" y="849.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-34)">
</text><text class="breeze-static-checks-r5" x="0" y="874" textLength="12.2" clip-path="url(#breeze-static-checks-line-35)">│</text><text class="breeze-static-checks-r7" x="451.4" y="874" textLength="988.2" clip-path="url(#breeze-static-checks-line-35)">check-system-tests-tocs&#160;|&#160;check-taskinstance-tis-attrs&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="874" textLength="12.2" clip-path="url(#breeze-static-checks-line-35)">│</text><text class="breeze-static-checks-r1" x="1464" y="874" textLength="12.2" clip-path="url(#breeze-static-checks-line-35)">
</text><text class="breeze-static-checks-r5" x="0" y="898.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-36)">│</text><text class="breeze-static-checks-r7" x="451.4" y="898.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-36)">check-template-context-variable-in-sync&#160;|&#160;check-template-fields-valid&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="898.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-36)">│</text><text class="breeze-static-checks-r1" x="1464" y="898.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-36)">
</text><text class="breeze-static-checks-r5" x="0" y="922.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-37)">│</text><text class="breeze-static-checks-r7" x="451.4" y="922.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-37)">check-tests-in-the-right-folders&#160;|&#160;check-tests-unittest-testcase&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="922.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-37)">│</text><text class="breeze-static-checks-r1" x="1464" y="922.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-37)">
</text><text class="breeze-static-checks-r5" x="0" y="947.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-38)">│</text><text class="breeze-static-checks-r7" x="451.4" y="947.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-38)">check-urlparse-usage-in-code&#160;|&#160;check-xml&#160;|&#160;check-zip-file-is-not-committed&#160;|&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="947.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-38)">│</text><text class="breeze-static-checks-r1" x="1464" y="947.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-38)">
</text><text class="breeze-static-checks-r5" x="0" y="971.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-39)">│</text><text class="breeze-static-checks-r7" x="451.4" y="971.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-39)">codespell&#160;|&#160;compile-fab-assets&#160;|&#160;compile-ui-assets&#160;|&#160;compile-ui-assets-dev&#160;|&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="971.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-39)">│</text><text class="breeze-static-checks-r1" x="1464" y="971.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-39)">
</text><text class="breeze-static-checks-r5" x="0" y="996" textLength="12.2" clip-path="url(#breeze-static-checks-line-40)">│</text><text class="breeze-static-checks-r7" x="451.4" y="996" textLength="988.2" clip-path="url(#breeze-static-checks-line-40)">create-missing-init-py-files-tests&#160;|&#160;debug-statements&#160;|&#160;detect-private-key&#160;|&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="996" textLength="12.2" clip-path="url(#breeze-static-checks-line-40)">│</text><text class="breeze-static-checks-r1" x="1464" y="996" textLength="12.2" clip-path="url(#breeze-static-checks-line-40)">
</text><text class="breeze-static-checks-r5" x="0" y="1020.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-41)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1020.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-41)">doctoc&#160;|&#160;end-of-file-fixer&#160;|&#160;fix-encoding-pragma&#160;|&#160;flynt&#160;|&#160;gci&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1020.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-41)">│</text><text class="breeze-static-checks-r1" x="1464" y="1020.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-41)">
</text><text class="breeze-static-checks-r5" x="0" y="1044.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-42)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1044.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-42)">generate-airflow-diagrams&#160;|&#160;generate-airflowctl-datamodels&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1044.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-42)">│</text><text class="breeze-static-checks-r1" x="1464" y="1044.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-42)">
</text><text class="breeze-static-checks-r5" x="0" y="1069.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-43)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1069.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-43)">generate-airflowctl-help-images&#160;|&#160;generate-openapi-spec&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1069.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-43)">│</text><text class="breeze-static-checks-r1" x="1464" y="1069.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-43)">
</text><text class="breeze-static-checks-r5" x="0" y="1093.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-44)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1093.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-44)">generate-openapi-spec-fab&#160;|&#160;generate-pypi-readme&#160;|&#160;generate-tasksdk-datamodels&#160;|&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1093.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-44)">│</text><text class="breeze-static-checks-r1" x="1464" y="1093.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-44)">
</text><text class="breeze-static-checks-r5" x="0" y="1118" textLength="12.2" clip-path="url(#breeze-static-checks-line-45)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1118" textLength="988.2" clip-path="url(#breeze-static-checks-line-45)">generate-volumes-for-sources&#160;|&#160;gofmt&#160;|&#160;identity&#160;|&#160;insert-license&#160;|&#160;kubeconform&#160;|&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1118" textLength="12.2" clip-path="url(#breeze-static-checks-line-45)">│</text><text class="breeze-static-checks-r1" x="1464" y="1118" textLength="12.2" clip-path="url(#breeze-static-checks-line-45)">
</text><text class="breeze-static-checks-r5" x="0" y="1142.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-46)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1142.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-46)">lint-chart-schema&#160;|&#160;lint-dockerfile&#160;|&#160;lint-helm-chart&#160;|&#160;lint-json-schema&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1142.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-46)">│</text><text class="breeze-static-checks-r1" x="1464" y="1142.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-46)">
</text><text class="breeze-static-checks-r5" x="0" y="1166.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-47)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1166.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-47)">lint-markdown&#160;|&#160;mixed-line-ending&#160;|&#160;mypy-airflow-core&#160;|&#160;mypy-airflow-ctl&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1166.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-47)">│</text><text class="breeze-static-checks-r1" x="1464" y="1166.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-47)">
</text><text class="breeze-static-checks-r5" x="0" y="1191.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-48)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1191.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-48)">mypy-dev&#160;|&#160;mypy-devel-common&#160;|&#160;mypy-providers&#160;|&#160;mypy-task-sdk&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1191.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-48)">│</text><text class="breeze-static-checks-r1" x="1464" y="1191.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-48)">
</text><text class="breeze-static-checks-r5" x="0" y="1215.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-49)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1215.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-49)">pretty-format-json&#160;|&#160;pylint&#160;|&#160;python-no-log-warn&#160;|&#160;replace-bad-characters&#160;|&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1215.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-49)">│</text><text class="breeze-static-checks-r1" x="1464" y="1215.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-49)">
</text><text class="breeze-static-checks-r5" x="0" y="1240" textLength="12.2" clip-path="url(#breeze-static-checks-line-50)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1240" textLength="988.2" clip-path="url(#breeze-static-checks-line-50)">rst-backticks&#160;|&#160;ruff&#160;|&#160;ruff-format&#160;|&#160;shellcheck&#160;|&#160;trailing-whitespace&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1240" textLength="12.2" clip-path="url(#breeze-static-checks-line-50)">│</text><text class="breeze-static-checks-r1" x="1464" y="1240" textLength="12.2" clip-path="url(#breeze-static-checks-line-50)">
</text><text class="breeze-static-checks-r5" x="0" y="1264.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-51)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1264.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-51)">ts-compile-format-lint-ui&#160;|&#160;update-black-version&#160;|&#160;update-breeze-cmd-output&#160;|&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1264.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-51)">│</text><text class="breeze-static-checks-r1" x="1464" y="1264.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-51)">
</text><text class="breeze-static-checks-r5" x="0" y="1288.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-52)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1288.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-52)">update-breeze-readme-config-hash&#160;|&#160;update-chart-dependencies&#160;|&#160;update-er-diagram&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1288.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-52)">│</text><text class="breeze-static-checks-r1" x="1464" y="1288.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-52)">
</text><text class="breeze-static-checks-r5" x="0" y="1313.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-53)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1313.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-53)">|&#160;update-in-the-wild-to-be-sorted&#160;|&#160;update-inlined-dockerfile-scripts&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1313.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-53)">│</text><text class="breeze-static-checks-r1" x="1464" y="1313.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-53)">
</text><text class="breeze-static-checks-r5" x="0" y="1337.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-54)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1337.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-54)">update-installed-providers-to-be-sorted&#160;|&#160;update-installers-and-pre-commit&#160;|&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1337.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-54)">│</text><text class="breeze-static-checks-r1" x="1464" y="1337.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-54)">
</text><text class="breeze-static-checks-r5" x="0" y="1362" textLength="12.2" clip-path="url(#breeze-static-checks-line-55)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1362" textLength="988.2" clip-path="url(#breeze-static-checks-line-55)">update-local-yml-file&#160;|&#160;update-migration-references&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1362" textLength="12.2" clip-path="url(#breeze-static-checks-line-55)">│</text><text class="breeze-static-checks-r1" x="1464" y="1362" textLength="12.2" clip-path="url(#breeze-static-checks-line-55)">
</text><text class="breeze-static-checks-r5" x="0" y="1386.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-56)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1386.4" textLength="988.2" clip-path="url(#breeze-static-checks-line-56)">update-providers-build-files&#160;|&#160;update-providers-dependencies&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1386.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-56)">│</text><text class="breeze-static-checks-r1" x="1464" y="1386.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-56)">
</text><text class="breeze-static-checks-r5" x="0" y="1410.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-57)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1410.8" textLength="988.2" clip-path="url(#breeze-static-checks-line-57)">update-pyproject-toml&#160;|&#160;update-reproducible-source-date-epoch&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1410.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-57)">│</text><text class="breeze-static-checks-r1" x="1464" y="1410.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-57)">
</text><text class="breeze-static-checks-r5" x="0" y="1435.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-58)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1435.2" textLength="988.2" clip-path="url(#breeze-static-checks-line-58)">update-spelling-wordlist-to-be-sorted&#160;|&#160;update-supported-versions&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1435.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-58)">│</text><text class="breeze-static-checks-r1" x="1464" y="1435.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-58)">
</text><text class="breeze-static-checks-r5" x="0" y="1459.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-59)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1459.6" textLength="988.2" clip-path="url(#breeze-static-checks-line-59)">update-vendored-in-k8s-json-schema&#160;|&#160;update-version&#160;|&#160;validate-operators-init&#160;|&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1459.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-59)">│</text><text class="breeze-static-checks-r1" x="1464" y="1459.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-59)">
</text><text class="breeze-static-checks-r5" x="0" y="1484" textLength="12.2" clip-path="url(#breeze-static-checks-line-60)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1484" textLength="988.2" clip-path="url(#breeze-static-checks-line-60)">yamllint&#160;|&#160;zizmor)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1484" textLength="12.2" clip-path="url(#breeze-static-checks-line-60)">│</text><text class="breeze-static-checks-r1" x="1464" y="1484" textLength="12.2" clip-path="url(#breeze-static-checks-line-60)">
</text><text class="breeze-static-checks-r5" x="0" y="1508.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-61)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1508.4" textLength="268.4" clip-path="url(#breeze-static-checks-line-61)">--show-diff-on-failure</text><text class="breeze-static-checks-r6" x="402.6" y="1508.4" textLength="24.4" clip-path="url(#breeze-static-checks-line-61)">-s</text><text class="breeze-static-checks-r1" x="451.4" y="1508.4" textLength="524.6" clip-path="url(#breeze-static-checks-line-61)">Show&#160;diff&#160;for&#160;files&#160;modified&#160;by&#160;the&#160;checks.</text><text class="breeze-static-checks-r5" x="1451.8" y="1508.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-61)">│</text><text class="breeze-static-checks-r1" x="1464" y="1508.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-61)">
</text><text class="breeze-static-checks-r5" x="0" y="1532.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-62)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1532.8" textLength="292.8" clip-path="url(#breeze-static-checks-line-62)">--initialize-environment</text><text class="breeze-static-checks-r1" x="451.4" y="1532.8" textLength="549" clip-path="url(#breeze-static-checks-line-62)">Initialize&#160;environment&#160;before&#160;running&#160;checks.</text><text class="breeze-static-checks-r5" x="1451.8" y="1532.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-62)">│</text><text class="breeze-static-checks-r1" x="1464" y="1532.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-62)">
</text><text class="breeze-static-checks-r5" x="0" y="1557.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-63)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1557.2" textLength="353.8" clip-path="url(#breeze-static-checks-line-63)">--max-initialization-attempts</text><text class="breeze-static-checks-r1" x="451.4" y="1557.2" textLength="854" clip-path="url(#breeze-static-checks-line-63)">Maximum&#160;number&#160;of&#160;attempts&#160;to&#160;initialize&#160;environment&#160;before&#160;giving&#160;up.</text><text class="breeze-static-checks-r5" x="1451.8" y="1557.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-63)">│</text><text class="breeze-static-checks-r1" x="1464" y="1557.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-63)">
</text><text class="breeze-static-checks-r5" x="0" y="1581.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-64)">│</text><text class="breeze-static-checks-r7" x="451.4" y="1581.6" textLength="854" clip-path="url(#breeze-static-checks-line-64)">(INTEGER&#160;RANGE)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1581.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-64)">│</text><text class="breeze-static-checks-r1" x="1464" y="1581.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-64)">
</text><text class="breeze-static-checks-r5" x="0" y="1606" textLength="12.2" clip-path="url(#breeze-static-checks-line-65)">│</text><text class="breeze-static-checks-r5" x="451.4" y="1606" textLength="854" clip-path="url(#breeze-static-checks-line-65)">[default:&#160;3;&#160;1&lt;=x&lt;=10]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1606" textLength="12.2" clip-path="url(#breeze-static-checks-line-65)">│</text><text class="breeze-static-checks-r1" x="1464" y="1606" textLength="12.2" clip-path="url(#breeze-static-checks-line-65)">
</text><text class="breeze-static-checks-r5" x="0" y="1630.4" textLength="1464" clip-path="url(#breeze-static-checks-line-66)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-static-checks-r1" x="1464" y="1630.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-66)">
</text><text class="breeze-static-checks-r5" x="0" y="1654.8" textLength="24.4" clip-path="url(#breeze-static-checks-line-67)">╭─</text><text class="breeze-static-checks-r5" x="24.4" y="1654.8" textLength="463.6" clip-path="url(#breeze-static-checks-line-67)">&#160;Selecting&#160;files&#160;to&#160;run&#160;the&#160;checks&#160;on&#160;</text><text class="breeze-static-checks-r5" x="488" y="1654.8" textLength="951.6" clip-path="url(#breeze-static-checks-line-67)">──────────────────────────────────────────────────────────────────────────────</text><text class="breeze-static-checks-r5" x="1439.6" y="1654.8" textLength="24.4" clip-path="url(#breeze-static-checks-line-67)">─╮</text><text class="breeze-static-checks-r1" x="1464" y="1654.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-67)">
</text><text class="breeze-static-checks-r5" x="0" y="1679.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-68)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1679.2" textLength="73.2" clip-path="url(#breeze-static-checks-line-68)">--file</text><text class="breeze-static-checks-r6" x="256.2" y="1679.2" textLength="24.4" clip-path="url(#breeze-static-checks-line-68)">-f</text><text class="breeze-static-checks-r1" x="305" y="1679.2" textLength="427" clip-path="url(#breeze-static-checks-line-68)">List&#160;of&#160;files&#160;to&#160;run&#160;the&#160;checks&#160;on.</text><text class="breeze-static-checks-r7" x="744.2" y="1679.2" textLength="73.2" clip-path="url(#breeze-static-checks-line-68)">(PATH)</text><text class="breeze-static-checks-r5" x="1451.8" y="1679.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-68)">│</text><text class="breeze-static-checks-r1" x="1464" y="1679.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-68)">
</text><text class="breeze-static-checks-r5" x="0" y="1703.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-69)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1703.6" textLength="134.2" clip-path="url(#breeze-static-checks-line-69)">--all-files</text><text class="breeze-static-checks-r6" x="256.2" y="1703.6" textLength="24.4" clip-path="url(#breeze-static-checks-line-69)">-a</text><text class="breeze-static-checks-r1" x="305" y="1703.6" textLength="292.8" clip-path="url(#breeze-static-checks-line-69)">Run&#160;checks&#160;on&#160;all&#160;files.</text><text class="breeze-static-checks-r5" x="1451.8" y="1703.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-69)">│</text><text class="breeze-static-checks-r1" x="1464" y="1703.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-69)">
</text><text class="breeze-static-checks-r5" x="0" y="1728" textLength="12.2" clip-path="url(#breeze-static-checks-line-70)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1728" textLength="146.4" clip-path="url(#breeze-static-checks-line-70)">--commit-ref</text><text class="breeze-static-checks-r6" x="256.2" y="1728" textLength="24.4" clip-path="url(#breeze-static-checks-line-70)">-r</text><text class="breeze-static-checks-r1" x="305" y="1728" textLength="1134.6" clip-path="url(#breeze-static-checks-line-70)">Run&#160;checks&#160;for&#160;this&#160;commit&#160;reference&#160;only&#160;(can&#160;be&#160;any&#160;git&#160;commit-ish&#160;reference).&#160;Mutually&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1728" textLength="12.2" clip-path="url(#breeze-static-checks-line-70)">│</text><text class="breeze-static-checks-r1" x="1464" y="1728" textLength="12.2" clip-path="url(#breeze-static-checks-line-70)">
</text><text class="breeze-static-checks-r5" x="0" y="1752.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-71)">│</text><text class="breeze-static-checks-r1" x="305" y="1752.4" textLength="183" clip-path="url(#breeze-static-checks-line-71)">exclusive&#160;with&#160;</text><text class="breeze-static-checks-r4" x="488" y="1752.4" textLength="158.6" clip-path="url(#breeze-static-checks-line-71)">--last-commit</text><text class="breeze-static-checks-r1" x="646.6" y="1752.4" textLength="793" clip-path="url(#breeze-static-checks-line-71)">.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1752.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-71)">│</text><text class="breeze-static-checks-r1" x="1464" y="1752.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-71)">
</text><text class="breeze-static-checks-r5" x="0" y="1776.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-72)">│</text><text class="breeze-static-checks-r7" x="305" y="1776.8" textLength="1134.6" clip-path="url(#breeze-static-checks-line-72)">(TEXT)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1776.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-72)">│</text><text class="breeze-static-checks-r1" x="1464" y="1776.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-72)">
</text><text class="breeze-static-checks-r5" x="0" y="1801.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-73)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1801.2" textLength="158.6" clip-path="url(#breeze-static-checks-line-73)">--last-commit</text><text class="breeze-static-checks-r6" x="256.2" y="1801.2" textLength="24.4" clip-path="url(#breeze-static-checks-line-73)">-c</text><text class="breeze-static-checks-r1" x="305" y="1801.2" textLength="793" clip-path="url(#breeze-static-checks-line-73)">Run&#160;checks&#160;for&#160;all&#160;files&#160;in&#160;last&#160;commit.&#160;Mutually&#160;exclusive&#160;with&#160;</text><text class="breeze-static-checks-r4" x="1098" y="1801.2" textLength="146.4" clip-path="url(#breeze-static-checks-line-73)">--commit-ref</text><text class="breeze-static-checks-r1" x="1244.4" y="1801.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-73)">.</text><text class="breeze-static-checks-r5" x="1451.8" y="1801.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-73)">│</text><text class="breeze-static-checks-r1" x="1464" y="1801.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-73)">
</text><text class="breeze-static-checks-r5" x="0" y="1825.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-74)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1825.6" textLength="207.4" clip-path="url(#breeze-static-checks-line-74)">--only-my-changes</text><text class="breeze-static-checks-r6" x="256.2" y="1825.6" textLength="24.4" clip-path="url(#breeze-static-checks-line-74)">-m</text><text class="breeze-static-checks-r1" x="305" y="1825.6" textLength="1134.6" clip-path="url(#breeze-static-checks-line-74)">Run&#160;checks&#160;for&#160;commits&#160;belonging&#160;to&#160;my&#160;PR&#160;only:&#160;for&#160;all&#160;commits&#160;between&#160;merge&#160;base&#160;to&#160;`main`&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1825.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-74)">│</text><text class="breeze-static-checks-r1" x="1464" y="1825.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-74)">
</text><text class="breeze-static-checks-r5" x="0" y="1850" textLength="12.2" clip-path="url(#breeze-static-checks-line-75)">│</text><text class="breeze-static-checks-r1" x="305" y="1850" textLength="1134.6" clip-path="url(#breeze-static-checks-line-75)">branch&#160;and&#160;HEAD&#160;of&#160;your&#160;branch.&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="1850" textLength="12.2" clip-path="url(#breeze-static-checks-line-75)">│</text><text class="breeze-static-checks-r1" x="1464" y="1850" textLength="12.2" clip-path="url(#breeze-static-checks-line-75)">
</text><text class="breeze-static-checks-r5" x="0" y="1874.4" textLength="1464" clip-path="url(#breeze-static-checks-line-76)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-static-checks-r1" x="1464" y="1874.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-76)">
</text><text class="breeze-static-checks-r5" x="0" y="1898.8" textLength="24.4" clip-path="url(#breeze-static-checks-line-77)">╭─</text><text class="breeze-static-checks-r5" x="24.4" y="1898.8" textLength="463.6" clip-path="url(#breeze-static-checks-line-77)">&#160;Building&#160;image&#160;before&#160;running&#160;checks&#160;</text><text class="breeze-static-checks-r5" x="488" y="1898.8" textLength="951.6" clip-path="url(#breeze-static-checks-line-77)">──────────────────────────────────────────────────────────────────────────────</text><text class="breeze-static-checks-r5" x="1439.6" y="1898.8" textLength="24.4" clip-path="url(#breeze-static-checks-line-77)">─╮</text><text class="breeze-static-checks-r1" x="1464" y="1898.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-77)">
</text><text class="breeze-static-checks-r5" x="0" y="1923.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-78)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1923.2" textLength="317.2" clip-path="url(#breeze-static-checks-line-78)">--skip-image-upgrade-check</text><text class="breeze-static-checks-r1" x="414.8" y="1923.2" textLength="536.8" clip-path="url(#breeze-static-checks-line-78)">Skip&#160;checking&#160;if&#160;the&#160;CI&#160;image&#160;is&#160;up&#160;to&#160;date.</text><text class="breeze-static-checks-r5" x="1451.8" y="1923.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-78)">│</text><text class="breeze-static-checks-r1" x="1464" y="1923.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-78)">
</text><text class="breeze-static-checks-r5" x="0" y="1947.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-79)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1947.6" textLength="158.6" clip-path="url(#breeze-static-checks-line-79)">--force-build</text><text class="breeze-static-checks-r1" x="414.8" y="1947.6" textLength="707.6" clip-path="url(#breeze-static-checks-line-79)">Force&#160;image&#160;build&#160;no&#160;matter&#160;if&#160;it&#160;is&#160;determined&#160;as&#160;needed.</text><text class="breeze-static-checks-r5" x="1451.8" y="1947.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-79)">│</text><text class="breeze-static-checks-r1" x="1464" y="1947.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-79)">
</text><text class="breeze-static-checks-r5" x="0" y="1972" textLength="12.2" clip-path="url(#breeze-static-checks-line-80)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1972" textLength="231.8" clip-path="url(#breeze-static-checks-line-80)">--github-repository</text><text class="breeze-static-checks-r6" x="366" y="1972" textLength="24.4" clip-path="url(#breeze-static-checks-line-80)">-g</text><text class="breeze-static-checks-r1" x="414.8" y="1972" textLength="585.6" clip-path="url(#breeze-static-checks-line-80)">GitHub&#160;repository&#160;used&#160;to&#160;pull,&#160;push&#160;run&#160;images.</text><text class="breeze-static-checks-r7" x="1012.6" y="1972" textLength="73.2" clip-path="url(#breeze-static-checks-line-80)">(TEXT)</text><text class="breeze-static-checks-r5" x="1098" y="1972" textLength="305" clip-path="url(#breeze-static-checks-line-80)">[default:&#160;apache/airflow]</text><text class="breeze-static-checks-r5" x="1451.8" y="1972" textLength="12.2" clip-path="url(#breeze-static-checks-line-80)">│</text><text class="breeze-static-checks-r1" x="1464" y="1972" textLength="12.2" clip-path="url(#breeze-static-checks-line-80)">
</text><text class="breeze-static-checks-r5" x="0" y="1996.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-81)">│</text><text class="breeze-static-checks-r4" x="24.4" y="1996.4" textLength="109.8" clip-path="url(#breeze-static-checks-line-81)">--builder</text><text class="breeze-static-checks-r1" x="414.8" y="1996.4" textLength="756.4" clip-path="url(#breeze-static-checks-line-81)">Buildx&#160;builder&#160;used&#160;to&#160;perform&#160;`docker&#160;buildx&#160;build`&#160;commands.</text><text class="breeze-static-checks-r7" x="1183.4" y="1996.4" textLength="73.2" clip-path="url(#breeze-static-checks-line-81)">(TEXT)</text><text class="breeze-static-checks-r5" x="1451.8" y="1996.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-81)">│</text><text class="breeze-static-checks-r1" x="1464" y="1996.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-81)">
</text><text class="breeze-static-checks-r5" x="0" y="2020.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-82)">│</text><text class="breeze-static-checks-r5" x="414.8" y="2020.8" textLength="756.4" clip-path="url(#breeze-static-checks-line-82)">[default:&#160;autodetect]&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</text><text class="breeze-static-checks-r5" x="1451.8" y="2020.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-82)">│</text><text class="breeze-static-checks-r1" x="1464" y="2020.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-82)">
</text><text class="breeze-static-checks-r5" x="0" y="2045.2" textLength="1464" clip-path="url(#breeze-static-checks-line-83)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-static-checks-r1" x="1464" y="2045.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-83)">
</text><text class="breeze-static-checks-r5" x="0" y="2069.6" textLength="24.4" clip-path="url(#breeze-static-checks-line-84)">╭─</text><text class="breeze-static-checks-r5" x="24.4" y="2069.6" textLength="195.2" clip-path="url(#breeze-static-checks-line-84)">&#160;Common&#160;options&#160;</text><text class="breeze-static-checks-r5" x="219.6" y="2069.6" textLength="1220" clip-path="url(#breeze-static-checks-line-84)">────────────────────────────────────────────────────────────────────────────────────────────────────</text><text class="breeze-static-checks-r5" x="1439.6" y="2069.6" textLength="24.4" clip-path="url(#breeze-static-checks-line-84)">─╮</text><text class="breeze-static-checks-r1" x="1464" y="2069.6" textLength="12.2" clip-path="url(#breeze-static-checks-line-84)">
</text><text class="breeze-static-checks-r5" x="0" y="2094" textLength="12.2" clip-path="url(#breeze-static-checks-line-85)">│</text><text class="breeze-static-checks-r4" x="24.4" y="2094" textLength="109.8" clip-path="url(#breeze-static-checks-line-85)">--dry-run</text><text class="breeze-static-checks-r6" x="158.6" y="2094" textLength="24.4" clip-path="url(#breeze-static-checks-line-85)">-D</text><text class="breeze-static-checks-r1" x="207.4" y="2094" textLength="719.8" clip-path="url(#breeze-static-checks-line-85)">If&#160;dry-run&#160;is&#160;set,&#160;commands&#160;are&#160;only&#160;printed,&#160;not&#160;executed.</text><text class="breeze-static-checks-r5" x="1451.8" y="2094" textLength="12.2" clip-path="url(#breeze-static-checks-line-85)">│</text><text class="breeze-static-checks-r1" x="1464" y="2094" textLength="12.2" clip-path="url(#breeze-static-checks-line-85)">
</text><text class="breeze-static-checks-r5" x="0" y="2118.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-86)">│</text><text class="breeze-static-checks-r4" x="24.4" y="2118.4" textLength="109.8" clip-path="url(#breeze-static-checks-line-86)">--verbose</text><text class="breeze-static-checks-r6" x="158.6" y="2118.4" textLength="24.4" clip-path="url(#breeze-static-checks-line-86)">-v</text><text class="breeze-static-checks-r1" x="207.4" y="2118.4" textLength="585.6" clip-path="url(#breeze-static-checks-line-86)">Print&#160;verbose&#160;information&#160;about&#160;performed&#160;steps.</text><text class="breeze-static-checks-r5" x="1451.8" y="2118.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-86)">│</text><text class="breeze-static-checks-r1" x="1464" y="2118.4" textLength="12.2" clip-path="url(#breeze-static-checks-line-86)">
</text><text class="breeze-static-checks-r5" x="0" y="2142.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-87)">│</text><text class="breeze-static-checks-r4" x="24.4" y="2142.8" textLength="73.2" clip-path="url(#breeze-static-checks-line-87)">--help</text><text class="breeze-static-checks-r6" x="158.6" y="2142.8" textLength="24.4" clip-path="url(#breeze-static-checks-line-87)">-h</text><text class="breeze-static-checks-r1" x="207.4" y="2142.8" textLength="329.4" clip-path="url(#breeze-static-checks-line-87)">Show&#160;this&#160;message&#160;and&#160;exit.</text><text class="breeze-static-checks-r5" x="1451.8" y="2142.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-87)">│</text><text class="breeze-static-checks-r1" x="1464" y="2142.8" textLength="12.2" clip-path="url(#breeze-static-checks-line-87)">
</text><text class="breeze-static-checks-r5" x="0" y="2167.2" textLength="1464" clip-path="url(#breeze-static-checks-line-88)">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</text><text class="breeze-static-checks-r1" x="1464" y="2167.2" textLength="12.2" clip-path="url(#breeze-static-checks-line-88)">
</text>
    </g>
    </g>
</svg>
