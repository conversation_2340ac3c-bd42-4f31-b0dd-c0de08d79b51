 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.



.. _howto/operator:TimeDeltaSensor:

TimeDeltaSensor
===============

Use the :class:`~airflow.providers.standard.sensors.time_delta.TimeDeltaSensor` to end sensing after specific time.


.. exampleinclude:: /../tests/system/standard/example_sensors.py
    :language: python
    :dedent: 4
    :start-after: [START example_time_delta_sensor]
    :end-before: [END example_time_delta_sensor]


.. _howto/operator:TimeDeltaSensorAsync:

TimeDeltaSensorAsync
====================

Use the :class:`~airflow.providers.standard.sensors.time_delta.TimeDeltaSensorAsync` to end sensing after specific time.
It is an async version of the operator and requires Triggerer to run.


.. exampleinclude:: /../tests/system/standard/example_sensors.py
    :language: python
    :dedent: 4
    :start-after: [START example_time_delta_sensor_async]
    :end-before: [END example_time_delta_sensor_async]



.. _howto/operator:TimeSensor:

TimeSensor
==========

Use the :class:`~airflow.providers.standard.sensors.time_sensor.TimeSensor` to end sensing after time specified. ``TimeSensor`` can be run in deferrable mode, if a Triggerer is available.

Time will be evaluated against ``data_interval_end`` if present for the dag run, otherwise ``run_after`` will be used.

.. exampleinclude:: /../tests/system/standard/example_sensors.py
    :language: python
    :dedent: 4
    :start-after: [START example_time_sensors]
    :end-before: [END example_time_sensors]


.. _howto/operator:DayOfWeekSensor:

DayOfWeekSensor
===============

Use the :class:`~airflow.sensors.weekday.DayOfWeekSensor` to sense for day of week.

.. exampleinclude:: /../tests/system/standard/example_sensors.py
    :language: python
    :dedent: 4
    :start-after: [START example_day_of_week_sensor]
    :end-before: [END example_day_of_week_sensor]
