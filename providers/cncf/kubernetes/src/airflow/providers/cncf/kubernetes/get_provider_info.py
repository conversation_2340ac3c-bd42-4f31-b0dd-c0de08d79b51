# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# NOTE! THIS FILE IS AUTOMATICALLY GENERATED AND WILL BE OVERWRITTEN!
#
# IF YOU WANT TO MODIFY THIS FILE, YOU SHOULD MODIFY THE TEMPLATE
# `get_provider_info_TEMPLATE.py.jinja2` IN the `dev/breeze/src/airflow_breeze/templates` DIRECTORY


def get_provider_info():
    return {
        "package-name": "apache-airflow-providers-cncf-kubernetes",
        "name": "Kubernetes",
        "description": "`Kubernetes <https://kubernetes.io/>`__\n",
        "integrations": [
            {
                "integration-name": "Kubernetes",
                "external-doc-url": "https://kubernetes.io/",
                "how-to-guide": ["/docs/apache-airflow-providers-cncf-kubernetes/operators.rst"],
                "logo": "/docs/integration-logos/Kubernetes.png",
                "tags": ["software"],
            },
            {
                "integration-name": "Spark on Kubernetes",
                "external-doc-url": "https://github.com/GoogleCloudPlatform/spark-on-k8s-operator",
                "logo": "/docs/integration-logos/Spark-On-Kubernetes.png",
                "tags": ["software"],
            },
        ],
        "operators": [
            {
                "integration-name": "Kubernetes",
                "python-modules": [
                    "airflow.providers.cncf.kubernetes.operators.custom_object_launcher",
                    "airflow.providers.cncf.kubernetes.operators.kueue",
                    "airflow.providers.cncf.kubernetes.operators.pod",
                    "airflow.providers.cncf.kubernetes.operators.spark_kubernetes",
                    "airflow.providers.cncf.kubernetes.operators.resource",
                    "airflow.providers.cncf.kubernetes.operators.job",
                ],
            }
        ],
        "sensors": [
            {
                "integration-name": "Kubernetes",
                "python-modules": ["airflow.providers.cncf.kubernetes.sensors.spark_kubernetes"],
            }
        ],
        "hooks": [
            {
                "integration-name": "Kubernetes",
                "python-modules": ["airflow.providers.cncf.kubernetes.hooks.kubernetes"],
            }
        ],
        "triggers": [
            {
                "integration-name": "Kubernetes",
                "python-modules": [
                    "airflow.providers.cncf.kubernetes.triggers.pod",
                    "airflow.providers.cncf.kubernetes.triggers.job",
                ],
            }
        ],
        "connection-types": [
            {
                "hook-class-name": "airflow.providers.cncf.kubernetes.hooks.kubernetes.KubernetesHook",
                "connection-type": "kubernetes",
            }
        ],
        "task-decorators": [
            {
                "class-name": "airflow.providers.cncf.kubernetes.decorators.kubernetes.kubernetes_task",
                "name": "kubernetes",
            },
            {
                "class-name": "airflow.providers.cncf.kubernetes.decorators.kubernetes_cmd.kubernetes_cmd_task",
                "name": "kubernetes_cmd",
            },
        ],
        "config": {
            "local_kubernetes_executor": {
                "description": "This section only applies if you are using the ``LocalKubernetesExecutor`` in\n``[core]`` section above\n",
                "options": {
                    "kubernetes_queue": {
                        "description": "Define when to send a task to ``KubernetesExecutor`` when using ``LocalKubernetesExecutor``.\nWhen the queue of a task is the value of ``kubernetes_queue`` (default ``kubernetes``),\nthe task is executed via ``KubernetesExecutor``,\notherwise via ``LocalExecutor``\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "kubernetes",
                    }
                },
            },
            "kubernetes_executor": {
                "description": None,
                "options": {
                    "api_client_retry_configuration": {
                        "description": "Kwargs to override the default urllib3 Retry used in the kubernetes API client\n",
                        "version_added": None,
                        "type": "string",
                        "example": '{ "total": 3, "backoff_factor": 0.5 }',
                        "default": "",
                    },
                    "logs_task_metadata": {
                        "description": "Flag to control the information added to kubernetes executor logs for better traceability\n",
                        "version_added": None,
                        "type": "boolean",
                        "example": None,
                        "default": "False",
                    },
                    "pod_template_file": {
                        "description": "Path to the YAML pod file that forms the basis for KubernetesExecutor workers.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                        "see_also": ":ref:`concepts:pod_template_file`",
                    },
                    "worker_container_repository": {
                        "description": "The repository of the Kubernetes Image for the Worker to Run\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                    },
                    "worker_container_tag": {
                        "description": "The tag of the Kubernetes Image for the Worker to Run\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                    },
                    "namespace": {
                        "description": "The Kubernetes namespace where airflow workers should be created. Defaults to ``default``\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "default",
                    },
                    "delete_worker_pods": {
                        "description": "If True, all worker pods will be deleted upon termination\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "True",
                    },
                    "delete_worker_pods_on_failure": {
                        "description": "If False (and delete_worker_pods is True),\nfailed worker pods will not be deleted so users can investigate them.\nThis only prevents removal of worker pods where the worker itself failed,\nnot when the task it ran failed.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "False",
                    },
                    "worker_pod_pending_fatal_container_state_reasons": {
                        "description": "If the worker pods are in a pending state due to a fatal container\nstate reasons, then fail the task and delete the worker pod\nif delete_worker_pods is True and delete_worker_pods_on_failure is True.\n",
                        "version_added": "8.1.0",
                        "type": "string",
                        "example": None,
                        "default": "CreateContainerConfigError,ErrImagePull,CreateContainerError,ImageInspectError,InvalidImageName",
                    },
                    "worker_pods_creation_batch_size": {
                        "description": 'Number of Kubernetes Worker Pod creation calls per scheduler loop.\nNote that the current default of "1" will only launch a single pod\nper-heartbeat. It is HIGHLY recommended that users increase this\nnumber to match the tolerance of their kubernetes cluster for\nbetter performance.\n',
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "1",
                    },
                    "multi_namespace_mode": {
                        "description": "Allows users to launch pods in multiple namespaces.\nWill require creating a cluster-role for the scheduler,\nor use multi_namespace_mode_namespace_list configuration.\n",
                        "version_added": None,
                        "type": "boolean",
                        "example": None,
                        "default": "False",
                    },
                    "multi_namespace_mode_namespace_list": {
                        "description": "If multi_namespace_mode is True while scheduler does not have a cluster-role,\ngive the list of namespaces where the scheduler will schedule jobs\nScheduler needs to have the necessary permissions in these namespaces.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                    },
                    "in_cluster": {
                        "description": "Use the service account kubernetes gives to pods to connect to kubernetes cluster.\nIt's intended for clients that expect to be running inside a pod running on kubernetes.\nIt will raise an exception if called from a process not running in a kubernetes environment.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "True",
                    },
                    "cluster_context": {
                        "description": "When running with in_cluster=False change the default cluster_context or config_file\noptions to Kubernetes client. Leave blank these to use default behaviour like ``kubectl`` has.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": None,
                    },
                    "config_file": {
                        "description": "Path to the kubernetes configfile to be used when ``in_cluster`` is set to False\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": None,
                    },
                    "kube_client_request_args": {
                        "description": "Keyword parameters to pass while calling a kubernetes client core_v1_api methods\nfrom Kubernetes Executor provided as a single line formatted JSON dictionary string.\nList of supported params are similar for all core_v1_apis, hence a single config\nvariable for all apis. See:\nhttps://raw.githubusercontent.com/kubernetes-client/python/41f11a09995efcd0142e25946adc7591431bfb2f/kubernetes/client/api/core_v1_api.py\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                    },
                    "delete_option_kwargs": {
                        "description": "Optional keyword arguments to pass to the ``delete_namespaced_pod`` kubernetes client\n``core_v1_api`` method when using the Kubernetes Executor.\nThis should be an object and can contain any of the options listed in the ``v1DeleteOptions``\nclass defined here:\nhttps://github.com/kubernetes-client/python/blob/41f11a09995efcd0142e25946adc7591431bfb2f/kubernetes/client/models/v1_delete_options.py#L19\n",
                        "version_added": None,
                        "type": "string",
                        "example": '{"grace_period_seconds": 10}',
                        "default": "",
                    },
                    "enable_tcp_keepalive": {
                        "description": "Enables TCP keepalive mechanism. This prevents Kubernetes API requests to hang indefinitely\nwhen idle connection is time-outed on services like cloud load balancers or firewalls.\n",
                        "version_added": None,
                        "type": "boolean",
                        "example": None,
                        "default": "True",
                    },
                    "tcp_keep_idle": {
                        "description": "When the `enable_tcp_keepalive` option is enabled, TCP probes a connection that has\nbeen idle for `tcp_keep_idle` seconds.\n",
                        "version_added": None,
                        "type": "integer",
                        "example": None,
                        "default": "120",
                    },
                    "tcp_keep_intvl": {
                        "description": "When the `enable_tcp_keepalive` option is enabled, if Kubernetes API does not respond\nto a keepalive probe, TCP retransmits the probe after `tcp_keep_intvl` seconds.\n",
                        "version_added": None,
                        "type": "integer",
                        "example": None,
                        "default": "30",
                    },
                    "tcp_keep_cnt": {
                        "description": "When the `enable_tcp_keepalive` option is enabled, if Kubernetes API does not respond\nto a keepalive probe, TCP retransmits the probe `tcp_keep_cnt number` of times before\na connection is considered to be broken.\n",
                        "version_added": None,
                        "type": "integer",
                        "example": None,
                        "default": "6",
                    },
                    "verify_ssl": {
                        "description": "Set this to false to skip verifying SSL certificate of Kubernetes python client.\n",
                        "version_added": None,
                        "type": "boolean",
                        "example": None,
                        "default": "True",
                    },
                    "ssl_ca_cert": {
                        "description": "Path to a CA certificate to be used by the Kubernetes client to verify the server's SSL certificate.\n",
                        "version_added": None,
                        "type": "string",
                        "example": None,
                        "default": "",
                    },
                    "task_publish_max_retries": {
                        "description": "The Maximum number of retries for queuing the task to the kubernetes scheduler when\nfailing due to Kube API exceeded quota errors before giving up and marking task as failed.\n-1 for unlimited times.\n",
                        "version_added": None,
                        "type": "integer",
                        "example": None,
                        "default": "0",
                    },
                },
            },
        },
        "executors": ["airflow.providers.cncf.kubernetes.executors.kubernetes_executor.KubernetesExecutor"],
    }
