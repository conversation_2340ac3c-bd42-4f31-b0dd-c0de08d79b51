# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# NOTE! THIS FILE IS AUTOMATICALLY GENERATED AND WILL BE OVERWRITTEN!

# IF YOU WANT TO MODIFY THIS FILE EXCEPT DEPENDENCIES, YOU SHOULD MODIFY THE TEMPLATE
# `pyproject_TEMPLATE.toml.jinja2` IN the `dev/breeze/src/airflow_breeze/templates` DIRECTORY
[build-system]
requires = ["flit_core==3.12.0"]
build-backend = "flit_core.buildapi"

[project]
name = "apache-airflow-providers-neo4j"
version = "3.9.0"
description = "Provider package apache-airflow-providers-neo4j for Apache Airflow"
readme = "README.rst"
authors = [
    {name="Apache Software Foundation", email="<EMAIL>"},
]
maintainers = [
    {name="Apache Software Foundation", email="<EMAIL>"},
]
keywords = [ "airflow-provider", "neo4j", "airflow", "integration" ]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Console",
    "Environment :: Web Environment",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "Framework :: Apache Airflow",
    "Framework :: Apache Airflow :: Provider",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: System :: Monitoring",
]
requires-python = "~=3.9"

# The dependencies should be modified in place in the generated file.
# Any change in the dependencies is preserved when the file is regenerated
# Make sure to run ``breeze static-checks --type update-providers-dependencies --all-files``
# After you modify the dependencies, and rebuild your Breeze CI image with ``breeze ci-image build``
dependencies = [
    "apache-airflow>=2.10.0",
    "neo4j>=5.20.0",
]

[dependency-groups]
dev = [
    "apache-airflow",
    "apache-airflow-task-sdk",
    "apache-airflow-devel-common",
    # Additional devel dependencies (do not remove this line and add extra development dependencies)
]

# To build docs:
#
#    uv run --group docs build-docs
#
# To enable auto-refreshing build with server:
#
#    uv run --group docs build-docs --autobuild
#
# To see more options:
#
#    uv run --group docs build-docs --help
#
docs = [
    "apache-airflow-devel-common[docs]"
]

[tool.uv.sources]
# These names must match the names as defined in the pyproject.toml of the workspace items,
# *not* the workspace folder paths
apache-airflow = {workspace = true}
apache-airflow-devel-common = {workspace = true}
apache-airflow-task-sdk = {workspace = true}
apache-airflow-providers-common-sql = {workspace = true}
apache-airflow-providers-standard = {workspace = true}

[project.urls]
"Documentation" = "https://airflow.apache.org/docs/apache-airflow-providers-neo4j/3.9.0"
"Changelog" = "https://airflow.apache.org/docs/apache-airflow-providers-neo4j/3.9.0/changelog.html"
"Bug Tracker" = "https://github.com/apache/airflow/issues"
"Source Code" = "https://github.com/apache/airflow"
"Slack Chat" = "https://s.apache.org/airflow-slack"
"Mastodon" = "https://fosstodon.org/@airflow"
"YouTube" = "https://www.youtube.com/channel/UCSXwxpWZQ7XZ1WL3wqevChA/"

[project.entry-points."apache_airflow_provider"]
provider_info = "airflow.providers.neo4j.get_provider_info:get_provider_info"

[tool.flit.module]
name = "airflow.providers.neo4j"
