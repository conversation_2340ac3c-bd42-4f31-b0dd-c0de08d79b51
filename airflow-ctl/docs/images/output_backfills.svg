<svg class="rich-terminal" viewBox="0 0 994 464.79999999999995" xmlns="http://www.w3.org/2000/svg">
    <!-- Generated with <PERSON> https://www.textualize.io -->
    <style>

    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Regular"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Regular.woff") format("woff");
        font-style: normal;
        font-weight: 400;
    }
    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Bold"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Bold.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Bold.woff") format("woff");
        font-style: bold;
        font-weight: 700;
    }

    .terminal-962206658-matrix {
        font-family: Fira Code, monospace;
        font-size: 20px;
        line-height: 24.4px;
        font-variant-east-asian: full-width;
    }

    .terminal-962206658-title {
        font-size: 18px;
        font-weight: bold;
        font-family: arial;
    }

    .terminal-962206658-r1 { fill: #c5c8c6 }
    </style>

    <defs>
    <clipPath id="terminal-962206658-clip-terminal">
      <rect x="0" y="0" width="975.0" height="413.79999999999995" />
    </clipPath>
    <clipPath id="terminal-962206658-line-0">
    <rect x="0" y="1.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-1">
    <rect x="0" y="25.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-2">
    <rect x="0" y="50.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-3">
    <rect x="0" y="74.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-4">
    <rect x="0" y="99.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-5">
    <rect x="0" y="123.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-6">
    <rect x="0" y="147.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-7">
    <rect x="0" y="172.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-8">
    <rect x="0" y="196.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-9">
    <rect x="0" y="221.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-10">
    <rect x="0" y="245.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-11">
    <rect x="0" y="269.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-12">
    <rect x="0" y="294.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-13">
    <rect x="0" y="318.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-14">
    <rect x="0" y="343.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-962206658-line-15">
    <rect x="0" y="367.5" width="976" height="24.65"/>
            </clipPath>
    </defs>

    <rect fill="#292929" stroke="rgba(255,255,255,0.35)" stroke-width="1" x="1" y="1" width="992" height="462.8" rx="8"/><text class="terminal-962206658-title" fill="#c5c8c6" text-anchor="middle" x="496" y="27">Command:&#160;backfills</text>
            <g transform="translate(26,22)">
            <circle cx="0" cy="0" r="7" fill="#ff5f57"/>
            <circle cx="22" cy="0" r="7" fill="#febc2e"/>
            <circle cx="44" cy="0" r="7" fill="#28c840"/>
            </g>

    <g transform="translate(9, 41)" clip-path="url(#terminal-962206658-clip-terminal)">

    <g class="terminal-962206658-matrix">
    <text class="terminal-962206658-r1" x="0" y="20" textLength="536.8" clip-path="url(#terminal-962206658-line-0)">Usage:&#160;airflowctl&#160;backfills&#160;[-h]&#160;COMMAND&#160;...</text><text class="terminal-962206658-r1" x="976" y="20" textLength="12.2" clip-path="url(#terminal-962206658-line-0)">
</text><text class="terminal-962206658-r1" x="976" y="44.4" textLength="12.2" clip-path="url(#terminal-962206658-line-1)">
</text><text class="terminal-962206658-r1" x="0" y="68.8" textLength="341.6" clip-path="url(#terminal-962206658-line-2)">Perform&#160;Backfills&#160;operations</text><text class="terminal-962206658-r1" x="976" y="68.8" textLength="12.2" clip-path="url(#terminal-962206658-line-2)">
</text><text class="terminal-962206658-r1" x="976" y="93.2" textLength="12.2" clip-path="url(#terminal-962206658-line-3)">
</text><text class="terminal-962206658-r1" x="0" y="117.6" textLength="256.2" clip-path="url(#terminal-962206658-line-4)">Positional&#160;Arguments:</text><text class="terminal-962206658-r1" x="976" y="117.6" textLength="12.2" clip-path="url(#terminal-962206658-line-4)">
</text><text class="terminal-962206658-r1" x="0" y="142" textLength="109.8" clip-path="url(#terminal-962206658-line-5)">&#160;&#160;COMMAND</text><text class="terminal-962206658-r1" x="976" y="142" textLength="12.2" clip-path="url(#terminal-962206658-line-5)">
</text><text class="terminal-962206658-r1" x="0" y="166.4" textLength="512.4" clip-path="url(#terminal-962206658-line-6)">&#160;&#160;&#160;&#160;cancel&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;cancel&#160;operation</text><text class="terminal-962206658-r1" x="976" y="166.4" textLength="12.2" clip-path="url(#terminal-962206658-line-6)">
</text><text class="terminal-962206658-r1" x="0" y="190.8" textLength="512.4" clip-path="url(#terminal-962206658-line-7)">&#160;&#160;&#160;&#160;create&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;create&#160;operation</text><text class="terminal-962206658-r1" x="976" y="190.8" textLength="12.2" clip-path="url(#terminal-962206658-line-7)">
</text><text class="terminal-962206658-r1" x="0" y="215.2" textLength="219.6" clip-path="url(#terminal-962206658-line-8)">&#160;&#160;&#160;&#160;create-dry-run</text><text class="terminal-962206658-r1" x="976" y="215.2" textLength="12.2" clip-path="url(#terminal-962206658-line-8)">
</text><text class="terminal-962206658-r1" x="0" y="239.6" textLength="610" clip-path="url(#terminal-962206658-line-9)">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;create_dry_run&#160;operation</text><text class="terminal-962206658-r1" x="976" y="239.6" textLength="12.2" clip-path="url(#terminal-962206658-line-9)">
</text><text class="terminal-962206658-r1" x="0" y="264" textLength="475.8" clip-path="url(#terminal-962206658-line-10)">&#160;&#160;&#160;&#160;get&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;get&#160;operation</text><text class="terminal-962206658-r1" x="976" y="264" textLength="12.2" clip-path="url(#terminal-962206658-line-10)">
</text><text class="terminal-962206658-r1" x="0" y="288.4" textLength="488" clip-path="url(#terminal-962206658-line-11)">&#160;&#160;&#160;&#160;list&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;list&#160;operation</text><text class="terminal-962206658-r1" x="976" y="288.4" textLength="12.2" clip-path="url(#terminal-962206658-line-11)">
</text><text class="terminal-962206658-r1" x="0" y="312.8" textLength="500.2" clip-path="url(#terminal-962206658-line-12)">&#160;&#160;&#160;&#160;pause&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;pause&#160;operation</text><text class="terminal-962206658-r1" x="976" y="312.8" textLength="12.2" clip-path="url(#terminal-962206658-line-12)">
</text><text class="terminal-962206658-r1" x="0" y="337.2" textLength="524.6" clip-path="url(#terminal-962206658-line-13)">&#160;&#160;&#160;&#160;unpause&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;unpause&#160;operation</text><text class="terminal-962206658-r1" x="976" y="337.2" textLength="12.2" clip-path="url(#terminal-962206658-line-13)">
</text><text class="terminal-962206658-r1" x="976" y="361.6" textLength="12.2" clip-path="url(#terminal-962206658-line-14)">
</text><text class="terminal-962206658-r1" x="0" y="386" textLength="231.8" clip-path="url(#terminal-962206658-line-15)">Optional&#160;Arguments:</text><text class="terminal-962206658-r1" x="976" y="386" textLength="12.2" clip-path="url(#terminal-962206658-line-15)">
</text><text class="terminal-962206658-r1" x="0" y="410.4" textLength="597.8" clip-path="url(#terminal-962206658-line-16)">&#160;&#160;-h,&#160;--help&#160;&#160;&#160;&#160;&#160;&#160;show&#160;this&#160;help&#160;message&#160;and&#160;exit</text><text class="terminal-962206658-r1" x="976" y="410.4" textLength="12.2" clip-path="url(#terminal-962206658-line-16)">
</text>
    </g>
    </g>
</svg>
