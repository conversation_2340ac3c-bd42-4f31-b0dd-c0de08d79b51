<svg class="rich-terminal" viewBox="0 0 994 660.0" xmlns="http://www.w3.org/2000/svg">
    <!-- Generated with <PERSON> https://www.textualize.io -->
    <style>

    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Regular"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Regular.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Regular.woff") format("woff");
        font-style: normal;
        font-weight: 400;
    }
    @font-face {
        font-family: "Fira Code";
        src: local("FiraCode-Bold"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff2/FiraCode-Bold.woff2") format("woff2"),
                url("https://cdnjs.cloudflare.com/ajax/libs/firacode/6.2.0/woff/FiraCode-Bold.woff") format("woff");
        font-style: bold;
        font-weight: 700;
    }

    .terminal-1120912380-matrix {
        font-family: Fira Code, monospace;
        font-size: 20px;
        line-height: 24.4px;
        font-variant-east-asian: full-width;
    }

    .terminal-1120912380-title {
        font-size: 18px;
        font-weight: bold;
        font-family: arial;
    }

    .terminal-1120912380-r1 { fill: #c5c8c6 }
    </style>

    <defs>
    <clipPath id="terminal-1120912380-clip-terminal">
      <rect x="0" y="0" width="975.0" height="609.0" />
    </clipPath>
    <clipPath id="terminal-1120912380-line-0">
    <rect x="0" y="1.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-1">
    <rect x="0" y="25.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-2">
    <rect x="0" y="50.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-3">
    <rect x="0" y="74.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-4">
    <rect x="0" y="99.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-5">
    <rect x="0" y="123.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-6">
    <rect x="0" y="147.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-7">
    <rect x="0" y="172.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-8">
    <rect x="0" y="196.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-9">
    <rect x="0" y="221.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-10">
    <rect x="0" y="245.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-11">
    <rect x="0" y="269.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-12">
    <rect x="0" y="294.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-13">
    <rect x="0" y="318.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-14">
    <rect x="0" y="343.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-15">
    <rect x="0" y="367.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-16">
    <rect x="0" y="391.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-17">
    <rect x="0" y="416.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-18">
    <rect x="0" y="440.7" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-19">
    <rect x="0" y="465.1" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-20">
    <rect x="0" y="489.5" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-21">
    <rect x="0" y="513.9" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-22">
    <rect x="0" y="538.3" width="976" height="24.65"/>
            </clipPath>
<clipPath id="terminal-1120912380-line-23">
    <rect x="0" y="562.7" width="976" height="24.65"/>
            </clipPath>
    </defs>

    <rect fill="#292929" stroke="rgba(255,255,255,0.35)" stroke-width="1" x="1" y="1" width="992" height="658" rx="8"/><text class="terminal-1120912380-title" fill="#c5c8c6" text-anchor="middle" x="496" y="27">Command:&#160;main</text>
            <g transform="translate(26,22)">
            <circle cx="0" cy="0" r="7" fill="#ff5f57"/>
            <circle cx="22" cy="0" r="7" fill="#febc2e"/>
            <circle cx="44" cy="0" r="7" fill="#28c840"/>
            </g>

    <g transform="translate(9, 41)" clip-path="url(#terminal-1120912380-clip-terminal)">

    <g class="terminal-1120912380-matrix">
    <text class="terminal-1120912380-r1" x="0" y="20" textLength="524.6" clip-path="url(#terminal-1120912380-line-0)">Usage:&#160;airflowctl&#160;[-h]&#160;GROUP_OR_COMMAND&#160;...</text><text class="terminal-1120912380-r1" x="976" y="20" textLength="12.2" clip-path="url(#terminal-1120912380-line-0)">
</text><text class="terminal-1120912380-r1" x="976" y="44.4" textLength="12.2" clip-path="url(#terminal-1120912380-line-1)">
</text><text class="terminal-1120912380-r1" x="0" y="68.8" textLength="256.2" clip-path="url(#terminal-1120912380-line-2)">Positional&#160;Arguments:</text><text class="terminal-1120912380-r1" x="976" y="68.8" textLength="12.2" clip-path="url(#terminal-1120912380-line-2)">
</text><text class="terminal-1120912380-r1" x="0" y="93.2" textLength="219.6" clip-path="url(#terminal-1120912380-line-3)">&#160;&#160;GROUP_OR_COMMAND</text><text class="terminal-1120912380-r1" x="976" y="93.2" textLength="12.2" clip-path="url(#terminal-1120912380-line-3)">
</text><text class="terminal-1120912380-r1" x="976" y="117.6" textLength="12.2" clip-path="url(#terminal-1120912380-line-4)">
</text><text class="terminal-1120912380-r1" x="0" y="142" textLength="122" clip-path="url(#terminal-1120912380-line-5)">&#160;&#160;&#160;&#160;Groups</text><text class="terminal-1120912380-r1" x="976" y="142" textLength="12.2" clip-path="url(#terminal-1120912380-line-5)">
</text><text class="terminal-1120912380-r1" x="0" y="166.4" textLength="549" clip-path="url(#terminal-1120912380-line-6)">&#160;&#160;&#160;&#160;&#160;&#160;assets&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Assets&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="166.4" textLength="12.2" clip-path="url(#terminal-1120912380-line-6)">
</text><text class="terminal-1120912380-r1" x="0" y="190.8" textLength="890.6" clip-path="url(#terminal-1120912380-line-7)">&#160;&#160;&#160;&#160;&#160;&#160;auth&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Manage&#160;authentication&#160;for&#160;CLI.&#160;Either&#160;pass&#160;token&#160;from</text><text class="terminal-1120912380-r1" x="976" y="190.8" textLength="12.2" clip-path="url(#terminal-1120912380-line-7)">
</text><text class="terminal-1120912380-r1" x="0" y="215.2" textLength="866.2" clip-path="url(#terminal-1120912380-line-8)">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;environment&#160;variable/parameter&#160;or&#160;pass&#160;username&#160;and</text><text class="terminal-1120912380-r1" x="976" y="215.2" textLength="12.2" clip-path="url(#terminal-1120912380-line-8)">
</text><text class="terminal-1120912380-r1" x="0" y="239.6" textLength="353.8" clip-path="url(#terminal-1120912380-line-9)">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;password.</text><text class="terminal-1120912380-r1" x="976" y="239.6" textLength="12.2" clip-path="url(#terminal-1120912380-line-9)">
</text><text class="terminal-1120912380-r1" x="0" y="264" textLength="585.6" clip-path="url(#terminal-1120912380-line-10)">&#160;&#160;&#160;&#160;&#160;&#160;backfills&#160;&#160;&#160;&#160;&#160;Perform&#160;Backfills&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="264" textLength="12.2" clip-path="url(#terminal-1120912380-line-10)">
</text><text class="terminal-1120912380-r1" x="0" y="288.4" textLength="549" clip-path="url(#terminal-1120912380-line-11)">&#160;&#160;&#160;&#160;&#160;&#160;config&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Config&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="288.4" textLength="12.2" clip-path="url(#terminal-1120912380-line-11)">
</text><text class="terminal-1120912380-r1" x="0" y="312.8" textLength="610" clip-path="url(#terminal-1120912380-line-12)">&#160;&#160;&#160;&#160;&#160;&#160;connections&#160;&#160;&#160;Perform&#160;Connections&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="312.8" textLength="12.2" clip-path="url(#terminal-1120912380-line-12)">
</text><text class="terminal-1120912380-r1" x="0" y="337.2" textLength="512.4" clip-path="url(#terminal-1120912380-line-13)">&#160;&#160;&#160;&#160;&#160;&#160;dag&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Dag&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="337.2" textLength="12.2" clip-path="url(#terminal-1120912380-line-13)">
</text><text class="terminal-1120912380-r1" x="0" y="361.6" textLength="549" clip-path="url(#terminal-1120912380-line-14)">&#160;&#160;&#160;&#160;&#160;&#160;dagrun&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;DagRun&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="361.6" textLength="12.2" clip-path="url(#terminal-1120912380-line-14)">
</text><text class="terminal-1120912380-r1" x="0" y="386" textLength="524.6" clip-path="url(#terminal-1120912380-line-15)">&#160;&#160;&#160;&#160;&#160;&#160;jobs&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Jobs&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="386" textLength="12.2" clip-path="url(#terminal-1120912380-line-15)">
</text><text class="terminal-1120912380-r1" x="0" y="410.4" textLength="536.8" clip-path="url(#terminal-1120912380-line-16)">&#160;&#160;&#160;&#160;&#160;&#160;pools&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Pools&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="410.4" textLength="12.2" clip-path="url(#terminal-1120912380-line-16)">
</text><text class="terminal-1120912380-r1" x="0" y="434.8" textLength="585.6" clip-path="url(#terminal-1120912380-line-17)">&#160;&#160;&#160;&#160;&#160;&#160;providers&#160;&#160;&#160;&#160;&#160;Perform&#160;Providers&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="434.8" textLength="12.2" clip-path="url(#terminal-1120912380-line-17)">
</text><text class="terminal-1120912380-r1" x="0" y="459.2" textLength="585.6" clip-path="url(#terminal-1120912380-line-18)">&#160;&#160;&#160;&#160;&#160;&#160;variables&#160;&#160;&#160;&#160;&#160;Perform&#160;Variables&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="459.2" textLength="12.2" clip-path="url(#terminal-1120912380-line-18)">
</text><text class="terminal-1120912380-r1" x="0" y="483.6" textLength="561.2" clip-path="url(#terminal-1120912380-line-19)">&#160;&#160;&#160;&#160;&#160;&#160;version&#160;&#160;&#160;&#160;&#160;&#160;&#160;Perform&#160;Version&#160;operations</text><text class="terminal-1120912380-r1" x="976" y="483.6" textLength="12.2" clip-path="url(#terminal-1120912380-line-19)">
</text><text class="terminal-1120912380-r1" x="976" y="508" textLength="12.2" clip-path="url(#terminal-1120912380-line-20)">
</text><text class="terminal-1120912380-r1" x="0" y="532.4" textLength="158.6" clip-path="url(#terminal-1120912380-line-21)">&#160;&#160;&#160;&#160;Commands:</text><text class="terminal-1120912380-r1" x="976" y="532.4" textLength="12.2" clip-path="url(#terminal-1120912380-line-21)">
</text><text class="terminal-1120912380-r1" x="976" y="556.8" textLength="12.2" clip-path="url(#terminal-1120912380-line-22)">
</text><text class="terminal-1120912380-r1" x="0" y="581.2" textLength="231.8" clip-path="url(#terminal-1120912380-line-23)">Optional&#160;Arguments:</text><text class="terminal-1120912380-r1" x="976" y="581.2" textLength="12.2" clip-path="url(#terminal-1120912380-line-23)">
</text><text class="terminal-1120912380-r1" x="0" y="605.6" textLength="622.2" clip-path="url(#terminal-1120912380-line-24)">&#160;&#160;-h,&#160;--help&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;show&#160;this&#160;help&#160;message&#160;and&#160;exit</text><text class="terminal-1120912380-r1" x="976" y="605.6" textLength="12.2" clip-path="url(#terminal-1120912380-line-24)">
</text>
    </g>
    </g>
</svg>
