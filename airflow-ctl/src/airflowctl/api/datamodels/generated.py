# generated by datamodel-codegen:
#   filename:  v2-rest-api-generated.yaml
#   version:   0.28.2

from __future__ import annotations

from datetime import datetime, timedelta
from enum import Enum
from typing import Annotated, Any
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, RootModel


class AppBuilderMenuItemResponse(BaseModel):
    """
    Serializer for AppBuilder Menu Item responses.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    name: Annotated[str, Field(title="Name")]
    href: Annotated[str | None, Field(title="Href")] = None
    category: Annotated[str | None, Field(title="Category")] = None


class AppBuilderViewResponse(BaseModel):
    """
    Serializer for AppBuilder View responses.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    name: Annotated[str | None, Field(title="Name")] = None
    category: Annotated[str | None, Field(title="Category")] = None
    view: Annotated[str | None, Field(title="View")] = None
    label: Annotated[str | None, Field(title="Label")] = None


class AssetAliasResponse(BaseModel):
    """
    Asset alias serializer for responses.
    """

    id: Annotated[int, Field(title="Id")]
    name: Annotated[str, Field(title="Name")]
    group: Annotated[str, Field(title="Group")]


class BaseInfoResponse(BaseModel):
    """
    Base info serializer for responses.
    """

    status: Annotated[str | None, Field(title="Status")] = None


class BulkAction(str, Enum):
    """
    Bulk Action to be performed on the used model.
    """

    CREATE = "create"
    DELETE = "delete"
    UPDATE = "update"


class BulkActionNotOnExistence(str, Enum):
    """
    Bulk Action to be taken if the entity does not exist.
    """

    FAIL = "fail"
    SKIP = "skip"


class BulkActionOnExistence(str, Enum):
    """
    Bulk Action to be taken if the entity already exists or not.
    """

    FAIL = "fail"
    SKIP = "skip"
    OVERWRITE = "overwrite"


class BulkActionResponse(BaseModel):
    """
    Serializer for individual bulk action responses.

    Represents the outcome of a single bulk operation (create, update, or delete).
    The response includes a list of successful keys and any errors encountered during the operation.
    This structure helps users understand which key actions succeeded and which failed.
    """

    success: Annotated[
        list[str] | None,
        Field(description="A list of unique id/key representing successful operations.", title="Success"),
    ] = []
    errors: Annotated[
        list[dict[str, Any]] | None,
        Field(
            description="A list of errors encountered during the operation, each containing details about the issue.",
            title="Errors",
        ),
    ] = []


class BulkDeleteActionConnectionBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[str], Field(description="A list of entity id/key to be deleted.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class BulkDeleteActionPoolBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[str], Field(description="A list of entity id/key to be deleted.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class BulkDeleteActionVariableBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[str], Field(description="A list of entity id/key to be deleted.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class BulkResponse(BaseModel):
    """
    Serializer for responses to bulk entity operations.

    This represents the results of create, update, and delete actions performed on entity in bulk.
    Each action (if requested) is represented as a field containing details about successful keys and any encountered errors.
    Fields are populated in the response only if the respective action was part of the request, else are set None.
    """

    create: Annotated[
        BulkActionResponse | None,
        Field(description="Details of the bulk create operation, including successful keys and errors."),
    ] = None
    update: Annotated[
        BulkActionResponse | None,
        Field(description="Details of the bulk update operation, including successful keys and errors."),
    ] = None
    delete: Annotated[
        BulkActionResponse | None,
        Field(description="Details of the bulk delete operation, including successful keys and errors."),
    ] = None


class TaskIds(RootModel[list]):
    root: Annotated[list, Field(max_length=2, min_length=2)]


class ClearTaskInstancesBody(BaseModel):
    """
    Request body for Clear Task Instances endpoint.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dry_run: Annotated[bool | None, Field(title="Dry Run")] = True
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    only_failed: Annotated[bool | None, Field(title="Only Failed")] = True
    only_running: Annotated[bool | None, Field(title="Only Running")] = False
    reset_dag_runs: Annotated[bool | None, Field(title="Reset Dag Runs")] = True
    task_ids: Annotated[list[str | TaskIds] | None, Field(title="Task Ids")] = None
    dag_run_id: Annotated[str | None, Field(title="Dag Run Id")] = None
    include_upstream: Annotated[bool | None, Field(title="Include Upstream")] = False
    include_downstream: Annotated[bool | None, Field(title="Include Downstream")] = False
    include_future: Annotated[bool | None, Field(title="Include Future")] = False
    include_past: Annotated[bool | None, Field(title="Include Past")] = False


class Value(RootModel[list]):
    root: Annotated[list, Field(max_length=2, min_length=2, title="Value")]


class ConfigOption(BaseModel):
    """
    Config option.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    key: Annotated[str, Field(title="Key")]
    value: Annotated[str | Value, Field(title="Value")]


class ConfigSection(BaseModel):
    """
    Config Section Schema.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    name: Annotated[str, Field(title="Name")]
    options: Annotated[list[ConfigOption], Field(title="Options")]


class ConnectionBody(BaseModel):
    """
    Connection Serializer for requests body.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    connection_id: Annotated[str, Field(max_length=200, pattern="^[\\w.-]+$", title="Connection Id")]
    conn_type: Annotated[str, Field(title="Conn Type")]
    description: Annotated[str | None, Field(title="Description")] = None
    host: Annotated[str | None, Field(title="Host")] = None
    login: Annotated[str | None, Field(title="Login")] = None
    schema_: Annotated[str | None, Field(alias="schema", title="Schema")] = None
    port: Annotated[int | None, Field(title="Port")] = None
    password: Annotated[str | None, Field(title="Password")] = None
    extra: Annotated[str | None, Field(title="Extra")] = None


class ConnectionResponse(BaseModel):
    """
    Connection serializer for responses.
    """

    connection_id: Annotated[str, Field(title="Connection Id")]
    conn_type: Annotated[str, Field(title="Conn Type")]
    description: Annotated[str | None, Field(title="Description")] = None
    host: Annotated[str | None, Field(title="Host")] = None
    login: Annotated[str | None, Field(title="Login")] = None
    schema_: Annotated[str | None, Field(alias="schema", title="Schema")] = None
    port: Annotated[int | None, Field(title="Port")] = None
    password: Annotated[str | None, Field(title="Password")] = None
    extra: Annotated[str | None, Field(title="Extra")] = None


class ConnectionTestResponse(BaseModel):
    """
    Connection Test serializer for responses.
    """

    status: Annotated[bool, Field(title="Status")]
    message: Annotated[str, Field(title="Message")]


class CreateAssetEventsBody(BaseModel):
    """
    Create asset events request.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    asset_id: Annotated[int, Field(title="Asset Id")]
    extra: Annotated[dict[str, Any] | None, Field(title="Extra")] = None


class DAGPatchBody(BaseModel):
    """
    Dag Serializer for updatable bodies.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    is_paused: Annotated[bool, Field(title="Is Paused")]


class DAGRunClearBody(BaseModel):
    """
    DAG Run serializer for clear endpoint body.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dry_run: Annotated[bool | None, Field(title="Dry Run")] = True
    only_failed: Annotated[bool | None, Field(title="Only Failed")] = False


class Note(RootModel[str]):
    root: Annotated[str, Field(max_length=1000, title="Note")]


class DAGRunPatchStates(str, Enum):
    """
    Enum for DAG Run states when updating a DAG Run.
    """

    QUEUED = "queued"
    SUCCESS = "success"
    FAILED = "failed"


class DAGSourceResponse(BaseModel):
    """
    DAG Source serializer for responses.
    """

    content: Annotated[str | None, Field(title="Content")] = None
    dag_id: Annotated[str, Field(title="Dag Id")]
    version_number: Annotated[int | None, Field(title="Version Number")] = None
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]


class DAGTagCollectionResponse(BaseModel):
    """
    DAG Tags Collection serializer for responses.
    """

    tags: Annotated[list[str], Field(title="Tags")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DagProcessorInfoResponse(BaseModel):
    """
    DagProcessor info serializer for responses.
    """

    status: Annotated[str | None, Field(title="Status")] = None
    latest_dag_processor_heartbeat: Annotated[str | None, Field(title="Latest Dag Processor Heartbeat")] = (
        None
    )


class DagRunAssetReference(BaseModel):
    """
    DAGRun serializer for asset responses.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    run_id: Annotated[str, Field(title="Run Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    start_date: Annotated[datetime, Field(title="Start Date")]
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    state: Annotated[str, Field(title="State")]
    data_interval_start: Annotated[datetime | None, Field(title="Data Interval Start")] = None
    data_interval_end: Annotated[datetime | None, Field(title="Data Interval End")] = None


class DagRunState(str, Enum):
    """
    All possible states that a DagRun can be in.

    These are "shared" with TaskInstanceState in some parts of the code,
    so please ensure that their values always match the ones with the
    same name in TaskInstanceState.
    """

    QUEUED = "queued"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"


class DagRunTriggeredByType(str, Enum):
    """
    Class with TriggeredBy types for DagRun.
    """

    CLI = "cli"
    OPERATOR = "operator"
    REST_API = "rest_api"
    UI = "ui"
    TEST = "test"
    TIMETABLE = "timetable"
    ASSET = "asset"
    BACKFILL = "backfill"


class DagRunType(str, Enum):
    """
    Class with DagRun types.
    """

    BACKFILL = "backfill"
    SCHEDULED = "scheduled"
    MANUAL = "manual"
    ASSET_TRIGGERED = "asset_triggered"


class DagScheduleAssetReference(BaseModel):
    """
    DAG schedule reference serializer for assets.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dag_id: Annotated[str, Field(title="Dag Id")]
    created_at: Annotated[datetime, Field(title="Created At")]
    updated_at: Annotated[datetime, Field(title="Updated At")]


class DagStatsStateResponse(BaseModel):
    """
    DagStatsState serializer for responses.
    """

    state: DagRunState
    count: Annotated[int, Field(title="Count")]


class DagTagResponse(BaseModel):
    """
    DAG Tag serializer for responses.
    """

    name: Annotated[str, Field(title="Name")]
    dag_id: Annotated[str, Field(title="Dag Id")]


class DagVersionResponse(BaseModel):
    """
    Dag Version serializer for responses.
    """

    id: Annotated[UUID, Field(title="Id")]
    version_number: Annotated[int, Field(title="Version Number")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    bundle_name: Annotated[str | None, Field(title="Bundle Name")] = None
    bundle_version: Annotated[str | None, Field(title="Bundle Version")] = None
    created_at: Annotated[datetime, Field(title="Created At")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    bundle_url: Annotated[str | None, Field(title="Bundle Url")] = None


class DagWarningType(str, Enum):
    """
    Enum for DAG warning types.

    This is the set of allowable values for the ``warning_type`` field
    in the DagWarning model.
    """

    ASSET_CONFLICT = "asset conflict"
    NON_EXISTENT_POOL = "non-existent pool"


class DryRunBackfillResponse(BaseModel):
    """
    Backfill serializer for responses in dry-run mode.
    """

    logical_date: Annotated[datetime, Field(title="Logical Date")]


class EventLogResponse(BaseModel):
    """
    Event Log Response.
    """

    event_log_id: Annotated[int, Field(title="Event Log Id")]
    when: Annotated[datetime, Field(title="When")]
    dag_id: Annotated[str | None, Field(title="Dag Id")] = None
    task_id: Annotated[str | None, Field(title="Task Id")] = None
    run_id: Annotated[str | None, Field(title="Run Id")] = None
    map_index: Annotated[int | None, Field(title="Map Index")] = None
    try_number: Annotated[int | None, Field(title="Try Number")] = None
    event: Annotated[str, Field(title="Event")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    owner: Annotated[str | None, Field(title="Owner")] = None
    extra: Annotated[str | None, Field(title="Extra")] = None
    dag_display_name: Annotated[str | None, Field(title="Dag Display Name")] = None


class ExternalLogUrlResponse(BaseModel):
    """
    Response for the external log URL endpoint.
    """

    url: Annotated[str, Field(title="Url")]


class ExtraLinkCollectionResponse(BaseModel):
    """
    Extra Links Response.
    """

    extra_links: Annotated[dict[str, str | None], Field(title="Extra Links")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class FastAPIAppResponse(BaseModel):
    """
    Serializer for Plugin FastAPI App responses.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    app: Annotated[str, Field(title="App")]
    url_prefix: Annotated[str, Field(title="Url Prefix")]
    name: Annotated[str, Field(title="Name")]


class FastAPIRootMiddlewareResponse(BaseModel):
    """
    Serializer for Plugin FastAPI root middleware responses.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    middleware: Annotated[str, Field(title="Middleware")]
    name: Annotated[str, Field(title="Name")]


class HTTPExceptionResponse(BaseModel):
    """
    HTTPException Model used for error response.
    """

    detail: Annotated[str | dict[str, Any], Field(title="Detail")]


class ImportErrorResponse(BaseModel):
    """
    Import Error Response.
    """

    import_error_id: Annotated[int, Field(title="Import Error Id")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]
    filename: Annotated[str, Field(title="Filename")]
    bundle_name: Annotated[str | None, Field(title="Bundle Name")] = None
    stack_trace: Annotated[str, Field(title="Stack Trace")]


class JobResponse(BaseModel):
    """
    Job serializer for responses.
    """

    id: Annotated[int, Field(title="Id")]
    dag_id: Annotated[str | None, Field(title="Dag Id")] = None
    state: Annotated[str | None, Field(title="State")] = None
    job_type: Annotated[str | None, Field(title="Job Type")] = None
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    latest_heartbeat: Annotated[datetime | None, Field(title="Latest Heartbeat")] = None
    executor_class: Annotated[str | None, Field(title="Executor Class")] = None
    hostname: Annotated[str | None, Field(title="Hostname")] = None
    unixname: Annotated[str | None, Field(title="Unixname")] = None
    dag_display_name: Annotated[str | None, Field(title="Dag Display Name")] = None


class JsonValue(RootModel[Any]):
    root: Any


class Id(RootModel[int]):
    root: Annotated[int, Field(ge=0, title="Id")]


class LastAssetEventResponse(BaseModel):
    """
    Last asset event response serializer.
    """

    id: Annotated[Id | None, Field(title="Id")] = None
    timestamp: Annotated[datetime | None, Field(title="Timestamp")] = None


class PluginImportErrorResponse(BaseModel):
    """
    Plugin Import Error serializer for responses.
    """

    source: Annotated[str, Field(title="Source")]
    error: Annotated[str, Field(title="Error")]


class PluginResponse(BaseModel):
    """
    Plugin serializer.
    """

    name: Annotated[str, Field(title="Name")]
    macros: Annotated[list[str], Field(title="Macros")]
    flask_blueprints: Annotated[list[str], Field(title="Flask Blueprints")]
    fastapi_apps: Annotated[list[FastAPIAppResponse], Field(title="Fastapi Apps")]
    fastapi_root_middlewares: Annotated[
        list[FastAPIRootMiddlewareResponse], Field(title="Fastapi Root Middlewares")
    ]
    appbuilder_views: Annotated[list[AppBuilderViewResponse], Field(title="Appbuilder Views")]
    appbuilder_menu_items: Annotated[list[AppBuilderMenuItemResponse], Field(title="Appbuilder Menu Items")]
    global_operator_extra_links: Annotated[list[str], Field(title="Global Operator Extra Links")]
    operator_extra_links: Annotated[list[str], Field(title="Operator Extra Links")]
    source: Annotated[str, Field(title="Source")]
    listeners: Annotated[list[str], Field(title="Listeners")]
    timetables: Annotated[list[str], Field(title="Timetables")]


class PoolBody(BaseModel):
    """
    Pool serializer for post bodies.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    name: Annotated[str, Field(max_length=256, title="Name")]
    slots: Annotated[int, Field(title="Slots")]
    description: Annotated[str | None, Field(title="Description")] = None
    include_deferred: Annotated[bool | None, Field(title="Include Deferred")] = False


class PoolPatchBody(BaseModel):
    """
    Pool serializer for patch bodies.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    pool: Annotated[str | None, Field(title="Pool")] = None
    slots: Annotated[int | None, Field(title="Slots")] = None
    description: Annotated[str | None, Field(title="Description")] = None
    include_deferred: Annotated[bool | None, Field(title="Include Deferred")] = None


class PoolResponse(BaseModel):
    """
    Pool serializer for responses.
    """

    name: Annotated[str, Field(title="Name")]
    slots: Annotated[int, Field(title="Slots")]
    description: Annotated[str | None, Field(title="Description")] = None
    include_deferred: Annotated[bool, Field(title="Include Deferred")]
    occupied_slots: Annotated[int, Field(title="Occupied Slots")]
    running_slots: Annotated[int, Field(title="Running Slots")]
    queued_slots: Annotated[int, Field(title="Queued Slots")]
    scheduled_slots: Annotated[int, Field(title="Scheduled Slots")]
    open_slots: Annotated[int, Field(title="Open Slots")]
    deferred_slots: Annotated[int, Field(title="Deferred Slots")]


class ProviderResponse(BaseModel):
    """
    Provider serializer for responses.
    """

    package_name: Annotated[str, Field(title="Package Name")]
    description: Annotated[str, Field(title="Description")]
    version: Annotated[str, Field(title="Version")]


class QueuedEventResponse(BaseModel):
    """
    Queued Event serializer for responses..
    """

    dag_id: Annotated[str, Field(title="Dag Id")]
    asset_id: Annotated[int, Field(title="Asset Id")]
    created_at: Annotated[datetime, Field(title="Created At")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]


class ReprocessBehavior(str, Enum):
    """
    Internal enum for setting reprocess behavior in a backfill.

    :meta private:
    """

    FAILED = "failed"
    COMPLETED = "completed"
    NONE = "none"


class SchedulerInfoResponse(BaseModel):
    """
    Scheduler info serializer for responses.
    """

    status: Annotated[str | None, Field(title="Status")] = None
    latest_scheduler_heartbeat: Annotated[str | None, Field(title="Latest Scheduler Heartbeat")] = None


class StructuredLogMessage(BaseModel):
    """
    An individual log message.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    timestamp: Annotated[datetime | None, Field(title="Timestamp")] = None
    event: Annotated[str, Field(title="Event")]


class TaskDependencyResponse(BaseModel):
    """
    Task Dependency serializer for responses.
    """

    name: Annotated[str, Field(title="Name")]
    reason: Annotated[str, Field(title="Reason")]


class TaskInstanceState(str, Enum):
    """
    All possible states that a Task Instance can be in.

    Note that None is also allowed, so always use this in a type hint with Optional.
    """

    REMOVED = "removed"
    SCHEDULED = "scheduled"
    QUEUED = "queued"
    RUNNING = "running"
    SUCCESS = "success"
    RESTARTING = "restarting"
    FAILED = "failed"
    UP_FOR_RETRY = "up_for_retry"
    UP_FOR_RESCHEDULE = "up_for_reschedule"
    UPSTREAM_FAILED = "upstream_failed"
    SKIPPED = "skipped"
    DEFERRED = "deferred"


class TaskInstancesBatchBody(BaseModel):
    """
    Task Instance body for get batch.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dag_ids: Annotated[list[str] | None, Field(title="Dag Ids")] = None
    dag_run_ids: Annotated[list[str] | None, Field(title="Dag Run Ids")] = None
    task_ids: Annotated[list[str] | None, Field(title="Task Ids")] = None
    state: Annotated[list[TaskInstanceState | None] | None, Field(title="State")] = None
    run_after_gte: Annotated[datetime | None, Field(title="Run After Gte")] = None
    run_after_lte: Annotated[datetime | None, Field(title="Run After Lte")] = None
    logical_date_gte: Annotated[datetime | None, Field(title="Logical Date Gte")] = None
    logical_date_lte: Annotated[datetime | None, Field(title="Logical Date Lte")] = None
    start_date_gte: Annotated[datetime | None, Field(title="Start Date Gte")] = None
    start_date_lte: Annotated[datetime | None, Field(title="Start Date Lte")] = None
    end_date_gte: Annotated[datetime | None, Field(title="End Date Gte")] = None
    end_date_lte: Annotated[datetime | None, Field(title="End Date Lte")] = None
    duration_gte: Annotated[float | None, Field(title="Duration Gte")] = None
    duration_lte: Annotated[float | None, Field(title="Duration Lte")] = None
    pool: Annotated[list[str] | None, Field(title="Pool")] = None
    queue: Annotated[list[str] | None, Field(title="Queue")] = None
    executor: Annotated[list[str] | None, Field(title="Executor")] = None
    page_offset: Annotated[int | None, Field(ge=0, title="Page Offset")] = 0
    page_limit: Annotated[int | None, Field(ge=0, title="Page Limit")] = 100
    order_by: Annotated[str | None, Field(title="Order By")] = None


class TaskInstancesLogResponse(BaseModel):
    """
    Log serializer for responses.
    """

    content: Annotated[list[StructuredLogMessage] | list[str], Field(title="Content")]
    continuation_token: Annotated[str | None, Field(title="Continuation Token")] = None


class TaskOutletAssetReference(BaseModel):
    """
    Task outlet reference serializer for assets.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dag_id: Annotated[str, Field(title="Dag Id")]
    task_id: Annotated[str, Field(title="Task Id")]
    created_at: Annotated[datetime, Field(title="Created At")]
    updated_at: Annotated[datetime, Field(title="Updated At")]


class TimeDelta(BaseModel):
    """
    TimeDelta can be used to interact with datetime.timedelta objects.
    """

    field__type: Annotated[str | None, Field(alias="__type", title="Type")] = "TimeDelta"
    days: Annotated[int, Field(title="Days")]
    seconds: Annotated[int, Field(title="Seconds")]
    microseconds: Annotated[int, Field(title="Microseconds")]


class TriggerDAGRunPostBody(BaseModel):
    """
    Trigger DAG Run Serializer for POST body.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dag_run_id: Annotated[str | None, Field(title="Dag Run Id")] = None
    data_interval_start: Annotated[datetime | None, Field(title="Data Interval Start")] = None
    data_interval_end: Annotated[datetime | None, Field(title="Data Interval End")] = None
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    run_after: Annotated[datetime | None, Field(title="Run After")] = None
    conf: Annotated[dict[str, Any] | None, Field(title="Conf")] = None
    note: Annotated[str | None, Field(title="Note")] = None


class TriggerResponse(BaseModel):
    """
    Trigger serializer for responses.
    """

    id: Annotated[int, Field(title="Id")]
    classpath: Annotated[str, Field(title="Classpath")]
    kwargs: Annotated[str, Field(title="Kwargs")]
    created_date: Annotated[datetime, Field(title="Created Date")]
    triggerer_id: Annotated[int | None, Field(title="Triggerer Id")] = None


class TriggererInfoResponse(BaseModel):
    """
    Triggerer info serializer for responses.
    """

    status: Annotated[str | None, Field(title="Status")] = None
    latest_triggerer_heartbeat: Annotated[str | None, Field(title="Latest Triggerer Heartbeat")] = None


class Type(str, Enum):
    IFRAME = "iframe"
    MODULE = "module"


class UiPluginResponse(BaseModel):
    """
    UI Plugin serializer for responses.
    """

    slug: Annotated[str, Field(title="Slug")]
    label: Annotated[str, Field(title="Label")]
    icon: Annotated[str | None, Field(title="Icon")] = None
    entry: Annotated[str, Field(title="Entry")]
    type: Annotated[Type, Field(title="Type")]
    permissions: Annotated[list[str], Field(title="Permissions")]
    plugin_name: Annotated[str, Field(title="Plugin Name")]


class ValidationError(BaseModel):
    loc: Annotated[list[str | int], Field(title="Location")]
    msg: Annotated[str, Field(title="Message")]
    type: Annotated[str, Field(title="Error Type")]


class VariableBody(BaseModel):
    """
    Variable serializer for bodies.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    key: Annotated[str, Field(max_length=250, title="Key")]
    value: JsonValue
    description: Annotated[str | None, Field(title="Description")] = None


class VariableResponse(BaseModel):
    """
    Variable serializer for responses.
    """

    key: Annotated[str, Field(title="Key")]
    value: Annotated[str, Field(title="Value")]
    description: Annotated[str | None, Field(title="Description")] = None
    is_encrypted: Annotated[bool, Field(title="Is Encrypted")]


class VersionInfo(BaseModel):
    """
    Version information serializer for responses.
    """

    version: Annotated[str, Field(title="Version")]
    git_version: Annotated[str | None, Field(title="Git Version")] = None


class XComCreateBody(BaseModel):
    """
    Payload serializer for creating an XCom entry.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    key: Annotated[str, Field(title="Key")]
    value: Annotated[Any, Field(title="Value")]
    map_index: Annotated[int | None, Field(title="Map Index")] = -1


class XComResponse(BaseModel):
    """
    Serializer for a xcom item.
    """

    key: Annotated[str, Field(title="Key")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    map_index: Annotated[int, Field(title="Map Index")]
    task_id: Annotated[str, Field(title="Task Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    run_id: Annotated[str, Field(title="Run Id")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]


class XComResponseNative(BaseModel):
    """
    XCom response serializer with native return type.
    """

    key: Annotated[str, Field(title="Key")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    map_index: Annotated[int, Field(title="Map Index")]
    task_id: Annotated[str, Field(title="Task Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    run_id: Annotated[str, Field(title="Run Id")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    value: Annotated[Any, Field(title="Value")]


class XComResponseString(BaseModel):
    """
    XCom response serializer with string return type.
    """

    key: Annotated[str, Field(title="Key")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    map_index: Annotated[int, Field(title="Map Index")]
    task_id: Annotated[str, Field(title="Task Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    run_id: Annotated[str, Field(title="Run Id")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    value: Annotated[str | None, Field(title="Value")] = None


class XComUpdateBody(BaseModel):
    """
    Payload serializer for updating an XCom entry.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    value: Annotated[Any, Field(title="Value")]
    map_index: Annotated[int | None, Field(title="Map Index")] = -1


class AssetAliasCollectionResponse(BaseModel):
    """
    Asset alias collection response.
    """

    asset_aliases: Annotated[list[AssetAliasResponse], Field(title="Asset Aliases")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class AssetEventResponse(BaseModel):
    """
    Asset event serializer for responses.
    """

    id: Annotated[int, Field(title="Id")]
    asset_id: Annotated[int, Field(title="Asset Id")]
    uri: Annotated[str | None, Field(title="Uri")] = None
    name: Annotated[str | None, Field(title="Name")] = None
    group: Annotated[str | None, Field(title="Group")] = None
    extra: Annotated[dict[str, Any] | None, Field(title="Extra")] = None
    source_task_id: Annotated[str | None, Field(title="Source Task Id")] = None
    source_dag_id: Annotated[str | None, Field(title="Source Dag Id")] = None
    source_run_id: Annotated[str | None, Field(title="Source Run Id")] = None
    source_map_index: Annotated[int, Field(title="Source Map Index")]
    created_dagruns: Annotated[list[DagRunAssetReference], Field(title="Created Dagruns")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]


class AssetResponse(BaseModel):
    """
    Asset serializer for responses.
    """

    id: Annotated[int, Field(title="Id")]
    name: Annotated[str, Field(title="Name")]
    uri: Annotated[str, Field(title="Uri")]
    group: Annotated[str, Field(title="Group")]
    extra: Annotated[dict[str, Any] | None, Field(title="Extra")] = None
    created_at: Annotated[datetime, Field(title="Created At")]
    updated_at: Annotated[datetime, Field(title="Updated At")]
    consuming_dags: Annotated[list[DagScheduleAssetReference], Field(title="Consuming Dags")]
    producing_tasks: Annotated[list[TaskOutletAssetReference], Field(title="Producing Tasks")]
    aliases: Annotated[list[AssetAliasResponse], Field(title="Aliases")]
    last_asset_event: LastAssetEventResponse | None = None


class BackfillPostBody(BaseModel):
    """
    Object used for create backfill request.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    dag_id: Annotated[str, Field(title="Dag Id")]
    from_date: Annotated[datetime, Field(title="From Date")]
    to_date: Annotated[datetime, Field(title="To Date")]
    run_backwards: Annotated[bool | None, Field(title="Run Backwards")] = False
    dag_run_conf: Annotated[dict[str, Any] | None, Field(title="Dag Run Conf")] = {}
    reprocess_behavior: ReprocessBehavior | None = "none"
    max_active_runs: Annotated[int | None, Field(title="Max Active Runs")] = 10


class BackfillResponse(BaseModel):
    """
    Base serializer for Backfill.
    """

    id: Annotated[int, Field(ge=0, title="Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    from_date: Annotated[datetime, Field(title="From Date")]
    to_date: Annotated[datetime, Field(title="To Date")]
    dag_run_conf: Annotated[dict[str, Any], Field(title="Dag Run Conf")]
    is_paused: Annotated[bool, Field(title="Is Paused")]
    reprocess_behavior: ReprocessBehavior
    max_active_runs: Annotated[int, Field(title="Max Active Runs")]
    created_at: Annotated[datetime, Field(title="Created At")]
    completed_at: Annotated[datetime | None, Field(title="Completed At")] = None
    updated_at: Annotated[datetime, Field(title="Updated At")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]


class BulkCreateActionConnectionBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[ConnectionBody], Field(description="A list of entities to be created.", title="Entities")
    ]
    action_on_existence: BulkActionOnExistence | None = "fail"


class BulkCreateActionPoolBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[PoolBody], Field(description="A list of entities to be created.", title="Entities")
    ]
    action_on_existence: BulkActionOnExistence | None = "fail"


class BulkCreateActionVariableBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[VariableBody], Field(description="A list of entities to be created.", title="Entities")
    ]
    action_on_existence: BulkActionOnExistence | None = "fail"


class BulkUpdateActionConnectionBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[ConnectionBody], Field(description="A list of entities to be updated.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class BulkUpdateActionPoolBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[PoolBody], Field(description="A list of entities to be updated.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class BulkUpdateActionVariableBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    action: Annotated[BulkAction, Field(description="The action to be performed on the entities.")]
    entities: Annotated[
        list[VariableBody], Field(description="A list of entities to be updated.", title="Entities")
    ]
    action_on_non_existence: BulkActionNotOnExistence | None = "fail"


class Config(BaseModel):
    """
    List of config sections with their options.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    sections: Annotated[list[ConfigSection], Field(title="Sections")]


class ConnectionCollectionResponse(BaseModel):
    """
    Connection Collection serializer for responses.
    """

    connections: Annotated[list[ConnectionResponse], Field(title="Connections")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DAGDetailsResponse(BaseModel):
    """
    Specific serializer for DAG Details responses.
    """

    dag_id: Annotated[str, Field(title="Dag Id")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    is_paused: Annotated[bool, Field(title="Is Paused")]
    is_stale: Annotated[bool, Field(title="Is Stale")]
    last_parsed_time: Annotated[datetime | None, Field(title="Last Parsed Time")] = None
    last_expired: Annotated[datetime | None, Field(title="Last Expired")] = None
    bundle_name: Annotated[str | None, Field(title="Bundle Name")] = None
    bundle_version: Annotated[str | None, Field(title="Bundle Version")] = None
    relative_fileloc: Annotated[str | None, Field(title="Relative Fileloc")] = None
    fileloc: Annotated[str, Field(title="Fileloc")]
    description: Annotated[str | None, Field(title="Description")] = None
    timetable_summary: Annotated[str | None, Field(title="Timetable Summary")] = None
    timetable_description: Annotated[str | None, Field(title="Timetable Description")] = None
    tags: Annotated[list[DagTagResponse], Field(title="Tags")]
    max_active_tasks: Annotated[int, Field(title="Max Active Tasks")]
    max_active_runs: Annotated[int | None, Field(title="Max Active Runs")] = None
    max_consecutive_failed_dag_runs: Annotated[int, Field(title="Max Consecutive Failed Dag Runs")]
    has_task_concurrency_limits: Annotated[bool, Field(title="Has Task Concurrency Limits")]
    has_import_errors: Annotated[bool, Field(title="Has Import Errors")]
    next_dagrun_logical_date: Annotated[datetime | None, Field(title="Next Dagrun Logical Date")] = None
    next_dagrun_data_interval_start: Annotated[
        datetime | None, Field(title="Next Dagrun Data Interval Start")
    ] = None
    next_dagrun_data_interval_end: Annotated[
        datetime | None, Field(title="Next Dagrun Data Interval End")
    ] = None
    next_dagrun_run_after: Annotated[datetime | None, Field(title="Next Dagrun Run After")] = None
    owners: Annotated[list[str], Field(title="Owners")]
    catchup: Annotated[bool, Field(title="Catchup")]
    dag_run_timeout: Annotated[timedelta | None, Field(title="Dag Run Timeout")] = None
    asset_expression: Annotated[dict[str, Any] | None, Field(title="Asset Expression")] = None
    doc_md: Annotated[str | None, Field(title="Doc Md")] = None
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    is_paused_upon_creation: Annotated[bool | None, Field(title="Is Paused Upon Creation")] = None
    params: Annotated[dict[str, Any] | None, Field(title="Params")] = None
    render_template_as_native_obj: Annotated[bool, Field(title="Render Template As Native Obj")]
    template_search_path: Annotated[list[str] | None, Field(title="Template Search Path")] = None
    timezone: Annotated[str | None, Field(title="Timezone")] = None
    last_parsed: Annotated[datetime | None, Field(title="Last Parsed")] = None
    default_args: Annotated[dict[str, Any] | None, Field(title="Default Args")] = None
    owner_links: Annotated[dict[str, str] | None, Field(title="Owner Links")] = None
    file_token: Annotated[str, Field(description="Return file token.", title="File Token")]
    concurrency: Annotated[
        int, Field(description="Return max_active_tasks as concurrency.", title="Concurrency")
    ]
    latest_dag_version: Annotated[
        DagVersionResponse | None, Field(description="Return the latest DagVersion.")
    ] = None


class DAGResponse(BaseModel):
    """
    DAG serializer for responses.
    """

    dag_id: Annotated[str, Field(title="Dag Id")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    is_paused: Annotated[bool, Field(title="Is Paused")]
    is_stale: Annotated[bool, Field(title="Is Stale")]
    last_parsed_time: Annotated[datetime | None, Field(title="Last Parsed Time")] = None
    last_expired: Annotated[datetime | None, Field(title="Last Expired")] = None
    bundle_name: Annotated[str | None, Field(title="Bundle Name")] = None
    bundle_version: Annotated[str | None, Field(title="Bundle Version")] = None
    relative_fileloc: Annotated[str | None, Field(title="Relative Fileloc")] = None
    fileloc: Annotated[str, Field(title="Fileloc")]
    description: Annotated[str | None, Field(title="Description")] = None
    timetable_summary: Annotated[str | None, Field(title="Timetable Summary")] = None
    timetable_description: Annotated[str | None, Field(title="Timetable Description")] = None
    tags: Annotated[list[DagTagResponse], Field(title="Tags")]
    max_active_tasks: Annotated[int, Field(title="Max Active Tasks")]
    max_active_runs: Annotated[int | None, Field(title="Max Active Runs")] = None
    max_consecutive_failed_dag_runs: Annotated[int, Field(title="Max Consecutive Failed Dag Runs")]
    has_task_concurrency_limits: Annotated[bool, Field(title="Has Task Concurrency Limits")]
    has_import_errors: Annotated[bool, Field(title="Has Import Errors")]
    next_dagrun_logical_date: Annotated[datetime | None, Field(title="Next Dagrun Logical Date")] = None
    next_dagrun_data_interval_start: Annotated[
        datetime | None, Field(title="Next Dagrun Data Interval Start")
    ] = None
    next_dagrun_data_interval_end: Annotated[
        datetime | None, Field(title="Next Dagrun Data Interval End")
    ] = None
    next_dagrun_run_after: Annotated[datetime | None, Field(title="Next Dagrun Run After")] = None
    owners: Annotated[list[str], Field(title="Owners")]
    file_token: Annotated[str, Field(description="Return file token.", title="File Token")]


class DAGRunPatchBody(BaseModel):
    """
    DAG Run Serializer for PATCH requests.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    state: DAGRunPatchStates | None = None
    note: Annotated[Note | None, Field(title="Note")] = None


class DAGRunResponse(BaseModel):
    """
    DAG Run serializer for responses.
    """

    dag_run_id: Annotated[str, Field(title="Dag Run Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    queued_at: Annotated[datetime | None, Field(title="Queued At")] = None
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    data_interval_start: Annotated[datetime | None, Field(title="Data Interval Start")] = None
    data_interval_end: Annotated[datetime | None, Field(title="Data Interval End")] = None
    run_after: Annotated[datetime, Field(title="Run After")]
    last_scheduling_decision: Annotated[datetime | None, Field(title="Last Scheduling Decision")] = None
    run_type: DagRunType
    state: DagRunState
    triggered_by: DagRunTriggeredByType | None = None
    conf: Annotated[dict[str, Any] | None, Field(title="Conf")] = None
    note: Annotated[str | None, Field(title="Note")] = None
    dag_versions: Annotated[list[DagVersionResponse], Field(title="Dag Versions")]
    bundle_version: Annotated[str | None, Field(title="Bundle Version")] = None
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]


class DAGRunsBatchBody(BaseModel):
    """
    List DAG Runs body for batch endpoint.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    order_by: Annotated[str | None, Field(title="Order By")] = None
    page_offset: Annotated[int | None, Field(ge=0, title="Page Offset")] = 0
    page_limit: Annotated[int | None, Field(ge=0, title="Page Limit")] = 100
    dag_ids: Annotated[list[str] | None, Field(title="Dag Ids")] = None
    states: Annotated[list[DagRunState | None] | None, Field(title="States")] = None
    run_after_gte: Annotated[datetime | None, Field(title="Run After Gte")] = None
    run_after_lte: Annotated[datetime | None, Field(title="Run After Lte")] = None
    logical_date_gte: Annotated[datetime | None, Field(title="Logical Date Gte")] = None
    logical_date_lte: Annotated[datetime | None, Field(title="Logical Date Lte")] = None
    start_date_gte: Annotated[datetime | None, Field(title="Start Date Gte")] = None
    start_date_lte: Annotated[datetime | None, Field(title="Start Date Lte")] = None
    end_date_gte: Annotated[datetime | None, Field(title="End Date Gte")] = None
    end_date_lte: Annotated[datetime | None, Field(title="End Date Lte")] = None


class DAGVersionCollectionResponse(BaseModel):
    """
    DAG Version Collection serializer for responses.
    """

    dag_versions: Annotated[list[DagVersionResponse], Field(title="Dag Versions")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DAGWarningResponse(BaseModel):
    """
    DAG Warning serializer for responses.
    """

    dag_id: Annotated[str, Field(title="Dag Id")]
    warning_type: DagWarningType
    message: Annotated[str, Field(title="Message")]
    timestamp: Annotated[datetime, Field(title="Timestamp")]


class DagStatsResponse(BaseModel):
    """
    DAG Stats serializer for responses.
    """

    dag_id: Annotated[str, Field(title="Dag Id")]
    stats: Annotated[list[DagStatsStateResponse], Field(title="Stats")]


class DryRunBackfillCollectionResponse(BaseModel):
    """
    Backfill collection serializer for responses in dry-run mode.
    """

    backfills: Annotated[list[DryRunBackfillResponse], Field(title="Backfills")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class EventLogCollectionResponse(BaseModel):
    """
    Event Log Collection Response.
    """

    event_logs: Annotated[list[EventLogResponse], Field(title="Event Logs")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class HTTPValidationError(BaseModel):
    detail: Annotated[list[ValidationError] | None, Field(title="Detail")] = None


class HealthInfoResponse(BaseModel):
    """
    Health serializer for responses.
    """

    metadatabase: BaseInfoResponse
    scheduler: SchedulerInfoResponse
    triggerer: TriggererInfoResponse
    dag_processor: DagProcessorInfoResponse | None = None


class ImportErrorCollectionResponse(BaseModel):
    """
    Import Error Collection Response.
    """

    import_errors: Annotated[list[ImportErrorResponse], Field(title="Import Errors")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class JobCollectionResponse(BaseModel):
    """
    Job Collection Response.
    """

    jobs: Annotated[list[JobResponse], Field(title="Jobs")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class PatchTaskInstanceBody(BaseModel):
    """
    Request body for Clear Task Instances endpoint.
    """

    model_config = ConfigDict(
        extra="forbid",
    )
    new_state: TaskInstanceState | None = None
    note: Annotated[Note | None, Field(title="Note")] = None
    include_upstream: Annotated[bool | None, Field(title="Include Upstream")] = False
    include_downstream: Annotated[bool | None, Field(title="Include Downstream")] = False
    include_future: Annotated[bool | None, Field(title="Include Future")] = False
    include_past: Annotated[bool | None, Field(title="Include Past")] = False


class PluginCollectionResponse(BaseModel):
    """
    Plugin Collection serializer.
    """

    plugins: Annotated[list[PluginResponse], Field(title="Plugins")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class PluginImportErrorCollectionResponse(BaseModel):
    """
    Plugin Import Error Collection serializer.
    """

    import_errors: Annotated[list[PluginImportErrorResponse], Field(title="Import Errors")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class PoolCollectionResponse(BaseModel):
    """
    Pool Collection serializer for responses.
    """

    pools: Annotated[list[PoolResponse], Field(title="Pools")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class ProviderCollectionResponse(BaseModel):
    """
    Provider Collection serializer for responses.
    """

    providers: Annotated[list[ProviderResponse], Field(title="Providers")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class QueuedEventCollectionResponse(BaseModel):
    """
    Queued Event Collection serializer for responses.
    """

    queued_events: Annotated[list[QueuedEventResponse], Field(title="Queued Events")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class TaskDependencyCollectionResponse(BaseModel):
    """
    Task scheduling dependencies collection serializer for responses.
    """

    dependencies: Annotated[list[TaskDependencyResponse], Field(title="Dependencies")]


class TaskInstanceHistoryResponse(BaseModel):
    """
    TaskInstanceHistory serializer for responses.
    """

    task_id: Annotated[str, Field(title="Task Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    dag_run_id: Annotated[str, Field(title="Dag Run Id")]
    map_index: Annotated[int, Field(title="Map Index")]
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    duration: Annotated[float | None, Field(title="Duration")] = None
    state: TaskInstanceState | None = None
    try_number: Annotated[int, Field(title="Try Number")]
    max_tries: Annotated[int, Field(title="Max Tries")]
    task_display_name: Annotated[str, Field(title="Task Display Name")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    hostname: Annotated[str | None, Field(title="Hostname")] = None
    unixname: Annotated[str | None, Field(title="Unixname")] = None
    pool: Annotated[str, Field(title="Pool")]
    pool_slots: Annotated[int, Field(title="Pool Slots")]
    queue: Annotated[str | None, Field(title="Queue")] = None
    priority_weight: Annotated[int | None, Field(title="Priority Weight")] = None
    operator: Annotated[str | None, Field(title="Operator")] = None
    queued_when: Annotated[datetime | None, Field(title="Queued When")] = None
    scheduled_when: Annotated[datetime | None, Field(title="Scheduled When")] = None
    pid: Annotated[int | None, Field(title="Pid")] = None
    executor: Annotated[str | None, Field(title="Executor")] = None
    executor_config: Annotated[str, Field(title="Executor Config")]
    dag_version: DagVersionResponse | None = None


class TaskInstanceResponse(BaseModel):
    """
    TaskInstance serializer for responses.
    """

    id: Annotated[str, Field(title="Id")]
    task_id: Annotated[str, Field(title="Task Id")]
    dag_id: Annotated[str, Field(title="Dag Id")]
    dag_run_id: Annotated[str, Field(title="Dag Run Id")]
    map_index: Annotated[int, Field(title="Map Index")]
    logical_date: Annotated[datetime | None, Field(title="Logical Date")] = None
    run_after: Annotated[datetime, Field(title="Run After")]
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    duration: Annotated[float | None, Field(title="Duration")] = None
    state: TaskInstanceState | None = None
    try_number: Annotated[int, Field(title="Try Number")]
    max_tries: Annotated[int, Field(title="Max Tries")]
    task_display_name: Annotated[str, Field(title="Task Display Name")]
    dag_display_name: Annotated[str, Field(title="Dag Display Name")]
    hostname: Annotated[str | None, Field(title="Hostname")] = None
    unixname: Annotated[str | None, Field(title="Unixname")] = None
    pool: Annotated[str, Field(title="Pool")]
    pool_slots: Annotated[int, Field(title="Pool Slots")]
    queue: Annotated[str | None, Field(title="Queue")] = None
    priority_weight: Annotated[int | None, Field(title="Priority Weight")] = None
    operator: Annotated[str | None, Field(title="Operator")] = None
    queued_when: Annotated[datetime | None, Field(title="Queued When")] = None
    scheduled_when: Annotated[datetime | None, Field(title="Scheduled When")] = None
    pid: Annotated[int | None, Field(title="Pid")] = None
    executor: Annotated[str | None, Field(title="Executor")] = None
    executor_config: Annotated[str, Field(title="Executor Config")]
    note: Annotated[str | None, Field(title="Note")] = None
    rendered_map_index: Annotated[str | None, Field(title="Rendered Map Index")] = None
    rendered_fields: Annotated[dict[str, Any] | None, Field(title="Rendered Fields")] = None
    trigger: TriggerResponse | None = None
    triggerer_job: JobResponse | None = None
    dag_version: DagVersionResponse | None = None


class TaskResponse(BaseModel):
    """
    Task serializer for responses.
    """

    task_id: Annotated[str | None, Field(title="Task Id")] = None
    task_display_name: Annotated[str | None, Field(title="Task Display Name")] = None
    owner: Annotated[str | None, Field(title="Owner")] = None
    start_date: Annotated[datetime | None, Field(title="Start Date")] = None
    end_date: Annotated[datetime | None, Field(title="End Date")] = None
    trigger_rule: Annotated[str | None, Field(title="Trigger Rule")] = None
    depends_on_past: Annotated[bool, Field(title="Depends On Past")]
    wait_for_downstream: Annotated[bool, Field(title="Wait For Downstream")]
    retries: Annotated[float | None, Field(title="Retries")] = None
    queue: Annotated[str | None, Field(title="Queue")] = None
    pool: Annotated[str | None, Field(title="Pool")] = None
    pool_slots: Annotated[float | None, Field(title="Pool Slots")] = None
    execution_timeout: TimeDelta | None = None
    retry_delay: TimeDelta | None = None
    retry_exponential_backoff: Annotated[bool, Field(title="Retry Exponential Backoff")]
    priority_weight: Annotated[float | None, Field(title="Priority Weight")] = None
    weight_rule: Annotated[str | None, Field(title="Weight Rule")] = None
    ui_color: Annotated[str | None, Field(title="Ui Color")] = None
    ui_fgcolor: Annotated[str | None, Field(title="Ui Fgcolor")] = None
    template_fields: Annotated[list[str] | None, Field(title="Template Fields")] = None
    downstream_task_ids: Annotated[list[str] | None, Field(title="Downstream Task Ids")] = None
    doc_md: Annotated[str | None, Field(title="Doc Md")] = None
    operator_name: Annotated[str | None, Field(title="Operator Name")] = None
    params: Annotated[dict[str, Any] | None, Field(title="Params")] = None
    class_ref: Annotated[dict[str, Any] | None, Field(title="Class Ref")] = None
    is_mapped: Annotated[bool | None, Field(title="Is Mapped")] = None
    extra_links: Annotated[
        list[str], Field(description="Extract and return extra_links.", title="Extra Links")
    ]


class UiPluginCollectionResponse(BaseModel):
    """
    UI Plugin Collection serializer.
    """

    plugins: Annotated[list[UiPluginResponse], Field(title="Plugins")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class VariableCollectionResponse(BaseModel):
    """
    Variable Collection serializer for responses.
    """

    variables: Annotated[list[VariableResponse], Field(title="Variables")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class XComCollectionResponse(BaseModel):
    """
    XCom Collection serializer for responses.
    """

    xcom_entries: Annotated[list[XComResponse], Field(title="Xcom Entries")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class AssetCollectionResponse(BaseModel):
    """
    Asset collection response.
    """

    assets: Annotated[list[AssetResponse], Field(title="Assets")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class AssetEventCollectionResponse(BaseModel):
    """
    Asset event collection response.
    """

    asset_events: Annotated[list[AssetEventResponse], Field(title="Asset Events")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class BackfillCollectionResponse(BaseModel):
    """
    Backfill Collection serializer for responses.
    """

    backfills: Annotated[list[BackfillResponse], Field(title="Backfills")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class BulkBodyConnectionBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    actions: Annotated[
        list[
            BulkCreateActionConnectionBody | BulkUpdateActionConnectionBody | BulkDeleteActionConnectionBody
        ],
        Field(title="Actions"),
    ]


class BulkBodyPoolBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    actions: Annotated[
        list[BulkCreateActionPoolBody | BulkUpdateActionPoolBody | BulkDeleteActionPoolBody],
        Field(title="Actions"),
    ]


class BulkBodyVariableBody(BaseModel):
    model_config = ConfigDict(
        extra="forbid",
    )
    actions: Annotated[
        list[BulkCreateActionVariableBody | BulkUpdateActionVariableBody | BulkDeleteActionVariableBody],
        Field(title="Actions"),
    ]


class DAGCollectionResponse(BaseModel):
    """
    DAG Collection serializer for responses.
    """

    dags: Annotated[list[DAGResponse], Field(title="Dags")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DAGRunCollectionResponse(BaseModel):
    """
    DAG Run Collection serializer for responses.
    """

    dag_runs: Annotated[list[DAGRunResponse], Field(title="Dag Runs")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DAGWarningCollectionResponse(BaseModel):
    """
    DAG warning collection serializer for responses.
    """

    dag_warnings: Annotated[list[DAGWarningResponse], Field(title="Dag Warnings")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class DagStatsCollectionResponse(BaseModel):
    """
    DAG Stats Collection serializer for responses.
    """

    dags: Annotated[list[DagStatsResponse], Field(title="Dags")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class TaskCollectionResponse(BaseModel):
    """
    Task collection serializer for responses.
    """

    tasks: Annotated[list[TaskResponse], Field(title="Tasks")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class TaskInstanceCollectionResponse(BaseModel):
    """
    Task Instance Collection serializer for responses.
    """

    task_instances: Annotated[list[TaskInstanceResponse], Field(title="Task Instances")]
    total_entries: Annotated[int, Field(title="Total Entries")]


class TaskInstanceHistoryCollectionResponse(BaseModel):
    """
    TaskInstanceHistory Collection serializer for responses.
    """

    task_instances: Annotated[list[TaskInstanceHistoryResponse], Field(title="Task Instances")]
    total_entries: Annotated[int, Field(title="Total Entries")]
